[{"type": "text", "text": "访问https://pdfs.top，查看更多海量书籍", "page_idx": 0}, {"type": "text", "text": "证券研究报告·行业动态", "page_idx": 0}, {"type": "text", "text": "DeepSeek 核心十问十答 ", "text_level": 1, "page_idx": 0}, {"type": "text", "text": "计算机", "text_level": 1, "page_idx": 0}, {"type": "text", "text": "核心观点", "text_level": 1, "page_idx": 0}, {"type": "text", "text": "DeepSeek-R1 模型发布，具有高性能、低算力需求的特性，带动小模型推理能力的提升，引发全球开发者及用户关注。R1 作为开源模型性能接近头部闭源模型 o1，一定程度上已经反映了 AI 平权，同时纯强化学习对推理能力的提升带来 RL 范式泛化可能，预计后续基模的持续迭代，有望推动 AI 全产业链持续保持高景气和高关注度，关注算力、应用、端侧、数据等核心投资机会。", "page_idx": 0}, {"type": "text", "text": "行业动态信息", "text_level": 1, "page_idx": 0}, {"type": "text", "text": "DeepSeek 模型密集更新，高性能 $+$ 低成本促进用户数高增", "text_level": 1, "page_idx": 0}, {"type": "text", "text": "近期DeepSeek 多款模型上线并完全开源，其中 R1 在推理任务上基本实现于o1 相当的性能，Janus-Pro 在多模态理解和生成方面表现较好。受春节信息传播下沉促进，DeepSeek 出圈并成为全球增速最快的 AI 原生应用，第 18 天达到 1500 万日活。此外，DeepSeek 通过算法迭代、架构升级，使通用及推理模型成本相较于OpenAI 同类模型下降至数十分之一以下。", "page_idx": 0}, {"type": "text", "text": "技术不断革新，大模型Scaling Law 仍有效", "text_level": 1, "page_idx": 0}, {"type": "text", "text": "DeepSeek 通过多头潜在注意力、MoE、多token 预测等架构和基础设施创新实现了高效训练，并在R1-Zero 模型验证了纯强化学习对推理能力的提升。尽管 Pre-Training Scaling 面临技术、算力、数据的制约，但强化学习带来了规模化扩张新方向，预计各厂商将陆续跟进，持续优化模型架构。", "page_idx": 0}, {"type": "text", "text": "DeepSeek-R1 促进 AI 平权，产业链享受发展红利", "text_level": 1, "page_idx": 0}, {"type": "text", "text": "R1 作为开源模型性能接近头部闭源模型 o1，一定程度上已经反映了 AI 平权。同时，R1 使小模型具备推理能力成为可能，更低的成本将更有利于开发者探索AI 的实际落地。", "page_idx": 0}, {"type": "text", "text": "投资建议：1）算力：算力建议关注以国产算力和AI 推理需求为核心的算力环节，尤其是IDC、服务器、国产芯片等算力配套产业，推荐海光信息、浪潮信息、软通动力，并关注AIDC相关标的，如并行科技、宝信软件等；2）应用：B 端推荐金蝶国际、鼎捷数智、赛意信息、用友网络、恒生电子、中控技术等；C 端推荐金山办公、万兴科技、彩讯股份、同花顺等；3)端侧：教育推荐视源股份、科大讯飞等；其次新终端推荐虹软科技、联想集团等；4）数据：建议关注向量数据库、数据处理类企业，以及具备行业侧专业数据的厂商，关注拓尔思等。", "page_idx": 0}, {"type": "text", "text": "维持", "page_idx": 0}, {"type": "text", "text": "强于大市应瑛********************** 编号:S1440521100010发布日期： 2025 年 02 月 04 日", "page_idx": 0}, {"type": "text", "text": "", "page_idx": 0}, {"type": "text", "text": "", "page_idx": 0}, {"type": "image", "img_path": "images/66cf69c01036f2d0b0360a11b022d073e6f11a0510bce5f62432bc9cc27a2c61.jpg", "img_caption": ["市场表现"], "img_footnote": [], "page_idx": 0}, {"type": "table", "img_path": "images/60412f7ae69509809963bea0318e986741533e4abd5ee5539b4fac4632a39ee0.jpg", "table_caption": ["相关研究报告"], "table_footnote": [], "table_body": "<html><body><table><tr><td>25.02.03</td><td>【中信建投人工智能】：DeepSeekR1 深度解析及算力影响几何</td></tr><tr><td>25.01.26</td><td>【中信建投计算机】：周报25年第3 期：计算机板块连续四季度低配， Agent加速落地 【中信建投计算机】：低空经济系列</td></tr><tr><td>25.01.20</td><td>报告5：空管系统—一低空经济核心 环节之一 【中信建投人工智能】：智元推出首</td></tr><tr><td>25.01.20</td><td>个4D 世界模型，OpenAI重点布局 机器人</td></tr><tr><td>25.01.19</td><td>【中信建投计算机】：周报25年第2 期：AI应用加速进行时</td></tr></table></body></html>", "page_idx": 0}, {"type": "text", "text": "目录", "text_level": 1, "page_idx": 1}, {"type": "text", "text": "", "page_idx": 1}, {"type": "text", "text": "、DeepSeek 模型密集更新，高性能 $^ +$ 低成本促进用户数高增 ..1.1 第一问：DeepSeek 的用户量趋势？1.2 第二问：R1 和 Janus-pro 模型的性能如何？1.3 第三问：如何看待 DeepSeek-V3 模型的训练成本？ 5  \n二、技术不断革新，大模型 Scaling Law 仍有效 ... 72.1 第四问：DeepSeek-V3/R1 技术革新有哪些？2.2 第五问：Janus 系列模型技术革新有哪些？ .122.3 第六问：DeepSeek 数据集的特点是什么？ ..132.3 第七问：Scaling Law 到底是否有效？ ... 14  \n三、DeepSeek-R1 促进 AI 平权，产业链享受发展红利 . ..163.1 第八问：R1 是否意味着AI 平权已经实现？ ..163.2 第九问：DeepSeek 出圈对产业的影响有几何？ ..19  \n四、投资建议... .. 224.1 第十问：DeepSeek 将带来哪些投资机会？ 22  \n风险分析. 23", "page_idx": 1}, {"type": "text", "text": "一、DeepSeek 模型密集更新，高性能 $^ +$ 低成本促进用户数高增", "text_level": 1, "page_idx": 2}, {"type": "text", "text": "1.1 第一问：DeepSeek 的用户量趋势？", "text_level": 1, "page_idx": 2}, {"type": "text", "text": "DeepSeek 坚定开源路线，密集更新 MoE、推理、多模态模型。近期，DeepSeek 连续发布并开源多个大模型，其低成本、高性能的特性迅速引发全球用户的关注。其中，2024 年 12 月26 日发布的 DeepSeek-V3 为 671B参数的自研 MoE 模型，运行时仅需激活37B，在 14.8T token 的数据上进行了预训练；2025 年 1 月 20 日发布的 DeepSeek-R1 为 660B 的高性能推理模型，对用户开放思维链输出，允许用户通过蒸馏技术借助 R1 训练其他模型；2025 年 1 月 27 日，DeepSeek 在 Hugging Face 平台上传了视觉模型 Janus-Pro 和多模态理解模型JanusFlow -1.3B，进一步在图像领域发力。", "page_idx": 2}, {"type": "table", "img_path": "images/095a2e9f52a4146474d4a87289c92a2c5074208324918ff7862c0950fb3519fe.jpg", "table_caption": ["表 1: DeepSeek 关键模型发布时间"], "table_footnote": ["资料来源：DeepSeek 公众号，中信建投"], "table_body": "<html><body><table><tr><td>时间</td><td>模型名称</td><td>模型类型</td><td>简介</td></tr><tr><td>2023年 11月2日</td><td>DeepSeek Coder</td><td>代码大模型</td><td>覆盖1B，7B，33B多个尺寸，包含Base 模型和指令调优模型，帮 助用户进行代码生成、bug修复、数据分析等工作。 包含7B 和67B 的基础模型及指令微调版本，同时开放下载训练中</td></tr><tr><td>2023年 11月29日</td><td>DeepSeek LLM</td><td>通用大模型</td><td>途的9个模型checkpoints，在推理、编码、数学和中文理解等方 面表现出色。</td></tr><tr><td>2024年 1月11日</td><td>DeepSeek MoE</td><td>通用大模型</td><td>基于自研全新MoE 架构，在2B、16B、145B（发布时尚处于早期实 验）三个尺度模型上均实现效果领先，显著优化计算量。</td></tr><tr><td>2024年 2月6日</td><td>DeepSeek Math</td><td>数学推理模型</td><td>以DeepSeek-Coder-V1.5 7B 为基础，在数学相关 token 以及自然 语言和代码数据上进行500B tokens 的预训练，在数学测试中取得 较好表现</td></tr><tr><td>2024年 3月11日</td><td>DeepSeek-VL</td><td>多摸态大模型</td><td>对训练数据、模型架构和训练策略进行联合拓展，其1.3B 和 7B 模 型能高效处理高分辨率图像，捕捉关键语义和细节信息。</td></tr><tr><td>2024年 5月7日</td><td>DeepSeek-V2</td><td>通用大模型</td><td>总参数 236B，每个 token 激活 21B，在 8.1 万亿 token 的语料 库上预训练，支持128K tokens，大幅节省训练成本。</td></tr><tr><td>2024年 6月17日</td><td>DeepSeek-Coder-V2</td><td>通用大模型</td><td>从DeepSeek-V2中间检查点开始，进一步预训练6万亿token， 增强了编码和数学推理能力，包含236B和16B两种参数规模，在编 码和数学基准测试中超越主流闭源模型。</td></tr><tr><td>2024年 12月13日</td><td>DeepSeek-VL2</td><td>多模态大模型</td><td>覆盖3B、16B、27B多个尺寸，通过DeepSeek-MoE 架构配合动态切 图，支持视觉定位、梗图解析、OCR、故事生成等任务。</td></tr><tr><td>2024年 12月26日</td><td>DeepSeek-V3</td><td>通用大模型</td><td>自研 MoE 模型，671B 参数，每个 token 激活 37B，在14.8T token 上进行了预训练，性能对齐海外领军闭源模型。</td></tr><tr><td>2025年 1月20日</td><td>DeepSeek-R1</td><td>推理大模型</td><td>660B 模型，在后训练阶段大规模使用了强化学习技术，在仅有极少 标注数据的情况下，极大提升了模型推理能力；对用户开放思维链 输出，允许用户通过蒸馏技术借助 R1 训练其他模型。</td></tr><tr><td>2025年 1月27日</td><td>Janus-Pro</td><td>多模态大模型</td><td>包括 7B 和1.5B 两个模型版本，采用新颖的自回归框架，统一了多 模态理解和生成，可适配视觉问答、图像标注等多模态场景。</td></tr></table></body></html>", "page_idx": 2}, {"type": "text", "text": "DeepSeek Web端与 APP 端访问量持续增长，春节信息传播下沉加速产品关注度裂变。Web 端，2024 年10月至 2024 年 12 月 DeepSeek 访问量分别为 245/422/1101 万，其中 11 月和 12 月分别同比增长 $7 2 . 2 4 \\% / 1 6 0 . 9 0 \\%$ ，12 月受全新开源模型 V3 促进访问量大幅增长；APP 端，DeepSeek 2025 年 1 月 10 日（官方公众号 1 月 15 日正式发文）在 iOS/Android 上线官方 APP，而后受益于 1 月 20 日发布 R1 模型的高性能、低成本，叠加春节期间信息传播下沉，产品关注度呈裂变式增长。具体而言，DeepSeek APP 安卓/iOS 端国区单日下载量均于 1 月 26日前后迎来陡增，至 1 月 29 日单日下载量分别达到 784.15/29.92 万；同时，DeepSeek 安卓端在华为应用商店下载排行中位列第四，iOS 端则霸榜全球 173 个地区中 160/162/171 个总榜（免费）/应用（免费）/效率（免费）第一；此外，从产品发布日起日活用户看，DeepSeek 第 5 天超过 ChatGPT，第 15 天以 259 万日活达到 ChatGPT的2 倍，亦为全球增速最快的 AI 原生应用，第18 天达到1500 万日活，而 ChatGPT 上线第 244 天才达到 1500万 DAU。", "page_idx": 2}, {"type": "text", "text": "", "page_idx": 3}, {"type": "image", "img_path": "images/d0bea95c82f5749d9fc791069335b7ba3f617ab6238eebb6467670d14556a432.jpg", "img_caption": ["图 1：1 月 15 日以来 DeepSeek APP 安卓端日下载量"], "img_footnote": ["资料来源：七麦数据，中信建投"], "page_idx": 3}, {"type": "image", "img_path": "images/f82ee8795f2e06b99a826715150c7d39155476ad194e43c8de455c9a830f851b.jpg", "img_caption": ["图 2：1 月 15 日以来 DeepSeek APP iOS 端日下载量"], "img_footnote": ["资料来源：七麦数据，中信建投"], "page_idx": 3}, {"type": "image", "img_path": "images/c6224589836fcce83864d2df8a0743d43cf2f971b3f247d4cd59d32d1c03753e.jpg", "img_caption": ["图 3：24 年 10 月-24 年 12 月 DeepSeek（Web端）访问量"], "img_footnote": ["资料来源：AI 产品榜，中信建投"], "page_idx": 3}, {"type": "image", "img_path": "images/a9e6d38b488cb5cccc7693b965d213b91dc37e96ba0f48543e3bd35299e4d008.jpg", "img_caption": ["图 4：DeepSeek（APP）DAU 用户量"], "img_footnote": ["资料来源：AI 产品榜，中信建投"], "page_idx": 3}, {"type": "text", "text": "我们认为，DeepSeek 用户数将持续高速增长。一方面 DeepSeek 作为开源路线的坚定践行者，有望受到全球开发者的高度关注；另一方面受益于春节期间信息传播下沉，DeepSeek 的国内渗透率将持续提升。", "page_idx": 3}, {"type": "text", "text": "1.2 第二问：R1 和 Janus-pro 模型的性能如何？", "text_level": 1, "page_idx": 3}, {"type": "text", "text": "DeepSeek-R1 在推理任务上基本实现与 OpenAI-o1 相当的性能，较 o3 模型仍有差距。DeepSeek 在 R1 模型的测试过程中，选取英文、中文、数学、代码等基准测试，与 Claude-3.5、GPT-4o、DeepSeek-V3、OpenAI o1、OpenAI o1-mini 等模型进行比较：", "page_idx": 3}, {"type": "text", "text": "教育为导向的知识任务：在以 MMLU（R1 90.8 分；V3 88.5 分；o1 91.8 分）和 GPQA Diamond（R1 71.5分；V3 59.1 分；o1 75.7 分；o3 87.7 分）为代表的知识基准上，R1 相比 V3 表现出更优越的性能，主因大规模强化学习（RL）促进STEM 相关问题上准确性显著进步；在依赖长上下文的FRAMES（R1 82.5 分；V3 73.7 分）基准，R1 同样展示了强大的文档分析能力。", "page_idx": 3}, {"type": "text", "text": "", "page_idx": 4}, {"type": "text", "text": "中英文搜索和数据分析任务：在英文事实基准测试 SimpleQA（R1 30.1 分；V3 24.9 分；o1 47.0 分）上，R1优于V3，展现了模型基于事实的查询能力；而在中文事实基准测试 C-SimpleQA（R1 63.7 分；V3 68.0 分）上，R1 表现不如V3，主要系安全强化学习后模型倾向于拒绝回答某些查询。如果没有安全 RL， R1 的准确率可以超过 $70 \\%$ 。此外，R1 模型在 IF-Eval（R1 83.3 分；V3 86.1 分）、AlpacaEval2.0（R1 87.6 分；V3 70.0 分）和ArenaHard（R1 92.3 分；V3 85.5 分）等基准测试中同样表现较好，展现了模型在遵循格式指令、写作任务和开放域问答上的能力。", "page_idx": 4}, {"type": "text", "text": "数学任务：在数学任务上， R1 表现出与 o1 相当的性能，优于其他非推理模型，突出了推理模型在数学测试中的主导地位。例如在 AIME 2024 基准上，R1/V3/o1/o3 分别得分 $7 9 . 8 / 3 9 . 2 / 7 9 . 2 / 9 6 . 7 \\$ 分；在 Math-500 基准上，R1/V3/o1 分别得分 $9 7 . 3 / 9 0 . 2 / 9 6 . 4 \\$ 分。", "page_idx": 4}, {"type": "text", "text": "编码任务：推理模型在数学测试中同样表现更佳，例如在 Codeforces 基准上，R1/V3/o1/o3 分别得分$2 0 2 9 / 1 1 3 4 / 2 0 6 1 / 2 7 2 7$ 分，分别超过 $9 6 . 3 \\% / 5 8 . 7 \\% / 9 6 . 6 \\% / 9 9 . 9 \\%$ 的人类参赛者；在 SWE-bench Verified 基准上，R1/V3/o1/o3 分别得分 $4 9 . 2 / 4 2 . 0 / 4 8 . 9 / 7 1 . 7$ 分。", "page_idx": 4}, {"type": "table", "img_path": "images/55ce5ef72916b991735f0e39ca64821ee32d6e718ac4cb4c0aff55f8a6a83500.jpg", "table_caption": ["图 5: DeepSeek-R1 和其他代表性模型的比较"], "table_footnote": ["数据来源：《DeepSeek-R1: Incentivizing Reasoning Capability in LLMs via Reinforcement Learning》，中信建投 "], "table_body": "<html><body><table><tr><td></td><td>Benchmark (Metric)</td><td>Claude-3.5- Sonnet-1022</td><td>GPT-4o DeepSeek OpenAI OpenAI|DeepSeek 0513</td><td>V3</td><td>ol-mini o1-1217</td><td></td><td>R1</td></tr><tr><td rowspan=\"2\"></td><td>Architecture</td><td></td><td></td><td>MoE</td><td></td><td></td><td>MoE</td></tr><tr><td>#ActivatedParams # Total Params</td><td>■</td><td></td><td>37B</td><td></td><td></td><td>37B 671B</td></tr><tr><td rowspan=\"9\"></td><td></td><td></td><td>■</td><td>671B</td><td>■</td><td>■</td><td></td></tr><tr><td>MML U (Pass@1)</td><td>88.3</td><td>87.2</td><td>88.5</td><td>85.2</td><td>91.8</td><td>90.8</td></tr><tr><td>MML U-Redux (EM)</td><td>88.9</td><td>88.0</td><td>89.1</td><td>86.7</td><td></td><td>92.9</td></tr><tr><td>MML U-Pro (EM) DROP (3-shot F1)</td><td>78.0</td><td>72.6</td><td>75.9</td><td>80.3</td><td></td><td>84.0</td></tr><tr><td></td><td>88.3</td><td>83.7</td><td>91.6</td><td>83.9</td><td>90.2</td><td>92.2</td></tr><tr><td>IF-Eval (Prompt Strict)</td><td>86.5</td><td>84.3</td><td>86.1</td><td>84.8</td><td></td><td>83.3</td></tr><tr><td>GPQA Diamond (Pass@1)</td><td>65.0 28.4</td><td>49.9</td><td>59.1 24.9</td><td>60.0 7.0</td><td>75.7</td><td>71.5</td></tr><tr><td>SimpleQA (Correct) FRAMES (Acc.)</td><td>72.5</td><td>38.2 80.5</td><td>73.3</td><td>76.9</td><td>47.0</td><td>30.1 82.5</td></tr><tr><td>AlpacaEval2.0 (LC-winrate)</td><td>52.0</td><td>51.1</td><td>70.0</td><td>57.8</td><td>■</td><td>87.6</td></tr><tr><td rowspan=\"5\"></td><td>ArenaHard (GPT-4-1106)</td><td>85.2</td><td>80.4</td><td>85.5</td><td>92.0</td><td>■</td><td>92.3</td></tr><tr><td>LiveCodeBench (Pass@1-COT)</td><td>38.9</td><td>32.9</td><td>36.2</td><td>53.8</td><td>63.4</td><td>65.9</td></tr><tr><td>Codeforces (Percentile)</td><td>20.3</td><td>23.6</td><td>58.7</td><td>93.4</td><td>96.6</td><td>96.3</td></tr><tr><td>Codeforces (Rating)</td><td>717</td><td>759</td><td>1134</td><td>1820</td><td>2061</td><td>2029</td></tr><tr><td>SWE Verified (Resolved)</td><td>50.8</td><td>38.8</td><td>42.0</td><td>41.6</td><td>48.9</td><td>49.2</td></tr><tr><td rowspan=\"3\">Math</td><td>Aider-Polyglot (Acc.)</td><td>45.3</td><td>16.0</td><td>49.6</td><td>32.9</td><td>61.7</td><td>53.3</td></tr><tr><td>AIME 2024 (Pass@1)</td><td>16.0</td><td>9.3</td><td>39.2</td><td>63.6</td><td>79.2</td><td>79.8</td></tr><tr><td>MATH-500 (Pass@1) CNMO 2024 (Pass@1)</td><td>78.3 13.1</td><td>74.6 10.8</td><td>90.2 43.2</td><td>90.0 67.6</td><td>96.4</td><td>97.3 78.8</td></tr><tr><td rowspan=\"4\">Chinese C-Eval (EM)</td><td>CLUEWSC (EM)</td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td>85.4</td><td>87.9</td><td>90.9</td><td>89.9</td><td></td><td>92.8</td></tr><tr><td></td><td>76.7</td><td>76.0</td><td>86.5</td><td>68.9 40.3</td><td></td><td>91.8</td></tr><tr><td>C-SimpleQA (Correct)</td><td>55.4</td><td>58.7</td><td>68.0</td><td></td><td></td><td>63.7</td></tr></table></body></html>", "page_idx": 4}, {"type": "text", "text": "蒸馏技术能显著提升小模型推理能力。通过向更高效的小模型蒸馏 DeepSeek-R1 的输出，能够显著提升小模型推理能力。例如，向 Qwen2.5-Math-7B 蒸馏 R1 模型得到的 DeepSeek-R1-Distill-Qwen-7B（简称 R1-7B，下同），全面超越非推理模型如 GPT-4o；向 Qwen2.5-14B 蒸馏得到 R1-14B 在所有评估指标上均超过了 QwQ-32B-Preview；而向 Qwen2.5-32B 和 Llama-3.3-70B-Instruct 蒸馏得到的 R1-32B 和 R1-70B 在大多数基准测 试中 显著超越了 o1-mini。", "page_idx": 5}, {"type": "table", "img_path": "images/c998352f6d8036afdf1f2658b698d3f9c9ee5e5044ebd4eac64cff968a7135ef.jpg", "table_caption": ["图 6: DeepSeek-R1 蒸馏模型评估"], "table_footnote": ["数据来源：《DeepSeek-R1: Incentivizing Reasoning Capability in LLMs via Reinforcement Learning》，中信建投 "], "table_body": "<html><body><table><tr><td rowspan=\"2\">Model</td><td colspan=\"2\">AIME 2024</td><td rowspan=\"2\">MATH-500</td><td rowspan=\"2\">GPQA Diamond</td><td rowspan=\"2\">LiveCode Bench</td><td rowspan=\"2\">CodeForces</td></tr><tr><td>pass@1</td><td>cons@64</td></tr><tr><td>GPT-4o-0513</td><td>9.3</td><td>13.4</td><td>74.6</td><td>49.9</td><td>32.9</td><td>rating 759</td></tr><tr><td>Claude-3.5-Sonnet-1022</td><td>16.0</td><td>26.7</td><td>78.3</td><td>65.0</td><td>38.9</td><td>717</td></tr><tr><td>OpenAI-o1-mini</td><td>63.6</td><td>80.0</td><td>90.0</td><td>60.0</td><td>53.8</td><td>1820</td></tr><tr><td>QwQ-32B-Preview</td><td>50.0</td><td>60.0</td><td>90.6</td><td>54.5</td><td>41.9</td><td>1316</td></tr><tr><td>DeepSeek-R1-Distill-Qwen-1.5B</td><td>28.9</td><td>52.7</td><td>83.9</td><td>33.8</td><td>16.9</td><td>954</td></tr><tr><td>DeepSeek-R1-Distill-Qwen-7B</td><td>55.5</td><td>83.3</td><td>92.8</td><td>49.1</td><td>37.6</td><td>1189</td></tr><tr><td>DeepSeek-R1-Distill-Qwen-14B</td><td>69.7</td><td>80.0</td><td>93.9</td><td>59.1</td><td>53.1</td><td>1481</td></tr><tr><td>DeepSeek-R1-Distill-Qwen-32B</td><td>72.6</td><td>83.3</td><td>94.3</td><td>62.1</td><td>57.2</td><td>1691</td></tr><tr><td>DeepSeek-R1-Distill-Llama-8B</td><td>50.4</td><td>80.0</td><td>89.1</td><td>49.0</td><td>39.6</td><td>1205</td></tr><tr><td>DeepSeek-R1-Distill-Llama-70B</td><td>70.0</td><td>86.7</td><td>94.5</td><td>65.2</td><td>57.5</td><td>1633</td></tr></table></body></html>", "page_idx": 5}, {"type": "text", "text": "Janus-Pro 在多模态理解和生成方面优于统一模型和单一功能模型。Janus-pro 主要延续 Janus 通过解耦多模态理解和生成的研究思路，通过优化训练策略、扩展训练数据和模型规模等方面提高模型性能：", "page_idx": 5}, {"type": "text", "text": "多模态理解：在 Janus 测试过程中选取 POPE、MME-P、MMB、SEED、MMMU、MM-Vet 等广泛认可的图像视觉语言基准测试，同时包括了一种用于真实世界视觉推理和组合式问答的新数据集 GQA。与其他前沿图像理解生成统一模型和仅用于理解的模型相比，Janus-Pro 取得了总体最佳的结果，例如Janus-Pro-7B 在多模态理解基准 MMBench 上得分 79.2，超越了包括 Janus（69.4）、TokenFlow（68.9）和 MetaMorph（75.2）等 ，主 因其将多模态理解和生成的视觉编码解耦，缓解了这两个任务之间的冲突。此外，Janus-Pro 与规模更大的模型相比仍具竞争力，例如 Janus-Pro-7B 在除 GQA 外的其他基准测试上的表现都优于 TokenFlow-XL（13B）。", "page_idx": 5}, {"type": "text", "text": "文本-图像生成：为评估Janus 视觉生成能力，DeepSeek 采用 GenEval（文本到图像构图能力基准测试）和DPG-Bench（密集提示图基准测试）两个工具进行测试。Janus-Pro-7B 在 GenEval 上的总体准确率达到 $80 \\%$ ，超过了所有其他统一模型或仅用于生成的模型，包括 Transfusion（ $63 \\%$ ）、SD3-Medium（ $74 \\%$ ）和 DALL-E 3（ $( 6 7 \\%$ ），反映 Janus-Pro 具有更好的指令跟随能力。同时，Janus-Pro 在 DPG-Bench 上的得分为 84.19，超过了所有其他方法，表明 Janus-Pro 在遵循用于文本到图像生成的密集指令方面表现出色。", "page_idx": 5}, {"type": "image", "img_path": "images/393a24095787f737333a19943d50c7c9b006311216c545383c855929f4d29294.jpg", "img_caption": ["图 7：Janus-Pro 在四个多模态理解基准的平均表现"], "img_footnote": [], "page_idx": 6}, {"type": "text", "text": "资料来源：《Janus-Pro: Unified Multimodal Understanding and Generation with Data and Model Scaling》，中信建投 ", "page_idx": 6}, {"type": "image", "img_path": "images/911beae4b7fddda6f71e667e2f6087c733aef37731393b256449f94f528fbd75.jpg", "img_caption": ["图 8：Janus-Pro 在文本-图像生成基准上的表现"], "img_footnote": ["资料来源：《Janus-Pro: Unified Multimodal Understanding and Generation with Data and Model Scaling》，中信建投 "], "page_idx": 6}, {"type": "text", "text": "我们认为，DeepSeek-R1 性能已基本达到 OpenAI-o1 水平，较 o3 模型基准测试表现仍有不小差距，随着DeepSeek 在 MoE 架构、强化学习等技术上进一步迭代，推理模型性能表现有望持续增长；Janus-Pro 在多模态理解和生成方面则相对表现较好，一定程度验证了图像理解和生成解耦思路的可行性。", "page_idx": 6}, {"type": "text", "text": "1.3 第三问：如何看待DeepSeek-V3 模型的训练成本？", "text_level": 1, "page_idx": 6}, {"type": "text", "text": "DeepSeek 通用及推理模型成本相较于 OpenAI 同类模型下降至数十分之一以下：", "page_idx": 6}, {"type": "text", "text": "通用模型方面，2024 年 12 月 26 日 DeepSeek-V3 更新上线，模型 API 服务定价调整为每百万输入 tokens0.5 元（缓存命中）/ 2 元（缓存未命中），每百万输出tokens 8 元。此外，V3 模型设置长达 45 天的优惠价格体验期：2025 年 2 月 8 日前，V3 的API 服务价格仍保持每百万输入tokens 0.1 元（缓存命中）/ 1 元（缓存未命中），每百万输出 tokens 2 元。与此同时，OpenAI GPT-4o 的 API 服务定价为每百万输入 tokens 1.25 美元（缓存命中）/ 2.5 美元（缓存未命中），每百万输出tokens 10 美元。", "page_idx": 6}, {"type": "text", "text": "推理模型方面，DeepSeek-R1 API 服务定价为每百万输入 tokens 1 元（缓存命中）/ 4 元（缓存未命中），每百万输出 tokens 16 元。而 OpenAI o1 的 API 服务定价为每百万输入 tokens 7.5 美元（缓存命中）/ 15 美元（缓存未命中），每百万输出 tokens 60 美元。", "page_idx": 6}, {"type": "text", "text": "行业动态报告", "text_level": 1, "page_idx": 7}, {"type": "image", "img_path": "images/0a0125942a3456a0297bec87421b1db8bd34928aa451a0c42ebb2a959971eaa4.jpg", "img_caption": ["图 9：DeepSeek-V3 位于模型性能/性价比最优范围"], "img_footnote": ["资料来源：DeepSeek 公众号，中信建投"], "page_idx": 7}, {"type": "image", "img_path": "images/4c72943f3c7b81f1763249472917680b621eb9cdc41ba153055379d84b5e335f.jpg", "img_caption": ["图 10：o1 类推理模型输入输出价格"], "img_footnote": ["资料来源：DeepSeek 公众号，中信建投"], "page_idx": 7}, {"type": "text", "text": "需要注意的是，不同模型 token 切分方法可能不同，通常 1 token 可对应 1-2 个中文汉字，或对应 3-4 个英文字符，或0.75 个英文单词。", "page_idx": 7}, {"type": "table", "img_path": "images/50b0ebaec089a05d308e5fdb439bcb4d14fc503407ed2bf317f6b3e027cfaa53.jpg", "table_caption": ["表 2: 不同大模型汉字/英文token 消耗情况"], "table_footnote": ["资料来源：AI 技术研习社公众号，中信建投"], "table_body": "<html><body><table><tr><td>平台</td><td>1token与汉字的关系</td><td>1token与英文的关系</td></tr><tr><td>通义千问</td><td>约等于1个汉字</td><td>约对应3-4个字母</td></tr><tr><td>ChatGPT</td><td>约等于1个汉字</td><td>约等于4个字符或0.75个单词</td></tr><tr><td>文心一言</td><td>约等于1个汉字</td><td>约对应3-4个字母</td></tr><tr><td>混元大模型</td><td>约等于1.8个汉字</td><td>约等于3个英文字母</td></tr><tr><td>星火大模型</td><td>约等于1.5个汉字</td><td>约等于0.8个英文单词</td></tr><tr><td>Baichuan 53B</td><td>约等于1.5个汉字</td><td>约等于0.8个英文单词或4个字符</td></tr></table></body></html>", "page_idx": 7}, {"type": "text", "text": "DeepSeek-V3（R1 的基础模型）总训练成本仅为 557.6 万美元，但不包括架构、算法等成本。以H800 算力计算，DeepSeek-V3 预训练阶段在不到两个月的时间内完成，耗费 266.4 万个 GPU 小时，加上上下文长度扩展所需的 11.9 万个 GPU 小时和后训练阶段的 0.5 万个 GPU 小时，DeepSeek-V3 的完整训练仅需 278.8 万个GPU 小时；假设 H800 GPU 的租用价格为每 GPU 小时 2 美元，我们的总训练成本仅为 557.6 万美元。需要注意的是，上述成本仅包括 DeepSeek-V3 的正式训练成本，不包括与架构、算法或数据的前期研究及消融实验相关的成本。", "page_idx": 7}, {"type": "table", "img_path": "images/cf1b00c9c7de263d5bc0d8acf48d7d13c0e836b5f8dbfee66d887ee35059714a.jpg", "table_caption": ["表 3: DeepSeek-V3 的训练成本"], "table_footnote": ["资料来源：《DeepSeek-V3 Technical Report》，中信建投注：假设 H800 的租赁价格为每 GPU 小时 2 美元"], "table_body": "<html><body><table><tr><td>训练成本</td><td>预训练</td><td>上下文拓展</td><td>后训练</td><td>总计</td></tr><tr><td>单位：H800 时间（小时）</td><td>2664K</td><td>119K</td><td>5K</td><td>2788K</td></tr><tr><td>单位：美元</td><td>$5.328M</td><td>$0.238M</td><td>$0.01M</td><td>$5.576M</td></tr></table></body></html>", "page_idx": 7}, {"type": "text", "text": "根据我们测算，GPT-4 需要 2.5 万张 A100 训练 95 天（5700 万 A100 GPU小时），OpenAI o1 需要用 3.2万张 H100 训练 90 天（6912 万 H100 SXM GPU小时）：1）GPT-4 由 16 个 111B 的 MoE 模型构成， 其中 两个用于向前传播，另有55B 被用做注意力机制的共享，则 GPT-4 的激活参数量约为 280B，我们假定o1 模型激活参数量是GPT-4 的两倍，达到 560B；2）GPT-4 的预训练数据集token 量为13B，我们假定o1 模型接近其两倍，达到25B；3）GPT-4 的训练时间约为90-100 天，我们取中间值 95 天，并假定o1 的训练周期为 90 天；4）GPT -4 的 GPU 利用率在 $3 2 \\%$ 到 $3 6 \\%$ 之间，我们取中间值 $34 \\%$ ，并假定o1 GPU 利用率也为 $34 \\%$ ；5）根据 OpenAI 在Scaling Laws 论文中给出的经验公式计算（ $\\mathbf { \\bar { C } = r T } \\approx 6 ^ { * } \\mathbf { P ^ { * } D }$ ， $\\mathrm { ~ \\bf ~ P ~ }$ 为模型参数量，D 为训练集token 大小，r 为训练集群硬件 FLOPS 总吞吐），则 OpenAI o1 预训练需要用 3.2 万张 $\\mathbf { H 1 0 0 }$ 。", "page_idx": 7}, {"type": "text", "text": "", "page_idx": 8}, {"type": "table", "img_path": "images/adb7133802e1bdbf31134d153c97976e59396e2ce4be66bfaee0f96934e1d9e8.jpg", "table_caption": ["表 4: GPT-4 与 OpenAI o1 训练 GPU 数量"], "table_footnote": ["资料来源：全天候科技，中信建投测算"], "table_body": "<html><body><table><tr><td>模型</td><td>GPT-4</td><td>OpenAIo1</td></tr><tr><td>模型激活参数量(B)</td><td>280</td><td>560</td></tr><tr><td>预训练 Token量（T）</td><td>13</td><td>25</td></tr><tr><td>理论算力需求（FLOPS）</td><td>2.18E+25</td><td>8.40E+25</td></tr><tr><td>训练天数</td><td>95</td><td>90</td></tr><tr><td>GPU型号</td><td>A100</td><td>H100 (SXM)</td></tr><tr><td>单卡算力（TFLOPS，FP16）</td><td>312</td><td>989</td></tr><tr><td>算力利用率（MFU)</td><td>34%</td><td>34%</td></tr><tr><td>所需GPU数（张)</td><td>25083</td><td>32125</td></tr></table></body></html>", "page_idx": 8}, {"type": "text", "text": "算法迭代、架构升级促进 DeepSeek-V3 模型训练成本降低，符合产业趋势。相较于 GPT-4 和 o1 模型，DeepSeek-R1 的基础模型 DeepSeek-V3 训练成本明显更低，结合 V3 技术报告和上述计算过程，我们认为成本优化主要缘于：1）V3 模型通过DeepSeekMoE 架构（3.1 中将进一步说明），使用更细粒度专家模型，同时隔离部分共享专家，提高计算资源利用率，激活参数少（仅37B），算力消耗低；2）V3 模型采用MLA 算法（3.1 中将进一步说明），通过低秩联合压缩注意力键值，减少推理时的键值（KV）缓存，降低计算量；3）Dual Pipe 框架实现高效流水线并行，或显著提高 GPU利用率；4）DeepSeek 提出了一种利用FP8 数据格式进行训练的细粒度混合精度框架，通过低精度训练优化训练效率。", "page_idx": 8}, {"type": "text", "text": "二、技术不断革新， 大模型 Scaling Law 仍有效", "text_level": 1, "page_idx": 8}, {"type": "text", "text": "2.1 第四问：DeepSeek-V3/R1 技术革新有哪些？", "text_level": 1, "page_idx": 8}, {"type": "text", "text": "通过架构和基础设施创新，DeepSeek-V3 实现了高效训练，奠定 R1 模型优化基础。架构方面，DeepSeek-V3 延续了 V2 模型的 MLA 和 DeepSeek MoE 架构，同时进一步开创了无辅助损失的负载均衡策略，并设定了多token 预测（MTP）训练目标以增强性能：", "page_idx": 8}, {"type": "text", "text": "多头潜在注意力（MLA）：LLM 的核心机制是自注意力（Self-Attention），其要求模型在生成每个token 时考虑之前所有词的关系，则假设文本长度 $\\mathfrak { n }$ 时总体复杂度为 $O ( n ^ { 3 } ) = O (  { \\Sigma } n ^ { 2 } )$ ；过去的研究提出了 KV Cache 方法，利用键值对（KV）存储已计算的注意力信息，此时总体复杂度降低为 $O ( n ^ { 2 } )$ ；而MLA 则进一步通过投影的方式，将token 的相异信息通过投影矩阵存储，在几乎不损失信息的情况下减少键值的缓存需求。", "page_idx": 8}, {"type": "text", "text": "", "page_idx": 9}, {"type": "text", "text": "DeepSeekMoE：专家混合模型（MoE）是当前大模型技术中对前馈神经网络（FNN）的一种替代方案。不同于 FNN 需要全部权重参与计算，MoE 利用门控机制判断输入数据需要由哪些专家模型参与处理。相较于主流MoE 模型，DeepSeekMoE 使用更细粒度的专家，并隔离一些模型作为共享专家，进一步优化了激活参数。此外，为解决专家负载不平衡导致的路由崩溃和计算效率降低，DeepSeek 提出无辅助损失负载均衡策略，为每个专家模型添加可动态调整的偏差项，确保训练过程中专家负载平衡、提高模型性能。", "page_idx": 9}, {"type": "image", "img_path": "images/1367360aec67446fe4bbf401ec2a41b3be44da5ecf3ec1410f9d8b5b4a45e77f.jpg", "img_caption": ["图 11: DeepSeek-V3 基本架构"], "img_footnote": [], "page_idx": 9}, {"type": "text", "text": "数据来源：《DeepSeek-V3 Technical Report》，中信建投", "page_idx": 9}, {"type": "text", "text": "多 token 预测（MTP）：主流大模型 token-by-token 生成序列，而每次 token 生成需要频繁与访存交互，从而因为访存效率形成训练或推理的瓶颈。MTP 方法主要将单 token 的生成，转变成多 token 的生成，提升训练和推理的性能。DeepSeek 主要对过往MTP 算法进行了一定优化，顺序预测额外token，并在每个预测深度保持完整的因果链。", "page_idx": 9}, {"type": "text", "text": "行业动态报告", "text_level": 1, "page_idx": 10}, {"type": "image", "img_path": "images/f4be254809fa9989bef52cd333b7f8907bc3a3759906b5515d87341a38189eb8.jpg", "img_caption": ["图 12: DeepSeek-V3 多 token 预测"], "img_footnote": [], "page_idx": 10}, {"type": "text", "text": "数据来源：《DeepSeek-V3 Technical Report》，中信建投", "page_idx": 10}, {"type": "text", "text": "除了基础架构，DeepSeek 还在基础设施方面进行了一定优化。例如设计了一种创新的管道并行算法DualPipe，在每一对前向和后向块内重叠计算和通信，提高通信效率、加速了模型训练；提出了一种用于 FP8训练的混合精度框架，其中大多数计算密集型操作在 FP8 精度下进行，而一些关键操作则战略性地保持在原始数据格式以平衡训练效率和数值稳定性；训练过程中，采用英伟达 PTX（并行线程执行）汇编级编程替代标准CUDA 方案，实现了硬件级深度优化，减少了计算冗余，提高了推理速度。", "page_idx": 10}, {"type": "image", "img_path": "images/4b631c6527e9bd7a98ea44f7ff7ada5395d32e8143088cb15f910f61c6214f40.jpg", "img_caption": ["图 13: DeepSeek-V3 DualPipe 调度策略"], "img_footnote": ["数据来源：《DeepSeek-V3 Technical Report》，中信建投"], "page_idx": 10}, {"type": "image", "img_path": "images/897d89010798bf762933f885769aeadabfd18be9b13b1cd4bf67c4ec0fded111.jpg", "img_caption": ["图 14: DeepSeek-V3 混合精度框架"], "img_footnote": [], "page_idx": 10}, {"type": "text", "text": "数据来源：《DeepSeek-V3 Technical Report》，中信建投", "page_idx": 10}, {"type": "text", "text": "R1-Zero 验证纯强化学习（RL）对推理能力的提升，R1 则强调冷启动和多阶段训练的平衡。R1-Zero 的特别之处在于，其无需任何监督微调数据即可获得强大的推理能力，反映了模型仅通过强化学习就能有效学习和泛化的能力。具体而言，R1-Zero 模型在 RL 过程中延续了 DeepSeek-V3 组相对策略优化算法（GRPO），通过组内奖励对比优化策略，而不需要额外的判别器，最终实现训练集上的平均响应长度持续提升，自然地学会了通过更多的思考时间来解决推理任务；此外，R1-Zero 训练过程自然地涌现出“思考能力”，即模型自发学会了重新评估其初始回答，并为问题分配更多的思考时间，这种“反思”的特性能够一定程度解决大模型幻觉问题（大模型逐 token 输出，过去没有机制去纠正已经输出的错误，反而会继续用错误掩盖先前的问题，带来幻觉问题）。", "page_idx": 10}, {"type": "text", "text": "", "page_idx": 11}, {"type": "text", "text": "尽管R1-Zero 模型展现了强大的推理能力，但仍面临可读性差和语言混合等挑战，R1 模型则通过冷启动和多阶段训练解决了上述问题。R1 同样从 DeepSeek-V3-Base 基础模型出发，经过数千条优质长链思维（CoT）数据微调（SFT）作为冷启动，使模型输出更符合要求、可读性更强；而后，针对微调后的模型采用与 R1-Ze ro 相同的大规模强化学习，并引入语言一致性奖励，直至模型在推理任务上达到收敛；面向推理的强化学习收敛后，利用生成的检查点收集新的SFT 数据，从而融入来自其他领域的数据，以增强模型在写作、角色扮演和其他通用任务中的能力；最后，为了进一步使模型与人类偏好保持一致，实施次级 RL 阶段，旨在提高模型的有用性和无害性、精炼其推理能力。通过冷启动和多阶段训练，R1 模型最终具备较强的推理性能，同时在可读性上表现较好。", "page_idx": 11}, {"type": "image", "img_path": "images/0cac82d2eacfdb5bb2cd9cf2b97f326ab8fdfc6d66bab7196529bb8e1d0c3e44.jpg", "img_caption": ["图 15：DeepSeek-R1-Zero 的思考时间呈现出持续的提升"], "img_footnote": ["资料来源：《DeepSeek-R1: Incentivizing Reasoning Capability in LLMs via Reinforcement Learning》，中信建投 "], "page_idx": 11}, {"type": "image", "img_path": "images/789330f44400833bb8863b3963d92605ca2d97dda5aa6ca3a744bfebbad668e2.jpg", "img_caption": ["图 16：R1-Zero 和 R1 训练过程对比"], "img_footnote": ["资料来源：《DeepSeek-R1: Incentivizing Reasoning Capability in LLMs via Reinforcement Learning》，AINLP 公众号，中信建投 "], "page_idx": 11}, {"type": "text", "text": "R1 系列模型提供了 RL Scaling Law的可行方向。实际上，在OpenAI 推出o1 模型时即发现了推理性能随着训练时间和测试时间计算而平稳提升的“RL Scaling law”，但业内尚未通过过程奖励模型（PRM）和蒙特卡洛树搜索（MCTS）等方法做出较好的效果，R1 的技术报告更是提到 PRM 和 MCTS 存在难以规模化拓展、奖励欺骗等问题。R1 模型的技术报告提供了一种多阶段训练的方式，其中在第一阶段 RL 过程中，研究人员可以通过扩大RL 训练集的方式提升模型性能，或为一种可以验证的“RL Scaling law”方向；OpenAI 首席研究官 MarkChen 也承认，“DeepSeek 的确独立发现了一些 o1 的核心思路”。", "page_idx": 11}, {"type": "text", "text": "<PERSON>@samadeepseek'sr1isan impressivemodel, particularlyaroundwhatthey'reable to deliver for the price.wewillobviouslydelivermuchbettermodelsandalsoit'slegitinvigoratingtohaveanewcompetitor!wewillpullupsomereleases.DeepSeek的R1是一个令人印象深刻的模型，尤其是围绕他们能够以价格交付的方式。显然，我们将提供更好的模型，并且拥有新的竞争对手的合法性！我们将提取一些版本。使用DeepL翻译", "page_idx": 12}, {"type": "text", "text": "资料来源：机器之心公众号，中信建投", "page_idx": 12}, {"type": "text", "text": "<PERSON>@markchen90  \nCongratsto DeepSeekonproducingano1-level reasoningmodel!Their  \nresearchpaperdemonstratesthatthey'veindependentlyfoundsomeof  \nthecoreideasthatwedidonourwaytoo1.  \n恭喜DeepSeek制作了O1级推理模型！他们的研究论文表明，他们独立地找  \n到了我们在前往O1的途中所做的一些核心思想。  \n使用DeepL翻译  \n2:11AM·Jan29,2025·2MViewsQ582 1.1K 14K 2.9K 山", "page_idx": 12}, {"type": "image", "img_path": "images/e6ec849fe9c91ee69134859b87aa6f5c2e753d96dd803009cc35ff45456cf2c3.jpg", "img_caption": ["图 17：<PERSON> 肯定 DeepSeek-R1 的性能", "图 18：<PERSON> 承认 DeepSeek 发现了 o1 的核心思路", "图 19：模型“能力分治”架构"], "img_footnote": ["资料来源：机器之心公众号，中信建投"], "page_idx": 12}, {"type": "text", "text": "蒸馏使小模型具备较强逻辑推理能力的思路或与 OpenAI o1-mini 不同。据张俊林分析，o1 系列模型更可能是重新训练的（OpenAI 多次强调o1-mini 逻辑推理能力强，但在世界知识方面弱；如果其基于 GPT 系列模型而来，世界知识应该不会弱于 GPT 4o-mini），而 DeepSeek-R1 则是在 V3 的基础上通过强化学习训练得到。因此，DeepSeek 通过向更高效的小模型蒸馏 DeepSeek-R1 的输出，显著提升小模型推理能力，更可能走出了与OpenAI o1-mini 不同的道路，从而实际上打破了之前“小模型逻辑推理能力难以通过蒸馏提升”的研究结论。", "page_idx": 12}, {"type": "text", "text": "此时，小模型有望通过“能力分治”（DCA）的模式将语言、世界知识及逻辑推理三个能力解耦，即语言能力靠小模型自身、逻辑推理靠 $\\scriptstyle { \\mathrm { R L } } +$ 蒸馏，世界知识靠外挂 RAG，从而具备目前最强大模型的能力，对于中小型开发者而言，部署模型也将更加友好。", "page_idx": 12}, {"type": "text", "text": "数据来源：机器学习算法与自然语言处理公众号，中信建投", "page_idx": 12}, {"type": "text", "text": "我们认为，DeepSeek-V3/R1 系列模型的核心突破在于 1）技术及架构升级显著优化模型训练成本，即工程优化了MoE 模型架构，预计未来各厂商仍将围绕MoE 模型进行注意力头的架构优化；2）组相对策略优化算法（GRPO）实质上仅依赖模型自身近些迭代，实现了“反思能力”；3）提供了一种具体可行的“RL Scaling law”方向，各厂商或将跟进并继续探索其他方向；4）蒸馏使小模型具备较强逻辑推理能力，有望促进中小型开发者推出相关应用。", "page_idx": 12}, {"type": "text", "text": "2.2 第五问：Janus 系列模型技术革新有哪些？", "text_level": 1, "page_idx": 13}, {"type": "text", "text": "Janus 系列模型缓解多模态理解和生成的冲突，提升模型能力表现。多模态理解与生成任务本身存在视觉编码器需求的冲突，其中在理解任务中，视觉编码器的目的是提取高层次的语义信息并进行表示；而生成任务则主要关注生成局部细节并在图像中保持全局一致性，因此需要低维度编码表示空间结构和纹理细节。Janus 系列模型的核心技术在于实现多模态理解与生成的解耦，通过2 个独立的视觉编码路径，缓解多模态理解和生成的冲突，从而提高模型的能力表现和可扩展性。", "page_idx": 13}, {"type": "image", "img_path": "images/6dc11addb18772021ef780b13b87fbf09c47ac6e88c4bdebaad332fd3ed53f2f.jpg", "img_caption": ["图 20：Janus-Pro 架构"], "img_footnote": [], "page_idx": 13}, {"type": "text", "text": "多模态生成模型架构尚无定论，自回归和扩散模型持续发展。目前图像生成模型主要包括以Transformer 为代表的自回归生成、以 DDPM、LDM、DiT 为代表的扩散模型，以及 MaskGIT、MAR 等掩码自回归图像生成三类架构。自回归架构通过算法逐个生成像素，DeepSeek 的 Janus 系列模型为其中代表；掩码自回归则优化了单次像素生成数量和顺序，提高了自回归模型的速度和表现；扩散模型的代表包括 Sora，其将图像生成表示成噪声图像变化至目标图像的过程，输入输出自始至终都是完整图像。目前，自回归和扩散模型均有前沿技术持续性突破，带来模型能力的持续提升。", "page_idx": 13}, {"type": "image", "img_path": "images/1599d9e8fd5b1ab390e569bcd88df928a19c18eb195dcb71c87b04e2a6920624.jpg", "img_caption": ["数据来源：《Janus-Pro: Unified Multimodal Understanding and Generation with Data and Model Scaling》，中信建投 ", "图 21：Diffusion Transformer (DiT) 架构"], "img_footnote": [], "page_idx": 13}, {"type": "text", "text": "数据来源：《Scalable Diffusion Models with Transformers》，中信建投", "page_idx": 13}, {"type": "text", "text": "我们认为，多模态模型整体仍处于技术探索过程中，Janus 系列核心在于提供了一种理解和生成解耦的架构，一定程度提升了模型表现，后续自回归和 DiT 技术将进一步发展，带来多模态模型性能的持续优化。", "page_idx": 14}, {"type": "text", "text": "2.3 第六问：DeepSeek 数据集的特点是什么？", "text_level": 1, "page_idx": 14}, {"type": "text", "text": "合成（生成）数据在大模型训练过程中发挥着重要作用。在高质量训练数据耗尽，以及互联网中充斥大量噪声数据的背景下，合成数据已成为大模型训练过程中数据集的重要来源, 截至 2024 年 9 月，在 HuggingFace 平台上标注为 “合成” 的数据集已超过 1000 个。具体而言，合成数据主要由算法、模型生成，为大模型训练提供更丰富且针对性强的信息，帮助拓展模型性能：", "page_idx": 14}, {"type": "text", "text": "通用大模型：在通用大模型训练中，合成数据主要用于丰富数据集，提升模型性能。以 DeepSeek-V3 的训练为例，其在监督微调阶段借助 DeepSeek-R1 模型生成样本数据，经 RL 训练后用拒绝采样筛选高质量数据用于最终模型训练，有效提升了模型的推理能力。", "page_idx": 14}, {"type": "text", "text": "推理模型：在推理模型训练中，合成数据主要用于优化训练流程。例如，DeepSeek-R1 在冷启动阶段利用R1-Zero 生成 $^ +$ 人工标注数据进行微调，并在监督微调阶段通过 V3 模型收集了约 60 万条与推理相关的训练样本，以及约20 万条与推理无关的训练样本。此外，R1 向小模型蒸馏的过程实际上也是通过R1 生成数据对小模型进行监督微调实现的。", "page_idx": 14}, {"type": "text", "text": "多模态模型：多模态模型训练中，合成数据能改善数据质量，显著强化视觉生成能力。Janus - Pro 在预训练阶段相较于 Janus 引入约 7200 万个合成美学数据样本，使真实数据与合成数据比例达到 1:1，从而加速了模型收敛速度，提升图像生成质量。而 Kimi-1.5 作为以强化学习方式训练的多模态大模型，分别在预训练阶段通过合成数据强化了推理和基于知识任务的解答能力，在多模态训练阶段合成了图像文本交错数据。", "page_idx": 14}, {"type": "image", "img_path": "images/57a060a75840cdc2352e077cf59cf3a11c4aa57556bcbd5433fd5c012c0f79f8.jpg", "img_caption": ["图 22：合成数据一般生成范式"], "img_footnote": ["数据来源：机器之心，中信建投"], "page_idx": 14}, {"type": "text", "text": "GRPO 算法在一定程度上使模型摆脱人类经验的束缚。如 2.1 所述，R1 - Zero 模型在 RL 过程中延续了DeepSeek - V3 组的相对策略优化算法（GRPO）。该算法通过组内奖励对比优化策略，无需额外的判别器，最终实现了训练集上平均响应长度的持续提升，使模型自然地学会通过更多思考时间来解决推理任务。实际上，GRPO对于 RL 数据集的处理同样具有重要意义。具体而言，PPO 算法需要依赖价值模型估计状态价值，以帮助计算优势函数；而 GRPO 算法只对输出的语言内容进行相对优势计算，不需要设计价值模型。价值模型的设定本身就包含了人类偏好，这种偏好通过人类经验限定了数据集的价值。而 GRPO 算法本质上可看作模型生成内容的自我博弈，它能让模型摆脱人类经验的束缚，通过提升思考深度不断拓展性能，最终甚至可能超越人类水平。", "page_idx": 14}, {"type": "image", "img_path": "images/6836aa536e7e0463a8290dff9cd91799f6dddb51b1d7a28e322698a2a033ff0d.jpg", "img_caption": ["图 23：PPO 和 GRPO 算法对比图"], "img_footnote": [], "page_idx": 15}, {"type": "text", "text": "数据来源：《Large Language Models Self-Compose Reasoning Structures》，中信建投 ", "page_idx": 15}, {"type": "text", "text": "我们认为，DeepSeek-V3/R1/Janus 等模型对于合成数据的应用符合大模型研究趋势，而 GRPO 算法则进一步使模型在RL 过程中摆脱了人类经验的限制，从而能够最大程度挖掘数据集的价值，向模型超越人类，最终实现AGI 的道路进发。", "page_idx": 15}, {"type": "text", "text": "2.3 第七问：Scaling Law 到底是否有效？", "text_level": 1, "page_idx": 15}, {"type": "text", "text": "训练侧 Scaling law推动模型能力持续提升，但仍面临技术、算力、数据的制约。早在2020 年，OpenAI即在论文中提出了“Scaling law”，其内涵在于大模型的最终性能主要与计算量、模型参数量和训练数据量三者的大小相关，而与模型的具体结构（层数/深度/宽度）基本无关。在“Scaling law”的思路下，业内追求在训练侧用更多的高质量数据，训练更大参数规模的模型，尤其在MoE 架构并行计算的加持下，大模型参数甚至能够提升至万亿以上，极大程度提高了模型的效果。", "page_idx": 15}, {"type": "text", "text": "然而，受到技术、算力、数据的制约，训练侧“Scaling law”正面临瓶颈：1）更高参数规模的模型训练比较复杂：当参数规模提升到万亿规模，模型进一步调整的技术方式仍待突破；2）算力规模一定程度制约了模型发展：英伟达 H100 目前可以做到单一集群 3.2 万张卡充分互联，每2 小时会出错一次（Founder Park 访谈拾象科技 CEO 李广密）。一旦算力集群增加到10 万卡，可能每 20-30 分钟即会出错一次，对数据中心的运维能力要求较高，否则会导致算力利用率明显下降。此时需要性能更强的算力卡出现。3）高质量数据缺失：早有消息称大模型训练已经耗尽了高质量数据，因此如果只是简单提升训练集规模，往往重复的数据占据了主要部分，从而对模型能力的提升有限。而数据合成的技术仍未能突破，同样一定程度上制约了模型的发展。", "page_idx": 15}, {"type": "image", "img_path": "images/3a7993ad7c9ac221324ffba9cba2300bfb61e69fb080f37e647d86ac6ecaa935.jpg", "img_caption": ["图 24: 大模型的 Scaling law"], "img_footnote": ["数据来源：《Scaling Laws for Neural Language Models》，中信建投"], "page_idx": 16}, {"type": "text", "text": "思维链等方式打开推理侧大模型能力提升空间。当训练侧“Scaling law”进度相对放缓，OpenAI 于 2024年9 月发布了系列新模型o1，其利用强化学习技术，通过提高推理侧的思考时间，大幅优化了模型表现；还能够在训练过程中生成高质量数据，解决天然数据缺失的问题。以思维链技术为例，其类比人类思考过程，使大模型在推理过程中把复杂问题拆解成若干简单步骤，从用户提出的问题出发，逐步生成正确答案。OpenAIo1 模型性能随着训练时间和测试时间计算而平稳提升，后训练及推理阶段思考深度（时间）或将成为 新的“Scaling law”；相较于 OpenAI 未开源推理算法，DeepSeek-R1 系列模型提供了 RL Scaling Law 的可行方向，有望促进各厂商跟进并继续探索其他推理侧拓展方向。", "page_idx": 16}, {"type": "text", "text": "Standard Prompting Chain-of-Thought Prompting Model Input Model Input   \nQ: <PERSON> has 5 tennis balls. He buys 2 more cans of Q: <PERSON> has 5 tennis balls. He buys 2 more cans of   \ntennis balls. Each can has 3 tennis balls. How many tennis balls. Each can has 3 tennis balls. How many   \ntennis balls does he have now? tennis balls does he have now?   \nA: The answer is 11. A: <PERSON> started with 5 balls.2 cans of 3 tennis balls each is 6 tennis bals. 5 + 6 = 11. The answer is 11.   \nQ: The cafeteria had 23 apples. If they used 20 to   \nmake lunch and bought 6 more, how many apples Q: The cafeteria had 23 apples.If they used 20 to   \ndo they have? make lunch and bought 6 more, how many apples do they have? Model Output Model Output   \nA: The answer is 27. X A:Thecafeteria had23applesoriginally.Theyused 20tomakelunch.Sotheyhad23-20=3.They bought6 more apples,sotheyhave3 +6=9.The answer is 9. ", "page_idx": 16}, {"type": "image", "img_path": "images/20b5cf6d3face20430f6c1b769cb3fec4ab068d62ff406a831fad20d4504e7c3.jpg", "img_caption": ["图 25：CoT 方式概述", "图 26：o1 性能随着训练时间和测试时间计算而平稳提升"], "img_footnote": ["资料来源：《Chain-of-Thought Prompting Elicits Reasoning in Large Language Models》，中信建投 ", "资料来源：OpenAI，中信建投"], "page_idx": 16}, {"type": "text", "text": "Scaling law三条路径齐头并进，助力模型性能持续提升。正如英伟达CEO 黄仁勋在CES 2025 上的主题发言提到的，o1 模型推出后，大模型Scaling law 已经实际上分为了三个路径：", "page_idx": 16}, {"type": "text", "text": "Pre-Training Scaling：对应 OpenAI 2020 年提出的结论，训练数据规模越大、模型规模越大、计算资源投入越多，AI 模型的性能就会相应提升。尽管Pre-Training Scaling 目前受技术、算力、数据影响遭遇瓶颈，但更强大的基础模型仍然是各厂商追求的主要方向，DeepSeek-R1 的技术报告同样提出，“更大基础模型发现的推理模式对于提升推理能力至关重要”。未来随着MoE 架构、模型Infra 等方面的优化，Pre-Training Scaling 有望持续发展。", "page_idx": 16}, {"type": "text", "text": "Post-Training Scaling： 包括强化学习和人类反馈等技术，通过输入大量优质的提示，优化模型性能表现。实际上，受限于人类工作效率，原有的人类反馈强化学习（RLHF）存在难以规模化扩张的问题（例如人工标注数据效率较低、不同标注者标准不一致等），而DeepSeek-R1 纯 RL 的技术方案实际上打破了这种限制，为各厂商提供了 Post-Training Scaling 的可行方案。", "page_idx": 17}, {"type": "text", "text": "Test-Time Scaling：强调重新调配资源，即在推理阶段考虑投入多少算力，并利用思维链将问题分解成若干个小步骤逐一解决。通过在模型推理阶段更加深入的思考，模型将具备更强劲的性能。", "page_idx": 17}, {"type": "image", "img_path": "images/f513964b00ab9c64f0c42104de362d269938690249a77cce6275803557314980.jpg", "img_caption": ["图 27：黄仁勋在 CES 2025 上发言称“Scaling Law”持续", "图 28：强化学习也需要更强大的基础模型"], "img_footnote": ["资料来源：英伟达，新浪财经，中信建投"], "page_idx": 17}, {"type": "text", "text": "更大基础模型发现的推理模式对于提升推理能力至关重要。 <PERSON><PERSON>rther exploredistillatjon fromDeepSeek-R1 tosmaller densemodels.UsingQwen2.5- 32BQwen,2024b)asthebasemodel,directdistilationfromDeepSeek-R1outperformsapplying RLonit.Thisdemonstrates thatthereasoningpaternsdiscoveredbylargerbasemodelsarecrucialforimprovingreasoning capabilities.Weopen-sourcethedistilledQwenand Llama(Dubey etal.2024)series.Notably,ourdistilled14Bmodel outperformsstate-of-the-art open-source QwQ-32B-PreviewQwen,2024a)byalargemargin,andthedistilled32Band70Bmodelsseta newrecord on the reasoning benchmarksamong dense models. ", "page_idx": 17}, {"type": "text", "text": "我们认为，Scaling Law 仍有效，同时RL 技术的不断迭代为模型能力的规模化扩张带来了新的方向。特别是DeepSeek 通过架构和技术创新，提出了纯RL 和分阶段的模型训练方法，并实现了较好的性能表现。预计各厂商将陆续跟进DeepSeek 的算法方向，并不断对架构进行调整，以探索出更为理想的模型优化方式。", "page_idx": 17}, {"type": "text", "text": "三、DeepSeek-R1 促进 AI 平权，产业链享受发展红利", "text_level": 1, "page_idx": 17}, {"type": "text", "text": "3.1 第八问：R1 是否意味着 AI 平权已经实现？", "text_level": 1, "page_idx": 17}, {"type": "text", "text": "DeepSeek-R1 开源引发全球复现热潮，小模型 $\\mathbf { \\nabla } _ { + \\mathbf { R } \\mathbf { L } }$ 实现“反思”涌现。在美国对中国实施 AI 芯片封锁的背景下，DeepSeek 以极低的成本成功训练出跻身全球第一梯队的推理模型 R1。同时，DeepSeek 完全开源了模型权重，所遵循的 MIT License 开源协议极为宽松，允许其他开发者将模型用于商业用途并进行模型蒸馏，被Facebook 首席人工智能科学家杨立昆誉为“开源模型对闭源模型的胜利”。", "page_idx": 17}, {"type": "text", "text": "R1 发布以来，全球前沿团队积极复现，目前已取得较好成效。其中，UC 伯克利的团队在 CountDown 游戏中复现了DeepSeek R1-Zero，以不到30 美金的成本通过强化学习，使3B 的基础语言模型完成自我验证和搜索；港科大的团队只用了 8K 个样本，就在 7B 模型上复刻出了 DeepSeek-R1-Zero 和 DeepSeek-R1 的训练，使模型在复杂的数学推理上取得强劲的结果；甚至全球最大开源平台 HuggingFace 团队，也在 1 月 26 日官宣开始复刻DeepSeek-R1 的所有pipeline，并将在复刻完成后，开源所有的训练数据和脚本。", "page_idx": 17}, {"type": "image", "img_path": "images/2edbfed9e92575a128db66f464104155eea4b674f1757077438bf81dc0270250.jpg", "img_caption": ["图 29：杨立昆称 DeepSeek-R1 为开源对闭源的胜利"], "img_footnote": ["资料来源：新智元，中信建投"], "page_idx": 18}, {"type": "text", "text": "60 1000 Reasoning Pattern 900 Emergence of Self Reflection   \n40 600 MoreReasoningTokens Accuracy Response Length   \n35 500 0 16 32 48 64 80 96 ", "page_idx": 18}, {"type": "text", "text": "全球大厂接连接入R1，DeepSeek 冲击下 OpenAI 战略方向或将转向。尽管美国质疑DeepSeek 在安全性、隐私方面的问题，但英伟达、英特尔、亚马逊、微软、AMD 等海外巨头仍纷纷在自家产品中接入了DeepSeek；国内硅基流动和华为云同样联合首发并上线了基于华为云昇腾云服务的 DeepSeek R1/V3 推理服务。受DeepSeek 全球热度冲击，Sam Altman 承认在开源策略上“站在了历史错误的一边”，并表示正在讨论开源部分模型。此外，OpenAI 于2 月 1 日紧急更新了o3-mini 系列，即使是免费用户也可以通过选择“Search+Reason”来使用体验 o3-mini 的搜索功能。然而，o3-mini 模型当前的定价为每百万输入 tokens 0.55美元（缓存命中）/ 1.1 美元（缓存未命中），每百万输出 tokens 4.4 美元，远高于R1 模型。", "page_idx": 18}, {"type": "image", "img_path": "images/f8d7efca0204184e10dbdd0c5b9bd830025b9591d969f04fa3168f5c8998be6a.jpg", "img_caption": ["图 30：港科大的团队复现了 R1-zero 和 R1 的训练过程", "图 31：海外大厂纷纷接入 R1 模型", "图 32：<PERSON> Altman 承认在开源策略上的错误"], "img_footnote": ["资料来源：中国基金报，英伟达，微软，亚马逊，中信建投"], "page_idx": 18}, {"type": "text", "text": "AMAwithOpenAl'<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>. lolzinventor·4h   \nWouldyouconsiderreleasingsomemodelweights,   \nandpublishingsomeresearch? 8 samaltmanCO-HosT·3h yes,wearediscussing.ipersonallythinkwehave beenonthewrongsideofhistoryhereandneedto figureoutadifferentopensourcestrategy;not everyoneatopenaisharesthisview,andit'salso notourcurrenthighestpriority. 山 2 ", "page_idx": 18}, {"type": "text", "text": "参考安卓及 iOS 份额变化，开源生态有望为 AI产业注入活力。在智能手机操作系统领域，安卓的开源与iOS 的封闭带来了截然不同的生态模式：", "page_idx": 19}, {"type": "text", "text": "安卓：Android 公司成立于 2003 年，2005 年被 Google 收购，并在 2007 年正式推出了 Android 操作系统。生态上，安卓系统开源开放，允许众多手机厂商基于其底层架构进行定制化开发，使其市场份额从 2008 年的$2 . 8 \\%$ 提升到 2011 年的 $4 8 \\%$ ，但同时也带来了专利诉讼、软件盗版和系统安全等一系列问题；2011 年，Google推出 Android 4，从此安卓设备逐步正规化、标准化，直至2024 年 12 月，安卓操作系统市场份额已经达到$7 3 . 4 9 \\%$ 。", "page_idx": 19}, {"type": "text", "text": "iOS：同样在安卓系统正式发布的2007 年，苹果发布了搭载iOS 系统的第一代 iPhone，开启了智能手机的新时代。相较于安卓的开放，苹果iOS 系统采用封闭式生态，严格把控软件审核环节，一定程度限制了系统的灵活性，但为用户提供了一致且高质量的使用体验。从市场份额看，近年来 iOS 系统的市占率相对稳定，2024 年 12 月市场份额为 $2 6 . 0 4 \\%$ ，低于 2009 年 1 月 iOS 的市场份额 $3 5 . 5 6 \\%$ 。", "page_idx": 19}, {"type": "text", "text": "AI产业：类比手机操作系统领域，当前AI 产业同样面临开源和闭源之争。参考安卓系统发展历程，开源模式能够吸引全球范围的开发者参与AI 技术创新，后来者能够基于已有成果快速进行应用开发与产品迭代，从而推动 AI 应用的快速落地，推动AI 产业加速发展。", "page_idx": 19}, {"type": "image", "img_path": "images/eae296249b5368ab59dc5f34d9aa0d7783f6149c94f5b42f60caeb9c8ce36056.jpg", "img_caption": ["图 33：2009 年 1 月至 2024 年 12 月全球手机操作系统份额"], "img_footnote": ["数据来源：statcounter，中信建投"], "page_idx": 19}, {"type": "text", "text": "我们认为，DeepSeek-R1 作为开源模型性能接近头部闭源模型o1，一定程度上已经反映了AI 平权。实际上，过去OpenAI 的领先更多基于先发优势，而当开源模型的性能实现对闭源模型的追赶，全球的团队的研发能力能够使开源模型的性能始终位于前列。近期各研究团队对R1 模型的积极复现更是侧面验证了开源模式的优势。此外，DeepSeek-R1 使小模型具备推理能力成为可能，更低的成本将更有利于开发者探索AI 的实际落地，带来更有价值的产品。", "page_idx": 19}, {"type": "text", "text": "3.2 第九问：DeepSeek 出圈对产业的影响有几何？", "text_level": 1, "page_idx": 20}, {"type": "text", "text": "DeepSeek 以其低成本、高性能全面影响AI产业链。AI 产业链大致可分为基础层（算力、数据、技术等）、模型层（通用/行业大模型、开发平台）和应用层（通用/垂域应用、Agent 等）。尽管创始人梁文锋称DeepSeek 技术突破只是“美国每天发生的大量创新里非常普通的一个”，但其低成本、高性能，以及为小模型带来强大推理能力的蒸馏方式，仍对AI 产业链产生了冲击：", "page_idx": 20}, {"type": "text", "text": "人工智能应用层计算机视觉大数据智能 营销客服 智慧管理自动驾驶  \n三 三 省 三   \n鑫 福 AI开放平台 AI开发平台  \n[-]阿里云 Ba大起火山开  \n京东云华为云腾讯云HIKVISION vn → [-阿里云百度智能云华为云 penAI  \nOpenAI IBM Ba大nRS 语音大模型 HOA小鹏 具商涵华为云[-阿里云 OpenAI [-]阿里云百  \n华为云西8酱五 京东云山一万物 8Meta  \n京东云n S MEGV旷视 百度智能云[-阿里云态 9OpenAI [-］阿里云西智糊云 多模态大模型 华为云京东云 川 百著昵云 百度智能云 -阿里云百度智能云e京东云西esE 讯云 PAA 华为云giteen DataCanvas火山SoSToNE智能云服务 向量数据库 数据集 Al模型架构企业自建智算中心 城市智算中心 业华为云 TnsorDB W DataCarvas 公共开源 企业私有 高校 政府智算软件平台 京东云 aws Pinecone qdrant", "page_idx": 20}, {"type": "text", "text": "算力：DeepSeek 的爆火使得“杰文斯悖论”这一经济学名词受到关注，它是指“燃料效率的提高往往会增加燃料使用”。如果将该理论拓展到算力领域，模型对算力应用效率的提升反而会带来算力需求的增长。实际上，“杰文斯悖论”反映了简单的经济学原理——当需求价格弹性系数大于 1，价格下降则会带来销售收入增加。因此，DeepSeek 影响下算力需求是否增加的关键在于算力的价格弹性，而这又受到算力用途的影响（一般来说，商品用途多，需求弹性就越大）。", "page_idx": 20}, {"type": "text", "text": "算力作为新一轮科技革命的底层基础，将会应用于千行百业，DeepSeek-R1 使小模型能通过蒸馏具备较强逻辑推理能力，更进一步加速了下游应用的产生，则算力的价格弹性更可能大于 1，符合“杰文斯悖论”，从而持续保持旺盛的需求。此外，梁文锋在访谈中提到高端芯片禁运或将成为卡点，同样反应了算力芯片自主可控的重要性。", "page_idx": 20}, {"type": "text", "text": "行业动态报告", "text_level": 1, "page_idx": 21}, {"type": "image", "img_path": "images/ac68a54e710e6a556e330d7c4c4ec9f4515bc6d7cb1d3cd94ad366c7ab45dd94.jpg", "img_caption": ["图 35：杰文斯悖论发生与否取决于需求弹性"], "img_footnote": ["弹性需求：效率提高 $20 \\%$ ，导致旅行增加$40 \\%$ 。 燃料消耗增加，杰文斯悖论发生。"], "page_idx": 21}, {"type": "image", "img_path": "images/9969efef3851966776d759376a5bfe54b4488c839d07b85e8d6998ccc8e630d2.jpg", "img_caption": [], "img_footnote": ["无弹性需求：效率提高 $20 \\%$ ，导致旅行增加 $10 \\%$ 。 杰文斯悖论不会发生。"], "page_idx": 21}, {"type": "text", "text": "模型：DeepSeek-R1 模型的突破实际上反映了中美在前沿大模型差距的缩小。以发布于2024 年 3 月的GPT-4 为例，2024 年 1 月发布的智谱 GLM-4 才在部分 benchmark 上达到了其 $9 0 \\% - 1 0 0 \\%$ 的水平，模型差距在10 个月以上；而 2025 年 1 月发布的 R1 已经接近 OpenAI 2024 年 9 月发布的 o1 模型，模型差距缩短到 4 个月左右。而大模型本身及其对应的 Chat bot 产品，用户切换成本低，存在“赢者通吃”的现象，例如 kimi 在2024 年3 月实现上下文无损输入长度提升至 200 万字，爆火出圈带来流量的大幅上涨；2024 年 12 月字节火山引擎热度攀升，以及DeepSeek-V3 的发布同样带来了流量的快速提升。在此背景下，预计大厂将跟进DeepSeek 模型层的研发，技术开源亦将促进大厂持续投入，形成正反馈。此外，DeepSeek 通过纯 RL 算法、架构优化等方式实现了模型性能的提升，或将促进各厂商在相关领域进行更多的探索。", "page_idx": 21}, {"type": "image", "img_path": "images/cdb1a3e958b0fd9f3f9229f3d7a48ff57083133341f3b1d753abbd801bf4f365.jpg", "img_caption": ["图 36：2024 年 1 月-12 月国内大模型 Chat bot（部分）月活"], "img_footnote": ["数据来源：微观经济学，中信建投", "数据来源：AI 产品榜，中信建投"], "page_idx": 21}, {"type": "text", "text": "应用：DeepSeek-V3/R1 作为通用/推理方面的基础模型，性能升级及在各类 Benchmark 跑分中的提高，本身就为应用落地带来了更大的可能性。然而，对于开发者而言，更关键的点在于模型能够和应用适配调优，提供稳定性的API 服务，以及性价比更高的tokens 成本。参考 2024 年 5 月DeepSeek-V2 发布后带来的大模型价格战，即使模型成本更高，字节、阿里等大厂亦按照烧钱补贴的逻辑大幅降价，本质上是因为开发者价格敏感，大厂愿意亏钱抢占市场份额，培育开发者使用习惯。", "page_idx": 21}, {"type": "text", "text": "", "page_idx": 22}, {"type": "text", "text": "考虑到 DeepSeek-R1 开发和调用成本本身较低，还通过蒸馏的方式带来了小模型推理能力的提升，则应用开发者能够以更低的成本部署模型或调用 API，并保持相对优秀的性能。当应用开发门槛降低，预计会出现更多产品探索方向，直至出现具有突破性的 “killer”应用。同时，DeepSeek-R1 的低价，同样有望带来推理模型新一轮的价格战（ $\\mathbf { 0 3 - m i n i }$ 的价格本身已经验证了这一观点），为开发者带来更多性价比之选。最后，当DeepSeek 模型的能力达到全球第一梯队后，其作为国内厂商能为国内应用开发者提供更稳定的服务（调用GPT API 可能会受到各种限制），亦将促进各类应用产生。", "page_idx": 22}, {"type": "table", "img_path": "images/a499974244ab415d48f804d4c3e578ffb7f97dbb918ab3d1169f5f810db74685.jpg", "table_caption": ["表 5: DeepSeek-V2 发布后，各厂商宣布降价的模型/最高性能模型价格变动情况"], "table_footnote": ["资料来源：镜相工作室，中信建投"], "table_body": "<html><body><table><tr><td rowspan=\"2\">公司</td><td rowspan=\"2\">降价时间</td><td rowspan=\"2\">模型</td><td>降价前 （元/万tokens）</td><td>降价后 （元/万tokens）</td><td rowspan=\"2\">降价幅度</td></tr><tr><td></td><td></td></tr><tr><td rowspan=\"2\">DeepSeek</td><td rowspan=\"2\">5月6日</td><td rowspan=\"2\">DeepSeek-V2</td><td>输入：/</td><td>输入：0.01 输出：0.01</td><td rowspan=\"2\">首次发布</td></tr><tr><td>输出：/</td><td></td></tr><tr><td rowspan=\"3\">智谱AI</td><td rowspan=\"3\">5月11日</td><td rowspan=\"2\">GLM-3 Turbo</td><td>输入：0.05</td><td>输入：0.01</td><td rowspan=\"2\">90%</td></tr><tr><td>输出：0.05 输入：1</td><td>输出：0.01</td></tr><tr><td>GLM-4</td><td></td><td>输入：1</td><td>未降价</td></tr><tr><td rowspan=\"3\">字节跳动</td><td rowspan=\"3\">5月15日</td><td rowspan=\"2\">豆包Pro-32K</td><td>输出：1 输入：/</td><td>输出：1 输入：0.008</td><td rowspan=\"2\">首次发布</td></tr><tr><td>输出：/</td><td>输出：0.02</td></tr><tr><td>输入：/ 豆包Pro-128K</td><td></td><td>输入：0.05</td></tr><tr><td rowspan=\"3\">阿里巴巴</td><td rowspan=\"3\">5月21日</td><td rowspan=\"2\">Qwen-Long</td><td>输出：/</td><td>输出：0.09</td><td rowspan=\"2\">首次发布 输入：97%</td></tr><tr><td>输入：0.2</td><td>输入：0.005</td></tr><tr><td>输出：0.2</td><td></td><td>输出：0.02 输出：90%</td></tr><tr><td rowspan=\"7\">百度</td><td rowspan=\"6\">5月21日</td><td rowspan=\"2\">Qwen-Max ENIRE Lite</td><td>输入：1.2 输出：1.2</td><td>输入：0.4 输出：1.2</td><td rowspan=\"2\">输入：67% 输出：未降价</td></tr><tr><td>输入：0.03</td><td>输入：免费</td></tr><tr><td rowspan=\"2\"></td><td>输出：0.06</td><td>输出：免费</td><td rowspan=\"2\">100%</td></tr><tr><td>输入：0.04</td><td>输入：免费</td></tr><tr><td rowspan=\"2\">ENIRE Speed</td><td>输出：0.08</td><td>输出：免费</td><td rowspan=\"2\">100%</td></tr><tr><td>输入：0.12</td><td>输入：0.12</td></tr><tr><td rowspan=\"2\"></td><td rowspan=\"2\">ENIRE 3.5 ENIRE 4.0</td><td>输出：0.12</td><td>输出：0.12</td><td rowspan=\"2\">未降价 未降价</td></tr><tr><td>输入：1.2</td><td>输入：1.2</td></tr><tr><td rowspan=\"5\">腾讯</td><td rowspan=\"5\">5月22日</td><td rowspan=\"2\">混元-Lite</td><td>输出：1.2</td><td>输出：1.2</td><td rowspan=\"2\">100%</td></tr><tr><td>输入：0.08</td><td>输入：免费</td></tr><tr><td rowspan=\"2\">混元-Standard</td><td>输出：0.08</td><td>输出：免费</td><td rowspan=\"2\">输入：55%</td></tr><tr><td>输入：0.1</td><td>输入：0.045</td></tr><tr><td rowspan=\"2\">混元-Pro</td><td>输出：0.1</td><td>输出：0.05</td><td>输出：50%</td></tr><tr><td rowspan=\"2\"></td><td>输入：1</td><td>输入：0.3</td><td rowspan=\"2\">输入：70% 输出：未降价</td></tr><tr><td rowspan=\"2\"></td><td>输出：1</td><td>输出：1</td></tr><tr><td rowspan=\"2\">5月22日</td><td rowspan=\"2\">Spark Lite</td><td>输入：0.18</td><td>输入：免费 输出：免费</td><td rowspan=\"2\">100%</td></tr><tr><td>输出：0.18 输入：0.24-0.3 Spark3.5 Max</td><td>输入：0.21</td></tr></table></body></html>", "page_idx": 22}, {"type": "text", "text": "数据：DeepSeek 系列模型的训练过程仍凸显了高质量数据的重要性。例如 V3 模型训练时使用了 14.8 万亿涵盖多种领域和语言的token；R1 通过精心筛选和处理的冷启动数据提升了模型性能和可读性；Janus-Pro在训练时同样较前代模型增加约 9000 万用于多模态理解的样本和约 7200 万用于视觉生成的合成美学数据。结合 RL 范式的可能性，预计高质量数据仍将在模型训练中具有重要意义。", "page_idx": 23}, {"type": "text", "text": "四、投资建议", "text_level": 1, "page_idx": 23}, {"type": "text", "text": "4.1 第十问：DeepSeek 将带来哪些投资机会？", "text_level": 1, "page_idx": 23}, {"type": "text", "text": "算力：算力作为新一轮科技革命的底层基础，将持续受益于千行百业的应用需求。叠加 DeepSeek - R1 为推理范式带来泛化的可能性，预计各厂商技术探索下算力产业链持续高景气。此外，中美AI 竞争加剧，高端算力芯片禁售下自主可控重要性进一步凸显。建议关注以国产算力和AI 推理需求为核心的算力环节，尤其是IDC、服务器、国产芯片等算力配套产业，推荐海光信息、浪潮信息、软通动力，并关注AIDC 相关标的，如并行科技、宝信软件等。", "page_idx": 23}, {"type": "text", "text": "应用：DeepSeek-R1 有望引发新一轮大模型API 降价，小模型通过蒸馏具备强劲推理能力，这也将促使开发者探索更多应用落地的可能性。AI 应用作为新一代生产力工具，看多 C 端软件的持续发展，B 端应用软件商业化进展更快。建议关注 B 端Agent，其中 $_ { \\mathrm { O A + E R P } }$ 作为核心入口，AI 结合更易，有望率先商业化，其次关注用户量多、生态好且可云化的软件公司，推荐金蝶国际、鼎捷数智、赛意信息、用友网络、恒生电子、中控技术等；C 端则关注有较多用户的垂直领域赛道了，其数据价值量更优，推荐金山办公、万兴科技、彩讯股份、同花顺等。", "page_idx": 23}, {"type": "text", "text": "端侧：小模型能力提升同样促进了端侧模型部署，我们看好AI 终端作为新一代计算平台爆发可能。首先，我们认为 $\\mathbf { A } \\mathbf { I } +$ 教育作为高频应用场景有望率先落地，特别教育部人工智能赋能教育行动陆续推进，有望带动AI 学习机、AI 教育大屏等需求增加，推荐视源股份、科大讯飞等；其次，我们认为AI 眼镜、AIPC、机器人等新终端的出货量有望随着模型升级后使用范围的增加而增加，因此建议关注以AI 眼镜、PC、机器人为代表的终端供应商或内部核心软件供应商，推荐虹软科技、联想集团等。", "page_idx": 23}, {"type": "text", "text": "数据：高质量数据仍然是大模型训练中不可或缺的一环，B 端 Agent 落地亦需要行业know-how 进行微调。建议关注向量数据库相关公司、数据处理类企业，以及具备行业侧专业数据的厂商，关注拓尔思等。", "page_idx": 23}, {"type": "text", "text": "风险分析", "text_level": 1, "page_idx": 24}, {"type": "text", "text": "（1）AI 产业商业化落地不及预期：目前各环节AI 产品的商业化模式尚处于探索阶段，如果各环节产品的推进节奏不及预期，或对相关企业业绩造成不利影响；（2）市场竞争风险：海外 AI 厂商凭借先发优势，以及较强的技术积累，在竞争中处于优势地位，如果国内 AI 厂商技术迭代不及预期，经营状况或将受到影响；同时，目前国内已有众多企业投入 AI 产品研发，后续可能存在同质化竞争风险，进而影响相关企业的收入；（3）政策风险：AI 技术的发展直接受各国政策和监管影响。随着 AI 在各个领域的渗透，政府可能会进一步出台相应的监管政策以规范其发展。如果企业未能及时适应和遵守相关政策，可能面临相应处罚，甚至被迫调整业务策略。此外，政策的不确定性也可能导致企业战略规划和投资决策的错误，增加运营的不确定性；（4）地缘政治风险：在全球地缘政治环境的波动下，尤其美国对中国的出口限制或将直接影响国内企业算力芯片的获取，进而影响其产品研发和市场竞争力。同时，地缘政治风险也可能导致 AI 产品开拓海外市场面临障碍，影响相关企业的营收情况。", "page_idx": 24}, {"type": "text", "text": "分析师介绍", "text_level": 1, "page_idx": 25}, {"type": "text", "text": "应瑛 ", "text_level": 1, "page_idx": 25}, {"type": "text", "text": "中信建投证券计算机行业首席分析师，伦敦国王学院硕士，5 年计算机行业研究经验。2021 年加入中信建投，深入覆盖医疗信息化、工业软件、云计算、网络安全等细分领域。", "page_idx": 25}, {"type": "text", "text": "研究助理", "text_level": 1, "page_idx": 25}, {"type": "text", "text": "李楚涵*******************", "page_idx": 25}, {"type": "table", "img_path": "images/6a77c38e7b35a246014eb4112a3bd8889a6474ab82ec8408add32ce6d3428099.jpg", "table_caption": ["评级说明"], "table_footnote": [], "table_body": "<html><body><table><tr><td>投资评级标准</td><td></td><td>评级</td><td>说明</td></tr><tr><td rowspan=\"5\">报告中投资建议涉及的评级标准为报告发布日后6 个月内的相对市场表现，也即报告发布日后的6个 月内公司股价（或行业指数）相对同期相关证券市 场代表性指数的涨跌幅作为基准。A股市场以沪深 300 指数作为基准;新三板市场以三板成指为基准;</td><td rowspan=\"5\">股票评级</td><td>买入</td><td>相对涨幅15%以上</td></tr><tr><td>增持</td><td>相对涨幅5%-15%</td></tr><tr><td>中性</td><td>相对涨幅-5%一5%之间</td></tr><tr><td>减持</td><td>相对跌幅5%一15%</td></tr><tr><td>卖出</td><td>相对跌幅15%以上</td></tr><tr><td rowspan=\"3\">香港市场以恒生指数作为基准；美国市场以标普 500 指数为基准。</td><td rowspan=\"3\">行业评级</td><td>强于大市</td><td>相对涨幅10%以上</td></tr><tr><td>中性</td><td>相对涨幅-10-10%之间</td></tr><tr><td>弱于大市</td><td>相对跌幅10%以上</td></tr></table></body></html>", "page_idx": 26}, {"type": "text", "text": "分析师声明", "text_level": 1, "page_idx": 26}, {"type": "text", "text": "本报告署名分析师在此声明：（i）以勤勉的职业态度、专业审慎的研究方法，使用合法合规的信息，独立、客观地出具本报告, 结论不受任何第三方的授意或影响。（ii）本人不曾因，不因，也将不会因本报告中的具体推荐意见或观点而直接或间接收到任何形式的补偿。", "page_idx": 26}, {"type": "text", "text": "法律主体说明", "text_level": 1, "page_idx": 26}, {"type": "text", "text": "本报告由中信建投证券股份有限公司及/或其附属机构（以下合称“中信建投”）制作，由中信建投证券股份有限公司在中华人民共和国（仅为本报告目的，不包括香港、澳门、台湾）提供。中信建投证券股份有限公司具有中国证监会许可的投资咨询业务资格，本报告署名分析师所持中国证券业协会授予的证券投资咨询执业资格证书编号已披露在报告首页。", "page_idx": 26}, {"type": "text", "text": "在遵守适用的法律法规情况下，本报告亦可能由中信建投（国际）证券有限公司在香港提供。本报告作者所持香港证监会牌照的中央编号已披露在报告首页。", "page_idx": 26}, {"type": "text", "text": "一般性声明", "text_level": 1, "page_idx": 26}, {"type": "text", "text": "本报告由中信建投制作。发送本报告不构成任何合同或承诺的基础，不因接收者收到本报告而视其为中信建投客户。", "page_idx": 26}, {"type": "text", "text": "本报告的信息均来源于中信建投认为可靠的公开资料，但中信建投对这些信息的准确性及完整性不作任何保证。本报告所载观点、评估和预测仅反映本报告出具日该分析师的判断，该等观点、评估和预测可能在不发出通知的情况下有所变更，亦有可能因使用不同假设和标准或者采用不同分析方法而与中信建投其他部门、人员口头或书面表达的意见不同或相反。本报告所引证券或其他金融工具的过往业绩不代表其未来表现。报告中所含任何具有预测性质的内容皆基于相应的假设条件，而任何假设条件都可能随时发生变化并影响实际投资收益。中信建投不承诺、不保证本报告所含具有预测性质的内容必然得以实现。", "page_idx": 26}, {"type": "text", "text": "本报告内容的全部或部分均不构成投资建议。本报告所包含的观点、建议并未考虑报告接收人在财务状况、投资目的、风险偏好等方面的具体情况，报告接收者应当独立评估本报告所含信息，基于自身投资目标、需求、市场机会、风险及其他因素自主做出决策并自行承担投资风险。中信建投建议所有投资者应就任何潜在投资向其税务、会计或法律顾问咨询。不论报告接收者是否根据本报告做出投资决策，中信建投都不对该等投资决策提供任何形式的担保，亦不以任何形式分享投资收益或者分担投资损失。中信建投不对使用本报告所产生的任何直接或间接损失承担责任。", "page_idx": 26}, {"type": "text", "text": "在法律法规及监管规定允许的范围内，中信建投可能持有并交易本报告中所提公司的股份或其他财产权益，也可能在过去 12 个月、目前或者将来为本报告中所提公司提供或者争取为其提供投资银行、做市交易、财务顾问或其他金融服务。本报告内容真实、准确、完整地反映了署名分析师的观点，分析师的薪酬无论过去、现在或未来都不会直接或间接与其所撰写报告中的具体观点相联系，分析师亦不会因撰写本报告而获取不当利益。", "page_idx": 26}, {"type": "text", "text": "本报告为中信建投所有。未经中信建投事先书面许可，任何机构和/或个人不得以任何形式转发、翻版、复制、发布或引用本报告全部或部分内容，亦不得从未经中信建投书面授权的任何机构、个人或其运营的媒体平台接收、翻版、复制或引用本报告全部或部分内容。版权所有，违者必究。", "page_idx": 26}, {"type": "text", "text": "中信建投证券研究发展部", "text_level": 1, "page_idx": 26}, {"type": "text", "text": "中信建投（国际）", "text_level": 1, "page_idx": 26}, {"type": "text", "text": "北京  \n朝阳区景辉街 16 号院1 号楼18  \n层  \n电话：（8610） 56135088  \n联系人：李祉瑶  \n邮箱：<EMAIL>  \n上海  \n上海浦东新区浦东南路528号南  \n塔 2103 室  \n电话：（8621） 6882-1600  \n联系人：翁起帆  \n邮箱：<EMAIL>  \n深圳  \n福田区福中三路与鹏程一路交  \n汇处广电金融中心 35 楼  \n电话：（86755）8252-1369  \n联系人：曹莹  \n邮箱：<EMAIL>.cn香港  \n中环交易广场 2 期18 楼电话：（852）3465-5600联系人：刘泓麟  \n邮箱：<EMAIL>", "page_idx": 26}, {"type": "text", "text": "", "page_idx": 26}, {"type": "text", "text": "", "page_idx": 26}, {"type": "text", "text": "", "page_idx": 26}]