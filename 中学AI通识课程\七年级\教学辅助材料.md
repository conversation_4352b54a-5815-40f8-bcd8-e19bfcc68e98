# 七年级AI通识课程教学辅助材料

## 📋 数据收集工具

### 1. 校园数据调查问卷模板

#### 学生学习习惯调查表
```
学生学习习惯调查问卷

亲爱的同学：
为了更好地了解同学们的学习习惯，设计智能学习助手，请如实填写以下信息。
所有信息仅用于课程学习，将严格保密。

基本信息：
□ 年级：_______  □ 性别：男/女  □ 年龄：_______

学习时间分配（每天平均时间，单位：小时）：
□ 课堂学习：_______  □ 作业时间：_______
□ 课外阅读：_______  □ 在线学习：_______
□ 体育运动：_______  □ 娱乐时间：_______

学科偏好（1-5分，5分最喜欢）：
□ 语文：_______  □ 数学：_______  □ 英语：_______
□ 物理：_______  □ 化学：_______  □ 生物：_______
□ 历史：_______  □ 地理：_______  □ 政治：_______

学习困难（可多选）：
□ 注意力不集中  □ 记忆力不好  □ 理解能力差
□ 时间管理困难  □ 学习方法不当  □ 缺乏学习动力
□ 其他：_______

希望的智能助手功能（可多选）：
□ 学习计划制定  □ 作业提醒  □ 知识点解答
□ 学习进度跟踪  □ 学习方法推荐  □ 学习伙伴匹配
□ 其他：_______
```

#### 课程评价调查表
```
课程评价调查表

课程名称：_______
授课教师：_______
调查日期：_______

课程内容评价（1-5分）：
□ 内容难度：_______  □ 内容趣味性：_______
□ 内容实用性：_______  □ 内容新颖性：_______

教学方式评价（1-5分）：
□ 讲解清晰度：_______  □ 互动参与度：_______
□ 实践操作性：_______  □ 案例丰富性：_______

学习效果评价：
□ 知识掌握程度：很好/较好/一般/较差/很差
□ 技能提升程度：很大/较大/一般/较小/很小
□ 兴趣激发程度：很强/较强/一般/较弱/很弱

改进建议：
_________________________________
_________________________________
```

### 2. 数据记录表模板

#### 实验数据记录表
```
实验名称：_______
实验日期：_______
实验小组：_______

实验目标：
_________________________________

实验步骤：
1. _________________________________
2. _________________________________
3. _________________________________
4. _________________________________

数据记录：
| 序号 | 测量项目 | 测量值 | 单位 | 备注 |
|------|----------|--------|------|------|
| 1    |          |        |      |      |
| 2    |          |        |      |      |
| 3    |          |        |      |      |
| 4    |          |        |      |      |

实验结果：
_________________________________

问题和发现：
_________________________________

改进建议：
_________________________________
```

#### 项目进度跟踪表
```
项目名称：校园智能助手
小组成员：_______
项目周期：_______

阶段一：问题定义和数据收集
□ 任务1：确定项目目标 完成日期：_______
□ 任务2：设计数据收集方案 完成日期：_______
□ 任务3：实施数据收集 完成日期：_______
□ 任务4：数据质量检查 完成日期：_______

阶段二：数据分析和特征工程
□ 任务1：数据预处理 完成日期：_______
□ 任务2：特征提取 完成日期：_______
□ 任务3：特征选择 完成日期：_______
□ 任务4：特征验证 完成日期：_______

阶段三：模型选择和训练
□ 任务1：算法选择 完成日期：_______
□ 任务2：模型训练 完成日期：_______
□ 任务3：参数调优 完成日期：_______
□ 任务4：模型验证 完成日期：_______

阶段四：评估和优化
□ 任务1：性能评估 完成日期：_______
□ 任务2：模型优化 完成日期：_______
□ 任务3：结果分析 完成日期：_______
□ 任务4：报告撰写 完成日期：_______

阶段五：成果展示
□ 任务1：展示准备 完成日期：_______
□ 任务2：成果汇报 完成日期：_______
□ 任务3：经验总结 完成日期：_______
□ 任务4：项目归档 完成日期：_______
```

## 📊 评价工具

### 1. 学生自评表

#### 课程学习自评表
```
姓名：_______  班级：_______  日期：_______

知识掌握情况（请在相应位置打√）：
                    很好  较好  一般  较差  很差
机器学习概念        □    □    □    □    □
数据收集方法        □    □    □    □    □
特征工程技能        □    □    □    □    □
算法基本原理        □    □    □    □    □
工具使用能力        □    □    □    □    □

技能发展情况：
                    很大提升  有提升  无变化  有退步
数据分析能力        □        □      □      □
问题解决能力        □        □      □      □
团队合作能力        □        □      □      □
创新思维能力        □        □      □      □
表达沟通能力        □        □      □      □

学习态度评价：
□ 我对AI课程很感兴趣
□ 我能够主动参与课堂讨论
□ 我认真完成课后作业
□ 我积极参与小组活动
□ 我愿意帮助其他同学

最大收获：
_________________________________

遇到的困难：
_________________________________

改进计划：
_________________________________
```

### 2. 同伴互评表

#### 小组合作互评表
```
评价者：_______  被评价者：_______
项目名称：_______  评价日期：_______

合作态度评价（1-5分）：
□ 参与积极性：_______
□ 责任心：_______
□ 团队精神：_______
□ 沟通配合：_______

工作贡献评价（1-5分）：
□ 任务完成质量：_______
□ 创新想法贡献：_______
□ 问题解决能力：_______
□ 技术技能水平：_______

具体表现描述：
优点：
_________________________________

需要改进的地方：
_________________________________

建议：
_________________________________
```

### 3. 教师评价表

#### 项目作品评价表
```
项目名称：_______
小组成员：_______
评价教师：_______
评价日期：_______

项目完整性（25分）：
□ 项目目标明确（5分）：_______
□ 方案设计合理（5分）：_______
□ 实施过程完整（5分）：_______
□ 结果分析充分（5分）：_______
□ 文档资料齐全（5分）：_______

技术水平（25分）：
□ 数据收集质量（5分）：_______
□ 特征工程水平（5分）：_______
□ 算法应用正确（5分）：_______
□ 工具使用熟练（5分）：_______
□ 结果解释合理（5分）：_______

创新性（20分）：
□ 问题定义创新（5分）：_______
□ 方法应用创新（5分）：_______
□ 特征设计创新（5分）：_______
□ 结果展示创新（5分）：_______

团队合作（15分）：
□ 分工合理明确（5分）：_______
□ 协作配合良好（5分）：_______
□ 共同解决问题（5分）：_______

展示效果（15分）：
□ 表达清晰流畅（5分）：_______
□ 逻辑结构合理（5分）：_______
□ 互动回应良好（5分）：_______

总分：_______/100分

评价等级：□ 优秀（90-100）□ 良好（80-89）□ 合格（70-79）□ 需努力（<70）

具体评语：
_________________________________
_________________________________

改进建议：
_________________________________
_________________________________
```

## 🛠️ 技术工具指南

### 1. DeepSeek使用指南

#### 基本操作步骤
```
1. 访问DeepSeek对话平台
   - 网址：https://chat.deepseek.com/
   - 注册账号或使用访客模式

2. 有效提问技巧
   - 明确具体：清楚描述问题和需求
   - 提供背景：说明问题的上下文
   - 分步询问：复杂问题分解为简单问题
   - 验证理解：确认AI的回答是否正确

3. 学习应用场景
   - 概念解释：询问AI技术概念
   - 案例分析：分析具体应用案例
   - 问题解答：解决学习中的疑问
   - 创意启发：获取项目创意和建议

4. 安全注意事项
   - 不透露个人隐私信息
   - 不完全依赖AI的回答
   - 对重要信息进行验证
   - 理性看待AI的能力和局限
```

### 2. Excel数据分析指南

#### 基本功能使用
```
1. 数据输入和整理
   - 规范的数据格式
   - 数据类型设置
   - 缺失值处理
   - 重复数据删除

2. 基本统计分析
   - 求和：SUM()
   - 平均值：AVERAGE()
   - 最大值：MAX()
   - 最小值：MIN()
   - 标准差：STDEV()

3. 数据可视化
   - 柱状图：比较不同类别数据
   - 折线图：显示趋势变化
   - 饼图：显示比例关系
   - 散点图：显示相关关系

4. 数据透视表
   - 数据汇总分析
   - 多维度统计
   - 动态数据筛选
   - 交叉分析表
```

### 3. Teachable Machine使用指南

#### 操作步骤
```
1. 访问平台
   - 网址：https://teachablemachine.withgoogle.com/
   - 选择项目类型：图像、音频或姿态

2. 数据收集
   - 创建分类标签
   - 上传或拍摄训练样本
   - 确保样本数量充足
   - 保证样本质量良好

3. 模型训练
   - 点击"训练模型"按钮
   - 等待训练完成
   - 观察训练进度和结果

4. 模型测试
   - 使用测试数据验证模型
   - 分析预测结果
   - 评估模型准确性
   - 记录测试结果

5. 模型导出
   - 下载训练好的模型
   - 获取模型使用代码
   - 集成到其他应用中
```

## 📚 参考资源

### 1. 在线学习资源
- **国家中小学智慧教育平台**：https://www.zxx.edu.cn/
- **3Blue1Brown神经网络系列**：YouTube/Bilibili
- **机器学习入门课程**：Coursera、edX
- **AI4K12教育资源**：https://ai4k12.org/

### 2. 工具和平台
- **编程学习**：Scratch、Python Turtle
- **数据分析**：Excel、Google Sheets
- **AI体验**：DeepSeek、ChatGPT
- **模型训练**：Teachable Machine、MIT App Inventor

### 3. 数据集资源
- **教育数据集**：学生成绩、课程评价
- **生活数据集**：天气、交通、消费
- **开源数据集**：Kaggle、UCI机器学习库
- **模拟数据集**：教学专用的简化数据

### 4. 案例库
- **成功案例**：著名的机器学习应用
- **失败案例**：常见的错误和教训
- **学生作品**：往届学生的优秀项目
- **行业应用**：不同领域的AI应用实例

---

*本辅助材料旨在为七年级AI通识课程提供完整的教学支持，包括数据收集工具、评价表格、技术指南和参考资源，帮助教师和学生更好地开展课程学习和项目实践。*
