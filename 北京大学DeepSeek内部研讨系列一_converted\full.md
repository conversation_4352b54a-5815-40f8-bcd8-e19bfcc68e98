# DeepSeek内部研讨系列

DeepSeek与AIGC应用

AI肖睿团队（孙萍、周嵘、李娜、张惠军、刘誉）2025年2月20日

#

• 北大青鸟人工智能研究院• 北大计算机学院元宇宙技术研究所• 北大教育学院学习科学实验室

1. 本次讲座为DeepSeek原理和应用系列研讨的讲座之一，主要介绍DeepSeek的基本概念，以及大模型技术和AIGC工具应用，不需要大家具备专业的AI或IT技术背景。

2. 本次讲座首先分析当前备受瞩目的DeepSeek-R1 的概念、优势和历史地位。然后进一步探讨大模型和AIGC的底层工作机制，旨在帮助读者突破工具应用的局限，理解DeepSeek和AIGC的深层次价值。最后，介绍如何科学选择与高效使用AI 工具，为大家提供更具深度与实用性的应用场景的指导，给听众带来更落地的AI应用价值。

3. 尽管DeepSeek-R1 以其低成本和开源策略为行业带来变革，但当前网络上的大量相关内容仅停留在工具应用层面，易对初级AI应用人员造成概念和思维方式的误导，这也是本次讲座希望解决的问题。

# 目  录

01 详解DeepSeek R1 02 AIGC的概念和应用

03 AIGC的能力揭秘 04 选择AIGC工具

PART 01

# 详解DeepSeek-R1

本部分介绍了DeepSeek-R1模型的技术特性、发展历程、应用场景及其在AIGC领域的定位。

首先介绍人工智能的发展历程，以及大模型相关术语，并对比DeepSeek-R1与其他模型的性能表现。DeepSeek-R1以其低成本、开源策略和卓越的推理能力脱颖而出，尤其在复杂逻辑推理、数学和编程任务中表现优异。

随后介绍DeepSeek公司的背景、市场定位以及DeepSeek-R1的技术原理和应用场景，揭示了其在推理密集型任务、教育、科研、知识应用和文档分析等领域的独特优势，并列举接入该模型的第三方应用。

通过对人工智能发展历史以及DeepSeek-R1的介绍和分析，本部分旨在为听众提供一个对DeepSeek的客观、全面的认识，并理解该模型在AIGC领域的重要地位和应用潜力。

![](images/189edff9ccb316b59e3d3416a910261fd790f5841e37ff7955635509378c97de.jpg)

# AIGC的发展历程

1950年1950s-1990S1966年 早期萌芽阶段1984-1986年2007年 1990s-2010S沉淀积累阶段2012年限于算法瓶颈，无法直接生成内容·2014年 2010s-2022年快速发展阶段2018年2019年  
2022年

# 大模型相关术语

多模态

文本、图片、音频、视频

AI工具（国内）➢ DeepSeek、 豆包、Kimi、腾讯元宝、智谱清言、通义千问、秘塔搜索、微信搜索...

$\bullet$ 通用模型

大语言模型 （LLM，Large Language Model）  
视频模型  
多模态模型

行业模型 （垂直模型、 垂类模型） ➢ 教育、医疗、金融等

文心一言 通义千问 腾讯混元 商汤日日新 BlueLM 360智脑 天工 MiLM 中科闻歌 紫东太初 舟科技  
通用闭源 ? 字节豆包 Kimi.ai li MINIMAX 8 从科技 penbayes Transn传神云和声山 百川智能 Y 零一万物 盘古大模型 OPPo AndesGPT ZTE中兴 讯飞星火 天翼AI  
通用开源 Qwen2.5 Q GLM-4 贝 面小锅炮M MiniCPM Yi 川 RWKV-LM TeleChat2-35B 书生·浦语  
推理 QWQ-32B-Preview DeepSeek-R1-Lite 福 InternThinker k0-math 360gpt2-01 Ll.aVA-CoT  
实时交互 星火极速 智谱清言 海螺AI C 豆包 文小言 通义APP 日日新 KKimi 语音合成/声音复刻  
文生视频 可灵AI 即梦AI 清 Vidu Pixverse 海螺AI HiDream.ai 通义万相 O Doubao-语音合成 6 百度TTSC 讯飞语音合成 女 CosyVoice  
视觉理解 腾讯混元 阶跃星辰 Qwen2-VL Doubao-vision SenseChat-Vision 海螺AI GLM-4v 书生·万象  
文生图 即梦AI 混元-DiT 快手可图 讯飞星火 meitu 通义万相 文心一格 Fih Audo speech-01医疗 汽车 教育 金融 工业 更多行业百度灵医 理想MindGPT MathGPT 蚂蚁金融大模型 奇智孔明Alnno-15B 营销： 探迹SalesGPT  
部 A科 作业帮 妙想金融大模型 为盈古工业 文化： 文a大模型小轩辕大模型讯飞晓医 易车大模型 子曰 HithinkGPT 羚羊工业大模型 AI4S:DP深势分子大模型…. . ：

#

# 准备期

ChatGPT发布，全球范围内迅速形成大模型共识。

GPT4发布，进一步掀起大模型研发 热潮。

国内快速跟进大模型研发。文心一言1.0、通义千问、讯飞星火、360智脑、ChatGLM等首批模型相继发布。

# 跃进期

Llama2开源，极大助力全球大模型开发者生态。

GPT-4Turbo、Gemini等海外大模型发布，继续提升模型性能。

Midjourney发布5.2 StableDiffusion XL发布

国内闭源大模型快速发展。豆包、混元商量3.0、盘古3.0、AndesGPT、BlueLM、星火3.0、KimiChat等陆续发布。

国内开源生态爆发。Baichuan、Qwen、InternLM、ChatGLM3、Yi-34B等系列模型引领开源热潮。

# 繁荣期

OpenAl发布Sora，极大拓展了Al在视频领域的想象力。

GPT-4o、Claude3.5、Gemini1.5、Llama3发布，海外进入“一超多强”的竞争格局。

国内多模态领域进展迅速，在部分领域领先海外。视频生成模型可灵AI、海螺视频、Vidu、PixVerse等模型陆续发布，并在海外取得较大应用进展。

国内通用模型持续提升。Qwen2.5、文心4.0、GLM4、商量5.5等通用模型陆续更新。

# 深化期

OpenAI发布o1，强化学习新范式，实现推理等复杂能力上的重大突破。

Claude3.5-Sonnet发布，在代码和Agent能力上掀起效率革命。

ChatGPT上线实时视频能力，深入语音视觉实时多模态应用场景。

国内推理模型迅速跟进。DeepSeek-R1、QwQ-32B-Preview、Kimi-k1.5、GLM-Zero、Skywork-o1、StepR-mini、讯飞星火X1等模型密集发布。

国内模型性能持续提升。DeepSeek-V3、Qwen2.5、豆包-Pro、混元-Turbo与GLM-4-Plus等系列模型综合能力上持续提升。

# 推理模型

生成模型与推理大模型的对比  

<html><body><table><tr><td>比较项</td><td>GPT-4o (生成模型)</td><td>DeepSeek-R1 (推理模型)</td></tr><tr><td>模型定位</td><td>专注于通用自然语言处理和多模态能力，适合日常对侧重于复杂推理与逻辑能力，擅长数学、编程和自然语言推理任 理、生成、对话等。</td><td>话、内容生成、翻译以及图文、音频、视频等信息处务，适合高难度问题求解和专业领域应用，在中文表达上容易出 彩。</td></tr><tr><td>推理能力</td><td>数学题求解）上准确率较低</td><td>在日常语言任务中表现均衡，但在复杂逻辑推理（如在复杂推理任务表现卓越，尤其擅长数学、代码推理任务，在部 分基准测试（如GPQA）中准确率高于GPT-4o。</td></tr><tr><td>多模态支持</td><td>支持文本、图像、音频乃至视频输入，可处理多种模 态信息。</td><td>当前主要支持文本输入，不具备图像处理等多模态能力。</td></tr><tr><td>应用场景</td><td>适合广泛通用任务，如对话、内容生成、多模态信息 处理以及多种语言相互翻译和交流；面向大众市场和 商业应用。</td><td>适合需要高精度推理和逻辑分析的专业任务，如数学竞赛、编程 问题和科学研究；在思路清晰度要求高的场景具有明显优势，比 如采访大纲、方案梳理；在对中文语言表达和情感表达方面有明 显优势。</td></tr><tr><td>验</td><td>用户交互体 提供流畅的实时对话体验，支持多种输入模态；用户可展示大部分链式思考过程，便于专业用户理解推理过程；界面 界面友好，适合大众使用</td><td>和使用体验具有较高的定制性，但整体交互节奏较慢。</td></tr></table></body></html>

# 推理模型的优劣势

<html><body><table><tr><td>优势</td><td>劣势</td></tr><tr><td>演绎或归纳等推理能力强 (如谜题、数学证明)</td><td>响应速度慢且计算成本高 (需要更多推理时间)</td></tr><tr><td>链式思维推理出色 (善于分解多步骤问题)</td><td>基于知识的任务更容易出错 (容易产生幻觉)</td></tr><tr><td>擅长复杂决策任务</td><td>-处理简单任务的时候效率低 (容易"过度思考")</td></tr><tr><td>-可以呈现思考过程</td><td></td></tr></table></body></html>

<html><body><table><tr><td>DeepSeek R1</td></tr><tr><td>OpenAl o1</td></tr><tr><td>OpenAl o3-mini</td></tr><tr><td>Gemini 2.0</td></tr><tr><td>Grok3</td></tr><tr><td>Kimi 1.5</td></tr></table></body></html>

# 火爆全网的DeepSeek-R1

当地时间1月27日，受中国人工智能初创公司一一深度求索公司（DeepSeek）冲击，美国人工智能主题股票遭抛售，美国芯片巨头英伟达（NVIDIA）股价历史性暴跌，纳斯达克综合指数大幅下跌。

据介绍，R1模型在技术上实现了重要突破——用纯深度学习方法让AI自发涌现出推理能力在数学、代码、自然语言推理等任务上，性能比肩OpenAI的o1模型正式版，且训练成本仅为560万美元，远低于美国科技巨头的数亿美元乃至数十亿美元投入。

DVIDIA.   
美国人工智能主题 股票大跌 deey 冲击？ dleepseer   
央视新闻 2.8万

![](images/531dfdd336120c16e87a1ece66a0e1100bdccbfbb51c3315ff9d7b5159d4fc5c.jpg)

![](images/5b662e4cb13046b916430c08c06a2469b1a946e44916df03537fe7a462508a9f.jpg)  
资料来源：DeepSeek官网，中国银河证券研究院

![](images/08e35bdeafd6bfd701a66fe85c35a441d44b8c5843329d2468ab93137d53df84.jpg)

资料来源：DeepSeek官网，中国银河证券研究院

• DeepSeek-R1的推理能力进入了第一梯队（媲美OpenAI o1）, 但训练和推理成本低、速度快、全部开源• DeepSeek打破了硅谷传统的“堆算力、拼资本”的大模型发展路径

01

02

![](images/d7b3535b0be799f7fa7f3238112c58c8fd3cef35e9e5a0af49c8a5eb67eb3055.jpg)

# 打破垄断

DeepSeek-R1以低成本和开源特性打破以往头部企业巨头割据局面

# 价格下调

DeepSeek-R1的API定价仅为行业均价的1/10,推动了中小型企业低成本接入AI,对行业产生了积极影响

# 推动创新

DeepSeek-R1促使行业开始从“唯规模论” 转向更加注重“性价比” 和“高效能” 方向

# DeepSeek公司

# 公司成立背景与发展历程

DeepSeek，全称杭州深度求索人工智能基础技术研究有限公司，成立于2023年7月17日，是一家创新型科技企业，专注于人工智能基础技术的研究与开发

大语言模型(LLM)的创新应用

DeepSeek专注于开发先进的大语言模型(LLM)和相关技术，旨在通过这些技术推动人工智能在多个领域的应用和创新

# 投资者背景与市场定位

作为由知名私募巨头幻方量化孕育而生的公司，DeepSeek获得了强大的资金支持和行业影响力，幻方量化与九坤投资、明汯投资、灵均投资并称量化私募领域的“四大天王”，管理资金规模均超过600亿元。这为DeepSeek提供了清晰的市场定位和投资者背景

里程碑

2025年1月20日推出DeepSeek-R1推理模型

# DeepSeek产品信息官方渠道

![](images/3cdfddc9257fab6f8fda354058b3c23baf3184dfa0cc4dda1fad01eac7534476.jpg)

QDeepSeek API文档DeepSeek-R1发布，性能对标OpenAl o1正式版今天，我们正式发布DeepSeek-R1，并同步开源模型权重。  
错误码·DeepSeek-R1遵循MIT License，允许用户通过蒸馏技术借助R1训练其他模型。·DeepSeek-R1上线API，对用户开放思维链输出，通过设置 mode1='deepseek-reasoner’即可调用。·DeepSeek 官网与App 即日起同步更新上线。性能对齐 OpenAl-o1正式版DeepSeek-R1在后训练阶段大规模使用了强化学习技术，在仅有极少标注数据的情况下，极大提升了模型推理能力。在数学、代码、自然语言推理等任务上，性能比肩OpenAlo1正式版。

# 新闻：https://api-docs.deepseek.com/zh-cn/news/news250120

官网：www.deepseek.com

![](images/75264e9b50ebab149e666a357518dae2eb61c6398c3b9d52f1878f7e3fd58efe.jpg)

我是DeepSeek，很高兴见到你！

我可以帮你写代码、读文件、写作各种创意内容，请把你的任务交给我吧\~

&深度思考(R1) 联网搜索

![](images/e9af887fc7cad4f49cd61d5af52a78701fd9911bd15f108ca49ac72450deb088.jpg)

![](images/ed453bfd15342c2d41db2dd316135b43456fbf12815c6857e7f30ddf3e6fbf38.jpg)

对话：chat.deepseek.com

# Github:https://github.com/deepseek-ai/

# DeepSeek模型系列

正式发布DeepSeekR1模型，在大模推理模型 型排名Arena宣布开源第二 DeepSeek R1 中其基准测试DeepSeek成 代MoE大模型 Lite预览版正 升至全类别大DeepSeek V2。 模型第三。■2024年1月 2024年9月 2024年12月 ■O O O O O C2023年7月 2024年5月 2024年11月 ■ 2025年1月■发布首个大模 合并Deep- 宣布型DeepSeek SeekCoder DeepSeek V3首个版本上线Seek V2 Chat 并同步开源模型权重。级推出全新的DeepSeekV2.5新模型。

DeepSeek最新的生成模型和推理模型版本对比  

<html><body><table><tr><td>比较方面</td><td>生成模型（V3)</td><td>推理模型 （R1)</td></tr><tr><td>设计初衷</td><td>想要在各种自然语言处理的任务中都重点是为了搞定复杂的推理情况，比如 能表现好，更通用</td><td>深度的逻辑分析和解决问题</td></tr><tr><td>性能展现</td><td>HumanEval编码任务通过率是65.2% 2024的通过率是79.8%</td><td>在数学题、多语言任务还有编码任务在需要逻辑思考的测试里很棒，比如 里表现不错，像Cmath能得90.7分，DROP任务F1分数能达到92.2%，AIME</td></tr><tr><td>应用的范围</td><td>适合大规模的自然语言处理工作，像 对话式AI、多语言翻译还有内容生成 等等，能给企业提供高效的AI方案， 满足好多领域的需求</td><td>适合学术研究、解决问题的应用和决策 支持系统等需要深度推理的任务，也能 拿来当教育工具，帮学生锻炼逻辑思维</td></tr></table></body></html>

![](images/5d87d990559e98eae6e90df41af9182bd19c034ac8415f4aab74e92da90733a8.jpg)

⚫ DeepSeek R1论文：https://github.com/deepseek-ai/DeepSeek-R1/blob/main/DeepSeek_R1.pdf⚫ DeepSeek R1论文图解：https://zhuanlan.zhihu.com/p/20844750193

<html><body><table><tr><td>模型名称</td><td></td></tr><tr><td>DeepSeek-R1-671B</td><td>满血版，能力最强</td></tr><tr><td>DeepSeek-R1-Distill-Qwen-7B</td><td rowspan="3">蒸馏版，能力稍弱</td></tr><tr><td>DeepSeek-R1-Distill-Llama-8B</td></tr><tr><td>DeepSeek-R1-Distill-Qwen-14B</td></tr><tr><td>DeepSeek-R1-Distill-Qwen-32B</td><td rowspan="2">-实际上是增加了推理能力的Qwen或Llama模型</td></tr><tr><td>DeepSeek-R1-Distill-Llama-70B</td></tr></table></body></html>

# 部署DeepSeek-R1 满血版的算力要求和性能

![](images/21b3132d032f87e523470a9846d839303d0265a637113dad1b45e70814957261.jpg)

理科能力强，且准确率高

$\bullet$ 数学推理  
$\bullet$ 代码生成  
$\bullet$ 复杂任务处理

![](images/c78abf33ed5a1b045d925a7c9c399c17bfd85512ab65783b5b3ab314e7ef1640.jpg)

通用能力

R1 的通用能力 （尤其是生成能力） 低于DeepSeek-V3

➢R1的幻觉仍旧比较明显 （可能源于R1的中文语言表达能力更强）

语言混杂

➢R1 在处理非中英文问题时，偶尔会出现语言混杂现象

➢这个现象在R1 Zero版本中更加明显

提示词工程

使用 few-shot 提示可能会降低R1性能

TheresultsareshowninTable1below.

<html><body><table><tr><td></td><td>DeepSeekR1</td><td>DeepSeekV3</td></tr><tr><td>Vectara'sHHEM2.1</td><td>14.3%</td><td>3.9%</td></tr><tr><td>Google'sFACTSw/GPT-4o&Claude-3.5-Sonnet</td><td>4.37%</td><td>2.99%</td></tr><tr><td>Google'sFACTSw/GPT-4o&Gemini-1.5-Pro</td><td>3.09%</td><td>1.99%</td></tr><tr><td>Google'sFACTSw/Claude-3.5-Sonnet&Gemini-1.5-Pro</td><td>3.89%</td><td>2.69%</td></tr></table></body></html>

Table1:Hallucinationratesof DeepSeekR1andV3byvarious hallucination judgment approaches.Lowerhallucinationratesarebetter.

Thus our surprise:consistentlyacross all judgment approaches,Deepseek-R1 is shown to be hallucinatingat significantly higher rates than Deepseek-V3.

使用过多的过程指导指令可能会降低R1的推理能力

推理密集型任务

➢ 编程任务中的代码生成、算法设计，媲美Claude 3.5 Sonet➢ 数学问题求解、科学推理和逻辑分析等需要复杂推理的场景。

$\bullet$ 教育与知识应用

可用于解决教育领域的问题，支持知识理解与解答。  
可用于科研任务的实验设计、数据分析和论文撰写。

⚫ 文档分析与长上下文理解➢ 适合处理需要深入文档分析和理解长上下文的任务，例如复杂信息提取与整合。

$\bullet$ 开放领域问答与写作

➢ 在内容生成、问题回答以及创造性写作中具有广泛应用，例如生成高质量文本或进行内容编辑。

# 如何使用DeepSeek R1

DeepSeek官方网站和官方app

腾讯系 微信AI搜索 腾讯元宝

腾讯ima个人知识库

AI搜索

deepseek深度求 兴 华为云 基流动基流动 科石 基石智翼青云科技giteeAl nVIDIA英伟达华为云ModelArts提供DeepSeek系列 硅基流动提供DeepSeek系列模型的在 青云科技旗下AI算力云服务—基石能基础技术研究有限公司。 模型服务，支持在线训练和推理。 线服务和部署方案。 智算CoresHub上线DeepSeek全系GiteeAl的ServerlessAPI提供 NVIDIAAl Enterprise提供DeepSeDeepSeek开箱的大模型API服务。 列模型，支持API调用、云端部署及私 R1模型的企业级支持。类型：云服务 类型：云服务 有化部署，加速企业快速接入及部署达间服务 达问服务 达间服务 厂商所在地：中国 厂商所在地：美国类型：云服务 厂商所在地：中国 类型：云服务联通云 联通云 [-J 腾讯云 访问服务 类型：云服务 访问服务联通云人工智能计算平台（AICP）提 阿里云PAI平台提供一键部署 腾讯云TI平台支持DeepSeek模型的调 访问服务ee.，练和推理服务， aws groqrogAmazon Bedrock和SageMaker提供 Groq Cloud提供DeepSeek模型的高 Amazon Bedrock和SageMaker提供DeepSeek-R1模型服务。 性能推理服务。 DeepSeek-R1模型服务。访问服务 达问服务 达问服务厂商所在地：中国 厂商所在地：美国 厂商所在地：美国百度智能云 百度智能云 SCNet 纳米Al搜索纳米搜索（360） 类型：云服务 类型：云服务 类型：云服务达问服务 访间服务 访问服务百度智能云文心工作台提供DeepSeek 超算互联网提供DeepSeek模型的在线 基于DeepSeek模型的智能搜索和对话模型的服务，支持多种AI应用场景。 CURSOR LStudio AAmazon Bedrock和SageMaker提供 本地运行和管理DeepSeek等大语言模 印度领先的AI云服务提供商，支持访间服务 达间服务 DeepSeek-R1模型服务。 型的桌面应用。 DeepSeek模型部署。迈问服务 厂商所在地：美国 厂商所在地：美国火山引擎 火山引擎 类型：编译工具 类型：云服务 类型：云服务移动云震泽大模型服务平台上线 达问服务 访问服务 访问服务e开双，力，满、 适用于业，过AP速epse 支持定制用 间 Ollama LLaMA A应用场景。 个模型，并提供了全网最高的限流。轻量级的本地LLM运行工具，支持 高性能C++推理引擎，支持DeepSeek 印度领先的AI云服务提供商，支持类型：云服务 类型：客户端 DeepSeek模型。 等大语言模型。 DeepSeek模型部署。访问服务 迈问服务 访问服务 厂商所在地：美国 厂商所在地：美国类型：部署工具 类型：推理引擎 类型：云服务5 天翼云 天翼云 中国科技器中国科技云 迈问服务 访问服务 访问服务天散智，芯片p的ee 中国电、e 中国科的 CLOUDFLARE CLOUDFLARE学术优化、AI搜索引繁，LaTeX公式识服务，涵盖科研、办公、教育等场 别和表格识别等功能，助力科研文献 在CloudflareWorkers中运行 通过CloudflareAl Gateway访问迈间服务迈间服务 达间服务 访问服务 访问服务

秘塔搜索 纳米AI搜索 知乎直答

# 其它接入DeepSeek-R1的产品

Molly R1（向量智能）

问小白

# 接入DeepSeek R1第三方服务的厂家

互联网大厂：腾讯：腾讯元宝、ima、微信、腾讯云$\bullet$ 百度：百度搜索、文小言$\bullet$ 字节跳动：豆包、扣子、飞书$\bullet$ 阿里：钉钉$\bullet$ 互联网小厂：科大讯飞、知乎、秘塔、纳米AI搜索、AI初创公司：零一万物、阶跃星辰、minmax、AI应用公司（教育类）：学而思、北大青鸟、网易有道、猿辅导、作业帮、$\bullet$ 手机厂商：华为、荣耀、魅族、$\bullet$ 其它云服务平台：三大运营商、云服务商（硅基流动等）

PART 02

# AIGC的概念和应用

本部分着重介绍人工智能生成内容（AIGC）的定义、应用范围及其在各行业的实际影响：

首先梳理AIGC相关的术语，包括AI、AGI、生成式AI和决策式AI等，明确了其在人工智能领域的定位。随后，详细列举AIGC在文本、图像、音频和视频生成方面的多样化应用，并探讨其在电商、新闻传媒、影视、游戏、教育和金融等行业的具体实践，展示AIGC在提升效率、降低成本和增强创新方面的核心价值。

此外，还分析AIGC带来的挑战，如数据隐私、伦理问题、生成质量控制以及对就业结构的影响。通过深入探讨AIGC的应用现状和未来趋势，本部分旨在帮助读者全面理解AIGC的潜力和影响，为应对技术变革提供参考。

本部分的内容参考了《人工智能通识课（微课版）》和相关的视频（B站的思睿观通）

# 初 识 AIGC

![](images/0e88c3162404003a4c978832400af4294add9b0c8642bc57d50d4d5b38a99d88.jpg)

![](images/c26ad0eb3e060227f5f375a76223b6ef2e701183e090c0dd10a86c76d9bd7cc3.jpg)

# 眼花缭乱的名词

![](images/52d22d2efa6291c7265ea7558cf34c3561fa7565482a2e3936e5d395008fca7c.jpg)

![](images/ae48613828c0ad481c09e7cb733126ac756228034beea4f680217811856f3e5f.jpg)

# AI文本

Cursor  
Windsurf  
MarsCode  
GitHub Copilot  
阿里云通义灵码

![](images/abaa8dae69ed6fd66004ea37a294f8692582257891bcda1067e7a1ac96104675.jpg)

• 微软“小冰”出版诗集AI撰写剧本电影《阳春》百万字小说《天命使徒》清华大学的《机忆之地》全景图是一副百米画卷，再现了当下西湖边的景观特色、生活场景，还有一些已经消失的景观，比如曾经闻名遐迩的大佛寺。

![](images/6d2cc0d6a6ebe6912fb3c0311dd06d8b336a173b38d57de1dd182b09715ef27d.jpg)  
太空歌剧院

![](images/c181909974a250e88c7042d1f374f9ec71f7ba3d0c7aa9d1964260ffbe61bafd.jpg)

![](images/0295231d817ae10861b3e1549f37bd372f3e706cd820b795c58a768a80ec0732.jpg)

![](images/c87d895181f8ada29574b764b0a5235bdbe65300bb7d32ff3e6166629bb90123.jpg)

![](images/e8c63137063016ee04b0a877a4c6f39c973441de938a85f0aa667f4e448d2e0d.jpg)

![](images/3afb6980572807cf39f19985ed1fd1d1b51aceafab67d81d2c0593baa3c9c3f3.jpg)

![](images/5a750253b483e3e34c64c3fae9c3a475e9fe9d61eb65655e49413519aa96230f.jpg)

![](images/2ee569a907ad3972a3d12eca6327f4f4f266a6b5a7d071742baacb0069b40943.jpg)  
新西湖繁胜全景图

图中包含5000个建筑，作者白小苏说如果这些建筑都要自己亲手画，一天画10个都要500天。最后他借助于AI技术，在一年内完成了作品

A12-13青未了·新知 20年2月21日星期六清晚款人人皆可周杰伦？AI加持下的音乐创作，颠覆你的想象31:你用A写过歌吗？你用A音乐眼过第一插金吗？AI音乐，正重  
型看音乐产业生态，AI如何在音乐大显身手的？人人皆  
是周杰伦的时代是否已然来临？ 主笔：于梅君 知多一点太魔幻！一句话一张图都能生成一首歌 不少人利用AI音乐“生钱或省钱 乐 意也有  
PIKA  
Pika整新活！  
能在视频中添加任意内容  
真假难辨

![](images/eba27c0b139a0f031f3f19de7195d0037f5984691a9850acc4921e0f8d9ad9ba.jpg)  
语音克隆

![](images/84687f5e3d397f5a617128027d95f80cdfba022f432a5be8585ff5e8dfa26ade.jpg)  
语音对话  
创作音乐

![](images/1a5c5b2ded60777c8f155dfe87bccae5205bf724138b543325e98f4313c9e58f.jpg)  
千秋诗颂

![](images/bdfd88ca01bcb8b90dc106263ec381bcd3195ac35477a65c2ee1d742259d0691.jpg)  
视频特效   
视频添加图片

# AIGC 的 行 业 应 用

![](images/3c6233291fc7e3814413e712c3025d252f984876aa647da99544e8f6f8411b0b.jpg)

![](images/d90e0ca13f446c3af6ea4aa1d8c2e56e04ddb17ae5b2f766460eca11037d3f11.jpg)

![](images/b2756baebea8a7e351e827fc5c2eb1de55fe6394c9a71d676930b08a7cf0c5e8.jpg)

商品3D模型改善购物体验

![](images/1ffb3930befaddce49845b340792f3f720cad78e38b6dcf6d6c955db05253b2d.jpg)  
虚拟主播提升直播带货效率

AI模特 产品拍摄降本增效

![](images/bac77d3d6fb9f3b00ad3af7b41da6f81a9cebea937c47d67e847cd75d8eff8da.jpg)

全流程参与提高效率

![](images/171af661b01c958d304e730329024e3ccc19c16fbf6ae4fd39a24b00b0483f26.jpg)

雪花啤酒虚拟偶像提升品牌宣传效果

# 新闻传媒

AIGC对传媒行业影响包括采编环节、传播环节，通过语音转写、智能写作、智能剪辑等方式提高采编环节的生产效率，在传播环节通过打造AI主播实现智能、高效播报。

采编环节 HH 传播环节 AIGC对传媒行业参与主体的影响传媒  
提高内容生产效率 AI合成主播为核心 山录音语音转写 应用场景拓展  
借助语音识别技术将录音语音转写成 新华社、央视等积极推出AI合成主播，  
文字，有效压缩重复工作，保障新闻 应用场景包括新闻报道、天气预报、时效性 晚会主持等 对传媒机构 对媒体从业者 对传媒用户智能新闻写作 应用功能升级 大幅提高生产效率， 将部分采编、播放 短时间获得更多新  
基于算法自动编写新闻，提高新闻时 AI合成主播开始陆续支持多语种播报和 并带来新的视觉化、 工作自动化，让其 闻，提高获取及时效性 手语播报 互动化体验; 更专注于思考创造 性及便捷性;如将精力集中于深智能视频剪辑 应用形态完善 丰形富式了，新推闻动报传道媒的向 度更报需道发专挥题人报类道精等准 得降受低众传可媒以门参槛与，内使  
通过使用视频字幕生成、视频锦集、 从2D到3D，从口型到面部表情都更加 智媒转变 分析事物、妥善处 容生产，增强参与  
视频拆条、视频超分等视频智能化剪 逼真、丰富理情感的领域; 感辑

# 智能图像修复

![](images/faa5d76ba0d8d955f20cb865bf3412878653f42de46d080cd9175dbaa87e5d19.jpg)

# 前期创作

# 中期拍摄

# 后期制作

![](images/a5a68a5df1dbdecf755523b30f80261e1d72c0b3ebc7cc9a523b23a82f47dc88.jpg)

# 影视剧本文稿创作

# 拓展角色范畴

# 赋能影视剪辑

通过对海量剧本数据进行分析归纳，并按照预设风格快速生产剧本，创作者再进行筛选和二次加工，激发创作者的灵感，缩短创作周期

通过AIGC合成人脸、声音等内容，实现“数字复活”已故演员、替换“劣迹艺人”、高难度动作合成等

![](images/c84908adee34426c44632d81a70d011d4613778a5d9bb0fb13988411f24c2f47.jpg)  
文本生成场景

修复、还原影像，提升影像资料的清晰度；实现影视预告片自动生成；实现将影视内容维度转制，从2D向3D自动转制

# 影视剧本创作平台

# 拓展场景空间

# 实时字幕

![](images/f31e021b4138553c55693a551008c7ba1225293390d4fb7287b554d6fb08cba5.jpg)

AIGC合成虚拟物理场景，将无法实拍或成本过高的场景生成出来，大大拓宽了影视作品想象力的边界，给观众带来更优质的视觉效果和听觉体验

![](images/62e6e0ffbf7be42d58f5ea79d19206b62053f8d587d5a656e0bd1931d11c36e9.jpg)

# 游戏行业

# AIGC游戏研发环节作用

01 增强游戏 特定风格模拟：AI通过模仿职业选手，玩家则感觉像在与真实的职业选手对抗；  
体验 体验 玩法教学：帮助玩家快速熟悉操作与游戏玩法，提高游戏可玩性。  
02 游戏性能 前期平衡性测试：充分模拟玩家在某套数值体系下的游戏体验，提出优化策略；  
功能 测试 游戏功能测试：针对性找出游戏交互的可能性，发现潜在漏洞辅助游戏策划。  
03 NPC角色 • AI创造不同的面孔、服饰、声音甚至性格特征，甚至可同步驱动嘴型、表情等面部变化，达到角色 生成 高度逼真;并通过大量数据模拟人类运动，完成行走、跑步等一系列动作反应。  
04 剧情 AI智能NPC能够分析玩家的实时输入，与玩家动态交互，构建几乎无限且不重复的剧情;  
剧情 生成 AI能够生产相关的图文、音乐等，创造游戏素材，辅以剧情铺排，提升剧情饱满度。  
05 游戏策 让AI感知环境、自身状态并基于特定目标决定当下需要执行的动作，基于特定问题和策略 略生成 场景，自主提出解决方案。  
AI自动化广告投放:目前抖音等平台的广告分发、内容推荐已通过算法实现，且效果较高;

# 游戏运营环节

生成广告素材:素材主要是图像及音视频内容，预计AIGC的加持下广告素材生成会更高效、高质量;  
• 玩家分类提升体验:通过不同玩家的数据，将用户细分类型，为不同类型玩家提供独特的玩法，提高用户体验。

# 其他行业

![](images/d5a124de5d571a7c79113d581194c6703fdf46f07c177c93a290fcc95d43c0cf.jpg)

![](images/15db01970976f7951d87342f7805ea76920483371309c6e8d1ff227542152f15.jpg)

# AIGC 的 未 来

![](images/b7851135852c9ec3ae9b4a5c48a6aa9f22fc81a6cc45855c6c4d6654d56c478f.jpg)  
企业赋能

![](images/7affa1acc417051a74687a68774ec673b228bc308f546af816ad27d2e2dfc2f7.jpg)  
个人赋能

制作虚假信息诈骗

![](images/12bdb22afb6d765a8a18eb6e686625a6e7bb2ae7408fa4e3a30488bf923dcd32.jpg)

“复活”逝者损害已故人的隐私权

![](images/e87b94f50debdc17856a0f269d1c84e55522845eaecf35041a46041a1c8eac9a.jpg)

![](images/250c30b58891c04ddc29b6d4b683cdcb371af38bc37d2ea497b3bab6f92a12db.jpg)

![](images/5fe031993c2ed915ba20ecf35dfe7666d390afd0ec3d58f9302627fa60895db4.jpg)

![](images/c67f583520b561efa87219102880985c2823489b5a2c9bbc9dd8e3c97151af4e.jpg)

技术进步

AIGC的生成质量和准确性将得到提升

# 更广泛的应用

在更多领域得到应用，如保险、医疗和法律服务等

# 人机协作

更多地与人类专家协作，以提供更高质量的内容和服务

# 监管框架的发展

预计将出现更多关于内容生成和使用的监管框架

2023年新兴技术成熟度曲线  
![](images/7130e5f192aac67abcc2c0809b35de93dcc6fa66090409e481e02a0590231c97.jpg)  
数据来源：Gartner（2023年8月）

生成式人工智能 （AI） 目前处于期望膨胀期，预计将在 $2 \sim 5$ 年内产生巨大效益

到 2026 年，Gartner预测超过$80 \%$ 的企业将使用生成式AI的API或模型，或在生产环境中部署支持生成式AI的应用，而在2023年初这一比例不到 $5 \%$ 。

# DemandforhealthcareandSTEMrolescould grow,whiledemandforoffice supportandcustomerservicerolescoulddecline.

Netexpectedchangeinlabordemand,Europe1andUS,faster/midpointscenario,2022-30   
![](images/8991fcce26c799072e07cc2f23f7791e0a0167a1b9633d32d089a18969edf836.jpg)

McKinsey&Company

2024年5月24日麦肯锡报告：《工作的新未来：在欧洲及其他地区部署人工智能和提升技能的竞赛》

# 显著上升

STEM （科学、技术、工程和数学）相关职业医疗保健和其他高技能职业

# 下降显著

办公室职员  
生产工人  
客户服务代表等传统职业

到 2030 年，欧洲和美国多达30% 的工作时间可能实现自动化

Demand for technological and social and emotional skills could increase in Europe.   
![](images/aa04ad55136226f577fa1c9a8300c7a986198932ddb6dc3bb356a243ef7ef391.jpg)  
2024年5月24日麦肯锡报告：《工作的新未来：在欧洲及其他地区部署人工智能和提升技能的竞赛》

技能类型需求变化

技术技能大幅增长  
炙手可热的 “新宠” 技能是社会和情感技能  
体力和手动技能的需求预计将保持大致稳定  
认知技能(文字和信息处理、编程、科研、工程等） 的需求预计将减少 $14 \%$

双牙新大人石器、陶器、胃铜器、铁器、水车、蒸汽机、电灯、电话、汽车、飞机、计算机、互联网、物联网、人工智能

$\bullet$ 学习AIGC基础知识  
$\bullet$ 积极使用AIGC工具  
$\bullet$ 关注AIGC在各行业的应用案例  
$\bullet$ 跟踪最新发展趋势

PART 03

# A I G C 的能力揭秘

本部分深入剖析AIGC背后的技术原理，涵盖文本生成和图像生成两大核心领域。通过对比文本生成和图像生成的不同路径，大家能够全面理解AIGC在不同模态下的工作原理，以及如何利用这些技术实现高效的内容创作和应用。

在文本生成方面，以OpenAI的GPT-4o为例，详细介绍了通用大语言模型（LLM）的工作原理，包括其基于Transformer架构的生成机制、上下文编码、自注意力机制以及预训练和微调过程。探讨了GPT-4o在多轮对话、语言转换、意图理解、文本生成和推理能力等方面的优势，同时也指出其在知识局限、上下文窗口限制和生成幻觉等方面的不足。

在图像生成方面，以Stable Diffusion模型为例，解释了其核心组件（文本编码器、图像信息生成器和图像解码器）的工作流程，揭示了文生图和图生图技术的优势与局限。

本部分的内容参考了《人工智能通识课（微课版）》和相关的视频（B站的思睿观通）。

# 文 本 生 成 的 奥秘

# AIGC与GPT （通用大模型的代表）

1950年1950s-1990S1966年 早期萌芽阶段1984-1986年范围实验2007年 1990s-2010S沉淀积累阶段·2012年限于算法瓶颈，无法直接生成内容●2014年 2010s-2022年快速发展阶段2018年2019年2023年 2022年T4-多模态大模型

# OpenAI GPT的学霸养成记

![](images/f70d39c4f1f780855f5b0ad497753840c45bdccaa4d118442691abb625b527fe.jpg)  
GPT:生成式预训练变换模型(Generative Pre-trained

![](images/cd7fdd366158f1f90b9cee3b7aeea6a864c5e5da53d289ef5951da8413e0426b.jpg)

# 工作原理-1

![](images/c7bb2e468a093a4d8d0f19aee763dd852c4f1b95b78e50aeecd85072ef9ac4c3.jpg)

![](images/c135d32309b106cbd4fcdfccb6cdfac55125cad309e080928c48fb7fef0dc506.jpg)

训练语料 （gpt-3)   

<html><body><table><tr><td>数据来源</td><td>说明</td></tr><tr><td>维基百科</td><td>在线百科,严谨</td></tr><tr><td>图书</td><td>经典为主的古登堡计划和自助出版 平台Smashwords等</td></tr><tr><td>杂志期刊</td><td>论文：ArXiv等</td></tr><tr><td>链接</td><td>WebText,Reddit</td></tr><tr><td>Common Crawl</td><td>开源项目，爬取互联网所有数据</td></tr><tr><td>GitHub</td><td>程序员聚集地</td></tr><tr><td>合计</td><td>700多GB,约有19万套四大名著 的阅读量 5000亿左右的token数量。（13 万亿token:gpt4) 100个标记大约等于75个英语单 词</td></tr></table></body></html>

![](images/9623a2cbf990a06aeea90a2259eee84919b5db21fd333aef50e0298bca840ca9.jpg)

典型的新技能学习曲线：规模到达临界点之后才会迅速增长

模型参数：1.8万亿参数 （GPT-4)

GPT-4o上下文窗口大小：8192个token(标记)

# 生成式通用大语言模型的优势与劣势

# 优势

语言理解和生成能力世界知识能力一定的推理能力

# 劣势

幻觉 （生成错误答案）

知识库有限

上下文窗口限制

![](images/3184a3c2db17e1bce870cfe8f9426eeef833d24424d7c484cfa220acc3dc502c.jpg)

ChatGPT：聊天机器人应用访问地址：https://chatgpt.com/

# GPT：大语言模型GPT-3.5、GPT-4o

![](images/e649379974365d2301d8d7db07c93929facfe97e7b0dd08ee48bd1020625a184.jpg)

Upgrade your plan   
Free + Plus : Team Upgrade to Plus UpgradetoTeam   
√Assistance with writing, problem √Early access to new features Everything in Plus,and: solving and more √Access toGPT-4, GPT-4o, GPT-3.5 √Higher limits for GPT-4, GPT-4o,and   
Access toGPT-3.5 √ Up to 5x more messages for GPT-4o tools like DALL·E image generation,   
√Limited access toGPT-4o √Access to advanced data analysis, file adanceddataaalsisowing   
√Limited access to advanced data uploads, vision,and web browsing √Create and share GPTs with your anawsinleoanwe √DALL·E image generation workspace √Create and use custom GPTs √Admin console for workspace management √Team data excluded from training by default. Learn more

原理限制使用建议

上下文编码自注意力机制

怎么给朗朗过十岁生日？ ChatGPT  
怎么给朗朗过十岁生日？可 ChatGPT  
怎么给朗朗过十岁生日？可以 ChatGPT 通  
怎么给朗朗过十岁生日？可以通 ChatGPT  
怎么给朗朗过十岁生日？可以通过 ChatGPT  
怎么给朗朗过十岁生日？可以通过以 ChatGPT  
怎么给朗朗过十岁生日？可以通过以下 ChatGPT

# 上下文窗口限制

• 问题 $^ +$ 回答总数包括字符、标点、空格

• GPT-3.5：4096，大约3000 个英文单词或 4000 个汉字  
GPT-4：8192，大约6000 个英文单词或 8000 个汉字  
（https://platform.openai.com/tokenizer）

分段对话定期总结使用关键词提醒精简输入

不同语言限制使用建议

人类语言之间转换机器语言之间转换人类语言与机器语言之间转换

人类语言生成能力不同前5名：英语、西班牙语、法语、德语、中文机器语言生成能力不同前4名：python、Java、Typescript、Javascript

GPT:使用英文中文优先选择国内产品编程尽量选用前4种语言

# 对话能力--意图和情感分析能力

能力限制使用建议

真实意图  
情绪识别  
判断行为特点  
重要他人

缺乏真正的情感上下文理解有限缺乏深层次推理提供清晰具体的信息简短和连贯的对话使用明确的情感表达

![](images/db0ee84ec01247ad6dcae2460385cda6fd7f127b865cf99e0a6ccf544afc8c55.jpg)

# 创作能力-文本分析能力

叫

# 文本统计

# 限制

# T

统计字符数、单词数、句子数等基本统计信息

# 文本摘要

从长文档中提取主要信息，并生成一个简短的概述

知识更新、长文本理解、数据偏见、数据质量、语境歧义、领域专业知识、新颖和未知类别、不确定性

# 文本分类

&

新闻分类、情感分析 （正面、负面、中性）

# 文本总结

文本的整体内容进行简要概述，通常比文本摘要更为简短

# 创作能力-文本润色能力

# 文本校对

错别字识别与修改、语法错误检查与修正、标点符号调整、词汇选择优化、语句重组与调整、保持一致的写作风格

# T

# 文本改写

口语转书面语、不同体裁风格、语义保持、文本简化、文本优化、保持一致性

# T

# 文本扩写

增加细节、举例说明、提供背景信息、解释专业术语或概念、描述过程或步骤、增加文学修辞、提供多种观点等

# 限制

新词汇与表达、语境误判、高度主观性、文化敏感性和幽默、 不断更新的语言规则、知识更新限制、 长文本处理能力、用户意图识别

# 四 使用优先级

文本分析 $>$ 文本润色>文本生成

分段处理长文本

明确和具体的指示

M

补充背景信息

注意多义词和模糊表达

# 对比能力

对比分析：事物、概念、人物、事件、文学作品、艺术作品等

品

# 列举能力

简单列举任务：组合/排列/序列，简单数学问题等

# 假设推理能力

多个领域能力不同：历史事件、文学作品解读等

# 限制

主观判断、上下文窗口、非专业算法、数据局限、缺乏常识和领域专长、模型理解能力、过度生成偏见、难以验证答案的准确性

# 使用建议

明确问题和期望补充背景信息结合领域知识

# 基本对话和多轮对话

# 基本对话

# 多轮对话

# 学会提问

明确、具体、避免歧义  
逻辑清晰  
上下文信息  
示范数据

追问 澄清 引导 凹

# 验证输出

从回复中找到有价值的部分判断结果质量：初步识别可靠信息评估信息准确性：与其他来源进行对比处理错误信息：指出错误，重新提问

# 图 像 生 成 的 奥 秘

# Stable Diffusion 模型如何实现图像生成？

![](images/f21df74baf5ffe24677dab1477bbdbcce32bedf327135c7e4b24cf1842d130a3.jpg)

# Stable Diffusion 模型的构成

![](images/2983fc1f6ae106ed88417bb595982cf0688c7a523d858e660823db727185df00.jpg)

![](images/bcbda597b9fd22a8fe6b37f73b3433735267af95fff12e303f2dd7026139902d.jpg)

# 图像信息生成器

• 将输入信息逐步处理和转换为图像数据

![](images/13e84a5d17cf3a3dea32eca44f9ed0dda1515eebbb35d59cfd2e30d0056b502d.jpg)

# 图像解码器

• 接收来自图像信息生成器的处理后的信息矩阵• 将信息矩阵转换为可视化的图像

![](images/fd79857eac4a53cbe449fc7d071fdc9afa8c0ff4934790b6641bdba94215af54.jpg)

![](images/05e6f9ad1380831deb89d080d60310c0bd77d0da99d32dc4a907e2673d584720.jpg)

# 优势

降低门槛  
提高效率  
艺术风格多样化

# 局限

精确控制困难随机性太强复杂场景理解

PART 04

# 选择AIGC工具

本部分聚焦于如何科学选择和高效应用AIGC工具，以满足不同场景下的多样化需求。

首先介绍当前AIGC领域的工具类型，包括聊天对话机器人、图像生成工具、音频与视频生成工具以及搜索工具等，并列举了国内外代表性工具。

随后，以DeepSeek-R1、Kimi、豆包、腾讯元宝等具体工具为例，详细分析了它们的特点、优势及适用场景，展示了不同工具在推理能力、多模态支持、长文本处理和搜索效率等方面的差异。

最后，还提出了选择AIGC工具的依据，包括明确需求、评估工具性能和考虑使用成本等，帮助读者根据自身需求做出合理选择。通过深入分析和对比，本部分旨在为读者提供一份实用的AIGC工具选择指南，助力其在AIGC时代更好地应用人工智能技术，提升工作效率和创新能力。

# AIGC工具

全球 Ai 产品名 网站（web）分类 12月上榜网站 12月上榜网站  
排名 产品 AI产品榜 aicpb.com Web访问量 变化  
讯混元 腾讯混元 LLM 867.14K $3 5 1 . 1 7 \%$   
2 DeepSeek AlChatBots 11.01M $1 6 0 . 6 3 \%$   
3 i搜 AI搜百度 AlChatBots 11.86M $9 3 . 0 4 \%$   
4 文心智能体平台（百度）AgentsPlatform 617.86K $8 2 . 5 2 \%$   
5 豆包|抖音 AlChatBots 31.77M 48.28%  
6 即梦AI剪映 AlImageGenerator 2.19M 45.58%  
7 火山方舟 ModelTraining&Dep 3.49M $3 2 . 8 4 \%$   
8 Marscode AlCodeassistant 1.11M $2 9 . 3 3 \%$   
9 纳米AI搜索|原360Al搜AISearchEngine 359.33M 27.10%  
10 中 扣子 AlChatBots 2.16M 23.79%  
11 腾讯元宝 AlChatBots 1.54M 20.68%  
12 R Kimi|月之暗面 AlChatBots 38.37M 16.93%  
13 川 墨刀AI AlDesign Tool 2.27M 13.35%  
14 D 亿图脑图 AlMindMapGenerat 589.6K 11.83%  
15 Ub liblib.art Model Trainina&Den 2.4M 10.71%  
全球 Ai 产品名 网站(web)分类 12月上榜网站12月上榜网站  
排名 产品销 AI产品榜 aicpb.com Web访问量 变化  
New Bing Al Search Engine 1.87B 2.31%  
2 纳米AI搜索丨原360AlAI Search Engine 359.33M 27.10%  
3 迷 Perplexity Al Al Search Engine 97.54M -9.55%  
4 ^ 秘塔AI搜索 Al Search Engine 8.32M 3.94%  
5 You Al Search Engine 5.54M -12.36%  
6 C知道 Al Search Engine 4.66M -2.03%  
7 Pim&yes Pimeye Al Search Engine 4.64M 2.73%  
8 O iAsk.Al Al Search Engine 4.22M -1.41%  
9 kagi Kagi Search Al Search Engine 3.44M 15.61%  
10 C Consensus Al Search Engine 3.37M -0.45%  
11 D 知乎直答 Al Search Engine 3.36M 3.40%  
12 1 GenSpark Al Search Engine 2.76M 28.92%  
13 Q felo.ai Al Search Engine 2.25M 5.49%  
14 scite_ Scite_ Al Search Engine 1.48M -1.14%

AI工具导航平台：https://www.aigc.cn

AI工具集：https://ai-bot.cn

$\hat { \mathbf { \Omega } } ^ { \bullet \bullet }$ AI工具集  
AAI应用集 Kim智的能助手 飞尾出的美ha Cop推出的网页版c. ChatATA对话工具 Bing推的版结合 D 钉钉个人个人服2 AI写作工具  
A图像工具 6 文心一的基于文心 腾讯元的免费A智 Gogl出的天 Meta费A 应百小能推出的免费 ClaudPr的最为有力AI视频工具  
A办公工具 Le Chat @ 海螺问问 紫东太初 X Grok 女 通义干问 ® 豆包Al设计工具  
AI对话聊天 智谱清言 ? 小悟空 跃问 商量SenseChat @ 冒泡鸭 应事AI<> A编程工具 智谱AI推出的生成式A. 字节跳动推出的免费A. 商汤科技推出的免费A.

<html><body><table><tr><td>工具类型</td><td>国内</td><td>国外</td></tr><tr><td>聊天对话机器人</td><td>DeepSeek、腾讯元宝、豆包、Kimi、智谱清言...</td><td>ChatGPT，Claude，X.com (Grok)</td></tr><tr><td>图像生成工具</td><td>通义万相、即梦、豆包</td><td>ChatGPT， Midjourney， Stable Diffusion</td></tr><tr><td>音频工具</td><td>音疯、网易天音、海绵音乐</td><td>Suno</td></tr><tr><td>视频生成工具</td><td>SkyReels、Vidu、可灵、海螺AI、即梦</td><td>Sora， Runway， Pika</td></tr><tr><td>搜索工具</td><td>秘塔、纳米AI、天工</td><td>Perplexity</td></tr></table></body></html>

腾讯元宝 https://yuanbao.tencent.com/

![](images/f8de218e87395628fa5c82341a6c25590345489aaafd4b702113bc6d83dc22d8.jpg)

# 豆包 https://www.doubao.com

你好，豆包让我们一起度过美好的一天！

![](images/4752820425e76d2e8f98df74f91f18273c12d888691612067bf3978973ea6d8b.jpg)

赛博朋克风格的城市街头，霓虹灯闪烁，街上的人们，穿着奇特

![](images/948f56b801cfb16ae5a2574655120ea7720fd38de149b58ffb5e407958718b5c.jpg)

Kimi https://kimi.moonshot.cn/

Hi，我是Kimi\~很高兴遇见你！你可以随时把网址或者文件目发给我，我来帮你看看

Hi，我是Kimi\~很高兴遇见你！你可以随时把网址或者文件发给我，我来帮你看看

# 请整理这本书的核心内容

# 帮我找10篇最近一年内关于AI人才用人需求的报告

LLMBook PDF, 10.3 MB

# 找到了10篇资料作为参考：

A超出字数限制，Kimi只阅读了前44%

这本书《大语言模型》由赵鑫、李军毅、周昆、唐天一和文继荣著作，主要围绕大语言模型（LargeLanguageModels，LLMs）的技术进展、理论基础、关键技术、以及应用实践进行了全面介绍。以下是书中的核心内容概述：

1.大语言模型的发展历程：从统计语言模型到神经网络语言模型，再到预训练语言模型，直至大语言模型的崛起，书中详细回顾了语言模型的演进历程  
2.大模型技术的背后技术：深入探讨了支撑大模型的技术，包括Transformer架构、预训练任务的设计、模型的可扩展性等。  
3.OpenAI的技术变革：分析了OpenAI在大模型技术发展中的作用，特别是GPT系列模型的研发历程，以及其在技术进步中的关键贡献。  
4.大模型训练的挑战：讨论了训练大模型时面临的算力限制、数据质量与规模的重要性，以及科研

您好，我找到了一些关于AI人才用人需求的最新报告，以下是部分摘要和链接：

1.麦肯锡全球人工智能最新调研：AI在中国企业的落地进展如何..1  
2.全球AI人才报告：清华第三，北大第六，硅谷40万人大.2  
3.清华AI人才报告：AI专业高校两年翻四倍..3  
4.2024年中国人工智能行业人才供需情况分析：人才短缺现象存在..4  
5.在华企业如何填补AI人才缺口：麦肯锡大中华区报告5  
6.2024年中国人工智能行业人才供需情况分析：人才短缺现象存在.6  
7.工业和信息化部人才交流中心：《人工智能产业人才发展报告（2019-2020年）》7  
8.猎聘发布《AIGC及其产业链人才需求大数据报告2023》：猎聘报告8  
9.人工智能全域变革图景展望：跃迁点来临(2023) $\because$ KPMG报告10

这些报告覆盖了AI人才的全球分布、中国AI人才的培养和需求、以及企业如何填补AI人才缺口等主题。您可以点击链接获取更详细的内容。

# 超长上下文

一次性阅读50份文档支持20万字上下文输入

网络搜索提高搜索效率减少幻像

# AIGC工具现场演示

![](images/0c101417239ec4bfc06678529934e641674e5ab898201505b560af2bb8865ded.jpg)

# 功能：

网络 推测为transtormerdecoder堆层，实现多模态输入的统一。

# 行业影响与思考

长视频内容（B站/youtube）的总结$\bullet$ 新闻报道行业报告

全网最详细：OpenAlGPT-4o多模态大模型深度解读#大模型#OpenAl#GPT-4o介绍OpenAI春季发布 相关技术回顾 GPT-4o技术深度解读 大模型趋势思考弹幕列表！关于本内容是晨一点整I.OpenAl春季发布：GPT-4o (o foromni)B2.相关技术回顾：Whisperv3-SORA-GPT43.GPT-4o技术畅想：E2E多模态大模型看大模型趋势：百模厂商的冲击&&产业思考T ZLm 自动信冰红茶冰红茶自春机

总结网页或PDF文档"去Kimi继续对话"

# 适用：

1.大语言模型（LLM）：进入瓶颈期，未来可能更多  
2.多模态（MM）：2024年成为多模态元年，国内外  
3.百模大战：国内厂商在多模态领域布局较少，需加快认知迭代速度。  
4.计算产业：多模态大模型对服务器算力和推理芯片有新需求。

C

# 结语

介绍大模型热点和新模型内容。

# 附加信息

视频链接：BV1Uy411Y76z这份笔记总结了视频的核心内容，包括GPT-4o模型的特笔记能帮助您更好地理解视频内容。

# Kimi Copilot插件

音乐工具：音疯 https://www.yinfeng.cn/create

![](images/47532869980d2e1e5c988657cd36a117179e63ec86d65b6a74ce0fd516fc4788.jpg)

视频生成工具：Vidu https://www.vidu.cn/

![](images/abb79b58ec97d3ecd969d693a45a08b1beb6fd53da36d20322edfbc4cca1348a.jpg)

一个3D形象的男孩， 穿着飞行夹克， 在公园滑滑板

![](images/a349dfa3d964354b90a5123f24bfb608805f226b91341fd17b8b91360e9dd0ed.jpg)

# 搜索工具：秘塔 https://metaso.cn

![](images/800009c2fa94974172bc073d255f7e00914ed3e659e9de80654b04f3038b2c21.jpg)

# 今年最热门的科技产品或应用程序有哪些？ $\Theta$

全网 文库 学术 图片 播客

$e ^ { 0 }$

来源

今年最热门的科技产品和应用程序包括：

1.AI眼镜：在CES2025大会上，AI眼镜成为焦点，多家厂商如雷鸟创新、歌尔股份、仙瞬科技等推出了新款智能眼镜产品。例如，雷鸟创新发布的雷鸟V3AI拍摄眼镜集成了多种功能，包括翻译、AI识别和音乐等IPDFIPDF。

社会服务行业投资分析超配（维持）一 -2025

2.虚拟现实（VR）和增强现实（AR）技术：这些技术正在改变游戏、教育、医疗等领域提供更高分辨率和更低延迟的沉浸式体验 $\textcircled{4}$

最新科技产品有哪些

![](images/****************************************8a0952e66e8c1c1524d4fc07.jpg)

# 如何选择AIGC工具

1. 明确自身需求  
2. 评测工具能力  
3. 关注使用成本

<html><body><table><tr><td>AIGC工具</td><td>工具特点</td><td>大模型</td></tr><tr><td>DeepSeek</td><td>文本模态、推理能力强</td><td>DeepSeek-R1</td></tr><tr><td>豆包</td><td>多模态、语音情感能力强</td><td>火山</td></tr><tr><td>Kimi</td><td>多模态、超长文本、搜索和推理能力强</td><td>Kimi-1.5</td></tr><tr><td>智谱清言</td><td>多模态</td><td>GLM-Zero-Preview</td></tr><tr><td>通义干问</td><td>效率工具、代码能力强</td><td>Qwen2.5-Max</td></tr><tr><td>腾讯元宝</td><td>可以使用微信生态</td><td>接入：DeepSeek-R1</td></tr></table></body></html>

# 如何提升AIGC使用能力： 持续更新自己的AI工具列表 北京大学

1. 明确使用AI工具的需求和目标

2. 建立使用清单

• 文本类、绘图类、视频类、语音类、搜索类、设计类、专用工具类（办公类、笔记类、内容转化类...）、智能体类  
• 以1\~2个为主，其它为辅

3. 真实使用中测试和筛选

4. 保持2-3周的更新频次

AIGC技术正在重塑各个行业的未来，从内容创作到科学研究，从教育到商业应用，潜力无限。然而，技术的快速发展也带来了新的挑战，面对这些挑战，我们需要保持开放的心态，积极学习AIGC的基础知识，关注其在各行业的应用案例，跟踪最新发展趋势。

与AI共舞，实现AI与人类的完美融合。  
让我们以DeepSeek-R1为起点，持续探索AIGC的无限可能。  
在AI时代的技术浪潮中，我们既是见证者，更是参与者。

![](images/1cf797771984aef26bbffea2b8ce9e6fc88fe589df7383ece6a4745dfc188fbf.jpg)