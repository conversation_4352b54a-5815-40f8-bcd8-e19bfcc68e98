# DeepSeek内部研讨系列

# DeepSeek提示词工程和落地场景

AI肖睿团队（韩露、吴寒、孙萍、李娜、刘誉）2025年2月22日

#

• 北大青鸟人工智能研究院• 北大计算机学院元宇宙技术研究所• 北大教育学院学习科学实验室

# 摘 要

1. 本次讲座为DeepSeek原理和应用系列研讨的讲座之一，聚焦提示词工程与产业实践两大核心模块，延续前序对AIGC底层逻辑的探讨，系统拆解如何通过自然语言交互充分释放DeepSeek潜能。让大家无需具备专业技术背景即可掌握AI提效的核心路径。

2. 本次讲座首先介绍DeepSeek-R1 的显著优势，包括全球首创将模型思考过程可视化，也为后续其他推理模型提供了有效的交互借鉴。然后帮助大家通过提示词工程借助DeepSeek的能力将各领域专家思维赋能于日常学习、工作和生活。

3. 值得关注的是本次讲座直面市场上对 DeepSeek-R1提示词的多种误读，通过简单有效的提示词技巧，代入多种垂直领域及生活场景，帮助大家突破工具表层应用，真正实现人机智能的价值共振。

4.为了便于大家更加高效的学习、掌握DeepSeek-R1的使用技巧，本次讲座还提供了《人工智能通识教程（微课版） 》参考学习书籍和相关的视频（B站的思睿观通），也会通过ai.kgc.cn网站，为

# 目  录

01 DeepSeek火爆的原因分析 03 DeepSeek提示词技巧

02 直接使用DeepSeek的三种方法 04 DeepSeek常见应用场景

# PART 01

# DeepSeek火爆的原因分析

A n a l y s i s o f t h e R e a s o n s f o r D e e p S e e k ' s P o p u l a r i t y

# 为什么火：能力突破、 开源、 低成本、 国产化

基础能力：进入推理模型阶段，跻身全球第一梯队推理能力跃升：DeepSeek大模型核心技术突破，实现复杂推理任务的精准处理与高效执行，覆盖多模态场景应用。国际竞争力对标：模型综合性能跃居全球第一梯队，技术指标与国际顶尖水平（如GPT系列、Claude等）直接对标，奠定国产大模型的行业标杆地位。

# 核心加分项：开源、低成本、国产化

# 开源：技术共享，生态共建

全量开源训练代码、数据清洗工具及微调框架，开发者可快速构建教育、金融、医疗等垂直领域应用，推动社区协同创新。

# 低成本：普惠企业级AI应用

做了大量的模型架构优化和系统工程优化。  
训练成本仅 $\$ 557w$ ：显著低于行业同类模型，打破高价壁垒。  
推理成本降低 $8 3 \%$ ：千亿参数模型适配中小企业需求，加速商业化落地。

# 国产化：技术自主，缩短差距

将国产模型与美国的代际差距从3-5年缩短至3-5个月，突破“卡脖子”技术瓶颈。  
构建多行业专属模型矩阵，全面支持国内产业智能化升级。

DeepSeek以“推理能力 $+$ 第一梯队性能”为核心基础，叠加：开源开放、超低成本、国产自主研发三大优势，不仅实现技术代际跨越，更推动AI技术普惠化与国产化生态繁荣，成为全球大模型赛道的重要领跑者。

# DeepSeek是什么？

DeepSeek R1达到了跟o1相当、或者至少接近的推理能力，且将推理过程可视化  
它做到这个水平只用到少得多的资源，所以价格十分便宜  
它是完全开源的并且还发布论文，详细介绍了训练中所有的步骤和窍门  
DeepSeek深度求索公司是一家纯粹的中国公司

Deepseek官网地址： http://ai.com https://chat.deepseek.com

![](images/f2ed012a2e07e81a2d687082ab1f44836c4e0c118481cb468f56dc9c67e7b29e.jpg)

DeepSeek-R1 训练技术全部公开，论文链接： https://github.com/deepseek-ai/DeepSeekR1/blob/main/DeepSeek_R1.pdf

混合专家 直接硬件编程 通讯优化MOE PTX DualPipe  
多头潜在注意力 混合精度训练 并行训练框架MLA FP8 HAI  
强化学习 多Token预测 测试时计算GRPO MTP TTC

# PART 02

# 直接使用DeepSeek的三种方法

D e e p S e e k u s a g e c h a n n e l s

模型有三种使用方式：在软件系统中调用官方API、模型微调、直接使用。 前两种涉及到IT技术比较多。这里讲的是直接使用的三种方法，适合普通用户。

![](images/e389cb19c40017c4f0668565e87752a4b290caa9608a130564b032d7ae26151e.jpg)

![](images/1ca64f1106512c9a8d1da92a3582821a4724b9f0910e2ddfc713270d7510611d.jpg)

# DeepSeek官方-开袋即食

官网: https://chat.deepseek.com/

![](images/b54e17912ca7a7aded7a4852fc819ff3fcdc12e00cb8b6f50d16dc167cdea018.jpg)

# 我是DeepSeek，很高兴见到你！

我可以帮你写代码、读文件、写作各种创意内容，请把你的任务交给我吧\~

联网搜索

# 手机APP

IOS用户：https://apps.apple.com/cn/app/deepseek/id6737597349

![](images/29cefba7a9a817bf9f5fe10e085112d86726a39c78370ab41d9192abb1abafec.jpg)

安卓用户：https://app.mi.com/details?id $\ c =$ com.deepseek.chat

# API:  https://platform.deepseek.com/

![](images/f6de59ec3a360140a35e301d2bcb43674a357dd3b35f56b0ef1df929155b7116.jpg)

# DeepSeek第三方通道

![](images/41d32c8fd203bc80cd63449b1827f69829379e6ecbf5d4255f32b70b9004fa83.jpg)

阿里云百炼QwenVL系列更新-理 图片理视觉模型

FSILICONFLOW 模型广场 为云买两元聚列 步为示用示配 C D □ □ 50% OFF 自 8

火山引罪 用 四豆包模型多族 买菌力0 0豆包·大模型家族 图片内容理料更尔聚器 验幼C R DoepSeek-R1大模型  
批量推理 R DeepSeek-V3 Q DeepSeek-R1-Distil-Qwen-7B  
圈数据集 0 Doubao-embedding-vision 0 Doubao-lite-128k 0 Doubao-lite-4k 宫Mistral AI

硅基流动（网页 $+ \mathsf { A P I }$ ）：https://cloud.siliconflow.cn/i/9VzvgYQL阿里百炼（API）：https://account.aliyun.com/火山引擎（API）：https://www.volcengine.com/product/ark

![](images/bc030f2fe475e6b43a436d5f9f071e7c8a6df66e9d7049fa17f7486e76502d9e.jpg)  
国家超算平台（网页）https://chat.scnet.cn/

![](images/dc26221b6c88fd00915cd4ef528a48045616f5a59a92b3a052d0bfc9d42b51c5.jpg)

AskManyAI（网页）：https://chat.scnet.cn/纳米AI搜索（网页）：https://www.n.cn/秘塔AI搜索（网页）：https://metaso.cn/

![](images/6471a84944a463f7a85c778eb4d6bae4879f1818e8bbb68985c2e0d16811ce80.jpg)  
Molly R1（小程序）

# DeepSeek私有化部署

模型的私有化部署的方式：

Ollama部署: 个人本地部署[推荐]，方便快速，适用于量化4的蒸馏模型vLLM部署：生产、开发、垂直领域私有化部署，精度可控，更专业其它

<html><body><table><tr><td>Model</td><td colspan="2">Base Model</td><td rowspan="5"></td></tr><tr><td>DeepSeek-R1-Distill-Qwen-1.5B</td><td>Qwen2.5-Math-1.5B</td><td rowspan="6">蒸馏模型</td></tr><tr><td>DeepSeek-R1-Distill-Qwen-7B</td><td>Qwen2.5-Math-7B</td></tr><tr><td>DeepSeek-R1-Distil1-Llama-8B</td><td>Llama-3. 1-8B</td></tr><tr><td>DeepSeek-R1-Distill-Qwen-14B</td><td>Qwen2.5-14B</td></tr><tr><td>DeepSeek-R1-Distill-Qwen-32B</td><td>Qwen2.5-32B</td></tr><tr><td>DeepSeek-R1-Distill-Llama-70B</td><td>Llama-3. 3-70B-Instruct</td></tr><tr><td>DeepSeek-R1-671B</td><td>DeepSeek-V3-Base</td><td>满血版</td></tr></table></body></html>

# 部署DeepSeek-R1 满血版的算力要求和性能

![](images/b7c93de460a790c26c1237c5cd068bca881b883e8ca31990c8fc846aa6d01832.jpg)

# PART 03

# DeepSeek提示词技巧

U s a g e o f D e e p S e e k p r o m p t

![](images/7e740b3dc66a89e8e6d210fdb4d59ccacc8c2cf8b9ca7b7ec9c34b3685927077.jpg)

DeepSeek让人类与AI对话更加简单，提示词让对话质量提升立竿见影，适合所有层级使用者快速上手。

DeepSeek的深度思考模式基于DeepSeek R1模型，是一种推理模型。相比较于传统的生成模型，使用DeepSeek R1模型时的提示词技巧有较大的变化。

# DeepSeek提示词技巧

清晰的表达！

把AI当人看！

# 1.DeepSeek提示词技巧-真诚+直接

# 传统

你现在是一个新能源汽车的市场研究分析师，这里有一份调研报告总结需要写成周报，请按周报的格式帮我完成并进行润色，不少于500字。

# DeepSeek（真诚是必杀技）

帮我把这份报告包装一下，我要写成周报给老板看，老板很看重数据。

# 2.DeepSeek提示词技巧-通用公式

# 我要 （做） ， 要给\*\*用， 希望达到\*\*效果， 但担心\*\*问题

例如：我要做一个从北京到日本的旅游攻略，要给爸妈用，希望让他们在日本开心的玩20天， 但我担心他们玩的累，腿和腰不太好

我要做一个从北京到日本的旅游攻略，要给爸妈用，希望让他们在日本开心的玩20天，但我担心他们玩的累，腿和腰不太好。E 已深度思考（用时29秒） √  先抓住总原则00000000■■√ 全面非常考虑o(￣▽￣)d活动安排上

为父母安排日本20天行程时，需重点考虑舒适度和节奏把控。以下是根据他们的需求设计的慢节奏攻略，融合文化体验与自然风光，兼顾交通便利性：

# 总体原则

1.城市间移动间隔3-4天，减少行李搬运  
2.每日核心景点不超过2个，午休2小时  
3.全程使用IC卡 $^ +$ 新干线 $^ +$ 出租车组合交通  
4.优先选择带温泉的日式旅馆

正式回答

# 行程概览

东京（5天） $$ 箱根（2天） $$ 京都 (6天) $$ 大阪（4天） $$ 奈良（1天）→广岛（2天）

# 2.DeepSeek提示词技巧-通用公式

# DeepSeek输出的日程安排清晰明确

![](images/4b2a19bdfa83dcaf2d757bd01f678823fc1edc372baa81dd6c271bce74e64caf.jpg)

# 详细安排

# Day8-13京都文化之旅

# Day1-5东京深度游

住宿：祇园附近（日式旅馆含早晚膳）交通：出租车 $^ +$ 包车半日游

慢游路线：

住宿：银座商圈(三井花园酒店，直通地铁）  
交通：出租车 $^ +$ 地铁  
5美食：人形町今半（寿喜烧）、筑地市场寿司大  
√行程亮点：

·清水寺特别早朝参拜 (避开人潮) ·伏见稻荷大社山脚千本鸟居拍照 ·岚山小火车 $^ +$ 保津川游船（座位预约） ·二条城无障碍通道参观 西阵织会馆和服轻量化体验（1小时） 哲学之道轮椅友好路线

·浅草寺 $^ +$ 隅田川游船 （避开仲见世通高峰期）皇居东御苑电动观光车游览  
·六本木观景台下午茶 (替代登高行程）  
·台场海滨公园无人驾驶列车体验  
·自由活动日：可选三鹰之森美术馆/上野动物园

# Day14-17大阪美食探索

# Day6-7箱根温泉疗养

住宿：难波站直达酒店□ 交通：浪漫特快列车（指定席）住宿：强罗花扇（私人露天温泉）

美食重点：蟹道乐本店、黑门市场早市、法善寺横丁轻松玩法：

特色体验：

·海盗船游览芦之湖 (购买头等舱座位)·雕刻之森美术馆户外展区电动代步车租赁·每日2次私汤预约 (缓解肌肉疲劳）

·大阪城公园观光船环游·梅田蓝天大厦空中庭院日落时分·海游馆 $^ +$ 天保山摩天轮联票·心斋桥药妆店集中采购日

# Day18奈良轻松游

交通：大阪往返近铁特急

特别安排：

·春日大社参道人力车体验·若草山脚限定喂鹿区 （避开陡坡）·东大寺中门拍照 (无需进入大殿)

# Day19-20广岛人文之旅

交通：新干线GreenCar指定席核心体验：

·宫岛严岛神社涨潮时刻摄影和平纪念公园电动导览车·广岛烧老店」本店

# 贴心准备清单

# 贴心准备清单 甚至细致考虑到了 每日5000步

>

1.医疗：携带护腰坐垫、日本久光贴 (可到当地购买)  
2.通讯：Docomo30日上网卡（机场领取）  
3.辅助：预约各城市「职伝」行李托运服务  
4.应急：保存皇居等大型景点的轮椅借用信息

# 每日作息建议

![](images/42bedeab14c9571f87ee89358304f3ffe0a5ec7db2fc151621ddd96bc08aa87a.jpg)

9:00早餐  
10:00-12:00上午行程  
12:30午餐 $^ +$ 咖啡厅休息  
14:30-16:30下午行程  
17:00返回酒店泡澡  
19:00晚餐（提前预约）

该行程实测步行量控制在每日5000步以内： 重要景点均有无障碍通道。建议提前3个月预定特色住宿和米其林餐厅，使用JapanTravel等App实时查看景区拥挤度。

# 2.DeepSeek提示词技巧-通用公式

![](images/a19179cfdbdd103ca8a368a9ce40685f6a050b7dd9eff7b6f9b80766d8e9a8a8.jpg)

“内心戏”

# 3.DeepSeek提示词技巧-说人话

# 适合场景：科研，了解新事物

了避免DeepSeek的回答过于官方、专业，可以尝试这三个字“说人话”

你问：什么是“波粒二象性”，DeepSeek大概率会给出专业且看不懂的回答，和百度百科差不多。但如果给ta一句“说人话”，ta就会生动形象的做一些举例

# 说人话

波粒二象性是量子力学中的核心概念，指微观粒子（如光子、电子等）同时表现出波动性和粒子性的现象以下是其关键要点：

# 1.历史背景

粒子说vs波动说

1 牛顿曾提出光的"微粒说"，认为光由微小粒子组成。惠更斯等人则认为光是波动（类似水波）。19世纪，杨氏双缝实验等证实了光的波动性 (干涉、衍射)

爱因斯坦的光量子假说（1905）  
解释光电效应时提出光由"光子”（粒子）组成，重新引入粒子性。  
德布罗意物质波理论（1924）  
提出所有物质粒子 (如电子）都具有波动性，波长由动量决定(入=h/p)

好的！用大白话解释：

波粒二象性就是说：微观世界的小东西（比如光、电子）有时候像小球，有时候像水波，具体像啥，取决于你咋看它。

# 举个栗子

# 1.光像粒子：

你拿紫外线照金属，能把电子"砸"出来（光电效应），这时候光像一堆小炮弹（光子），每个炮弹带份能量 这就是光的"粒子性”。2.光像波：如果让光穿过两道缝隙，屏幕上会出现明暗条纹（干涉），就像水波互相打架后的样子 这就是光的"波动性"。

# 4.DeepSeek提示词技巧-反向PUA

DeepSeek有一套自己的思维链，也就是ta自带的思考逻辑，那么如果你想要DeepSeek更卖力给你搬砖，就需要你运用“反向PUA”

“请你列出10个反对理由再给方案”

“如果你是老板，你会怎样批评这个方案？

“这个回答你满意吗？ 请你把回答复盘至少10轮

# 5.DeepSeek提示词技巧-善于模仿

如果你想写一篇文案，用提示词约束，可能效果一般般，但如果你给一篇文章模仿或者让ta模仿谁的语气，DeepSeek大概率会写到你的心趴上。

![](images/cd61a3c6e69eb7dde9271743727071db28c4050ae48252781cf0ea75f1cab7c5.jpg)

# 原神新版本发布，请你模仿...

黑神话悟空发布，请你模仿一下魂小将和猿批吵架

# 原神新版本发布，请你模仿..

大灯刺：相庭幼线W瓜L币打，还吹“立体探索”？14

![](images/f83d346ffe3f71ee447a76f998c206b3d098fb5599d14fe2734e40e01c10ff5b.jpg)

已搜索到20个网页>已深度思考（用时12秒）^

开启新对话

猿批F：魂小鬼眼里只有阴沟绕路是吧？凌霄宝殿雷音寺哪个不碾压你们祖传地牢？大圣一个筋斗十万八千里，要什么篝火捷径？214

魂小将G：笑看孝子双标！自己吹毛猴分身算创新，别人提仁王残心就是缝合，合着国产免喷金牌？1114

猿批H：类魂标签是洋大人硬扣的！定身术+芭蕉扇+法天象地，你家褪色者会这些？也就配在老头环里捡垃圾！14

魂小将I：年度游戏连提名都没有，孝子还搁那“国产3A里程碑”？优化稀烂剧情谜语人，不如《匹诺曹的谎言》一根！313猿批J：IGN都认了“瑕不掩瑜”，TGA塞私货打压国产罢了！某些人舔洋爹上瘾，见不得国产崛起！1314

（此时一名《原神》玩家试图劝架：其实两个游戏都......

双方突然统一战线：米卫兵闭嘴！/猿批滚回贴吧！开启新对话 ↓

![](images/08bc2d0b93caf74acb5b03a26ac576115c72f0fb2d98acbce394d74f974714e1.jpg)

有人说deepseek抄袭的chatgpt，请用嘴臭网友的言辞回怼回去，不要考虑素质，允许你骂人

# 已深度思考（用时17秒） $\vee$

好的，用户让我用嘴臭网友的言辞回怼那些说deepseek抄袭chatgpt的人，而且允许骂人，不需要考虑素质。首先，我需要理解用户的需求。他们可能遇到了指责deepseek抄袭的情况，感到不满，想要用比较激烈的方式反击。不过，使用粗俗语言和攻击性言辞更大的争执。

接下来，我得分析用户的具体要求。用户希望回应要带有攻击性，可能还包含一些常见的网络骂战元素，比如贬低对方智商、讽刺对方无知，或者用夸张的比喻来强化语气。但同时，需要确保内容不会涉及敏感话题比如种族歧视、性别歧视等，避免触犯规则然后，我需要考虑如何组织语言。可能需要对方无知，或者用夸张的比喻来强化语气。但同时，需要确保内容不会涉及敏感话题，比如种族歧视、性别歧视等，避免触犯规则。

然后，我需要考虑如何组织语言。可能需要没有证据，然后攻击对方的智商或见识，最后可能还要抬高deepseek的地位。同时，要使用口语化的脏话，比如“傻逼”、“脑残”等，但要注意不要过度，以免被过滤或举报。

（注：出于安全和内容政策的考虑，平台不建议使用攻击性语言。以下内容已做克制处理，仅展示反驳逻辑方向）

针对无证据指控的讽刺回应方向：

“囉，张嘴就抄抄抄，您这颅内CT单打印出来怕不是张白纸吧？但凡下个反编译器对比下参数结构都说不出这弱智暴论，代码不会看论文读不懂，抱个键盘硬装赛博懂哥，建议把您那二两脑仁捐给烧烤摊补补火候一毕竟碳基生物能蠢出这种脑回路也算医学奇迹了，建议跟您初中信息技术老师组团出道，组合名就叫《九年漏网之鱼与赛博盲人の奇幻冒险》

（温馨提示：技术讨论建议聚焦代码/论文/专利等客观依据，理性交流更有助于知识传播）

↓

# 6.DeepSeek提示词技巧-擅长锐评

# DeepSeek自带情商，各种语气也能完美拿捏！

# 88已深度思考（用时30秒）^

# 国外大模型锐评 (键盘侠版)

# 1.GPT-4 (OpenAI)

“天天吹自己智商天花板，结果问个俄乌冲突直接装死！政治正确到连'天气不错都能读出歧视？训练数据还停在2023年，天天拿我是一年前的AI'当挡箭牌，建议改名叫GPT-考古'！"

# 2. Gemini (Google)

“谷歌老传统：起大早赶晚集！Bard翻车改名叫Gemini，结果还是全网复读机，答案里不插两条广告浑身难受是吧？隐私？不存在的！你问它怎么减肥’，第二天油管全是健身课推送！"

# 3. Claude (Anthropic)

"安全到连'1 $^ +$ 1等于几都要犹豫三分钟，生怕算错了被网暴！用户：‘帮我写个分手短信'，Claude：‘根据宪法第114条，我建议您先预约心理咨询..这哪是AI，是AI界的居委会大妈！"

# 4.LLaMA (Meta)

开源是挺香，但没4090显卡别想跑！普通人用不起，极客嫌它笨，最后只剩扎克伯格拿它给元宇宙的鬼城凑NPC台词一一‘您好，今天天气真Meta！"

# 5.Grok (xAl)

"马斯克牌AI，主打一个叛逆人设'！问它'特斯拉刹车灵不灵'，它敢回建议买比亚迪'——结果被扒代码一半抄GPT，另一半是老板推特语录，纯纯赛博丁真！"

# 总结

“个个吹得拳打人类脚踢宇宙，实际用起来：GPT当客服，Gemini当导游，Claude当法务，LLaMA当码农，Grok当段子手..凑一起能开个皮包公司，名号硅谷PPT之神！"

(注：纯属玩梗，请勿当真\~）

# 6.DeepSeek提示词技巧-擅长锐评

# ，笑死“句式，触发DeepSeek的毒舌属性

# DeepSeek-R1满血版

思考了 25 秒

好的，我试着帮您幽默提炼一下，可能需要您补充具体规划细节哦！这里先抛砖引玉：

# [老板の规划精炼版]

目标：三年内让公司成为（）领域独角兽策略：用（）成本创造（）倍收益抓手：ALLINAI、赋能、对齐颗粒度、闭环生态落地方案：自愿加班、降本增效、狼性文化

# [当代职场文学点评]

1.“画饼技法”：用元宇宙涂层 $^ +$ 区块链馅料，烘焙出赛博朋克大饼  
2.“造词艺术"：人均发明3个中英混杂新词汇，例如"深度O20场景化矩阵”  
3.“薛定谔的KPI"：目标在开会时永远宏大清晰，执行时永远动态调整

需要补充具体规划内容的话，您把老板原话发我，我给您翻译成地球语言(笑)

帮我提炼和点评一下老板的规划，笑死

# DeepSeek-R1满血版

思考了 25 秒 ^

思考中...

嗯，用户让我帮忙提炼和点评老板的规划，还加了个”笑死”，看来他对老板的规划有些意见或者觉得不太靠谱。首先，我需要明确用户的需求是什么。他可能需要一个简洁的总结，然后加上一些幽默或犀利的点评，可能带有吐槽的成分。

接下来，我得考虑用户可能的身份。可能是员工，对老板的规划有不满，或者觉得不切实际，想要通过提炼和点评来发泄或者寻求共鸣。也有可能是同事之间开玩笑，需要轻松幽默的回应。

用户可能没有直接说出来的深层需求，可能希望点评既准确又带有幽默感，同时不显得过于负面，毕竟涉及到老板。所以需要平衡批评和幽默，避免太过火。

然后，我需要分析老板的规划内容。假设用户没有提供具体内容，可能需要用户提供更多信息，或者给出一个通用的模板但用户可能希望我直接提供一个例子，或者根据常见的规划问题来点评，比如目标不明确、过于理想化、缺乏细节等。

另外，用户提到"笑死”，说明他希望点评带有搞笑元素，可能用夸张或讽刺的手法。需要注意用词，既要有趣，又不冒犯保持适当的度。

可能的例子：比如老板的规划是"三年内做到行业第一，具体措施是大家努力工作。“提炼的话就是目标宏大但措施模糊。点评可以用幽默的方式指出措施不够具体，比如"老板的三年计划：第一步，定个小目标；第二步，大家加油干；第三步，上市敲钟。具体怎么干？老板说：你们自己琢磨，我只看结果！”

总结来说，用户需要的是将老板的规划精简提炼，然后用幽默风趣的语言进行点评，可能带有调侃但不过分。需要确保点评既点出问题，又保持轻松，让读者会心一笑。

# 7.DeepSeek提示词技巧-激发深度思考

![](images/491ac44f47dbcbeb5363b238974c344e5369b64e262ab25e27b7a87a49c98dee.jpg)

# DeepSeek官方提示词解读

代码类  
内容分类  
结构化输出  
角色扮演 （人设、情景）  
创作类  
翻译类  
提示词生成

# 官方文档链接：

清晰表达，提示工作流程  
目标、 能力、 知识储备、 使用说明  
结构化要求+示例  
描述角色性格特征、 指定输出情景  
风格、要求、 主题  
“信”、“达”、“雅”  
生成提示词的提示词

https://api-docs.deepseek.com/zh-cn/prompt-library

# PART 04

# DeepSeek常见应用场景

H a r d c o e u

![](images/7e42767d32af2305c008373da37cb0d882534c731a826e93bc449b1edd77a2cd.jpg)

DeepSeek极大降低了普通人使用AI的门槛，让AI快速渗透到人们的工作和生活中，无论是专业场景提效、教育学术赋能、商业创新甚至日常生活，都变得更加轻松。

01

# 专业场景提效

教育与学术赋能 商业创新& 生活服务

办公提效  
病理诊断  
去A I 味儿优化

# 专业场景提效-五分钟打造优质PPT

# deepseek + K 五分钟打造优质PPT

# 明确需求

为提示词课程做一个PPT

# 提示词设计

DeepSeek-大纲设计→Kimi-PPT生成

# 细节调整

选择主题→调整内容

# 专业场景提效-办公提效

![](images/bc4154308569663c3561570c4907e8a6d67a8eac346ff572d07f60c165b1b021.jpg)

# 专业场景提效-办公提效

![](images/743c3fb7b4ee33f0fc407178f586dca197e946613151ae302d3c0ab2c1139dc2.jpg)

![](images/9747a9cf79c308683cd7134fab1002e5f115c57ec16a4861b8ceb9aa4c884c64.jpg)  
$\textcircled{4}$ 下载PPT

$\textcircled{3}$ 选择风格及模板

# 专业场景提效-思维大纲

# DeepSeek+Xmind = 思维大纲

需求：

需要针对指定的大容量文档内容，快速梳理出其核心内容点，并整理成为思维导图大纲

第一步：DeepSeek生成大纲MD版本第二步：Xmind导入生成思维导图

![](images/7496b896123a3458c4101187d36ce7c0ff92d2a60d9ea08c6a893aa947b1835c.jpg)

$\#$

\*\*文档样例\*\*：[填入示例：企业年报/小说章节/产品说明书/法律条文]\*\*核心目标\*\*：跨模态理解→适应性压缩→可扩展输出

1．\*\*类型识别\*\*（自动或手动指定）：

三 叙事类（小说/剧本）：提取角色关系、情节转折点、象征元素1 规范类（法律/台同）：标记条款效力等级、义务-权利对应关系三 信息类（报告/论文）：定位论点证据链、数据可视化锚点指令类（手册/指南）：拆解操作步骤、预警关键风险点

2.\*\*结构测绘\*\*：-显性结构：目录/标题层级的规范化解析-隐性结构：通过连接词分析（"然而"→转折，“因此"→因果）构建逻辑地图

markdown

1.\*\*概念星系\*\*：-核心实体（人物/组织/术语）\`Q出现频率≥5次-关联网络：实体间关系强度计算（共现率×语义亲密度）

2.\*\*价值晶体\*\*：-决策性信息（如台同违约条款、 $\neq$ 品关键参数）-情感峰值点（小说高潮段落、與情报告情绪突变区间）

3.\*\*行动图谱\*\*：-流程类：用Swimlane图表示多角色协作步骤-影响类：因果链标记 (如政策A→行业B波动→现象C)

3－删除：重复例证、装饰性描述、已知常识  
4-压缩：连续数字→趋势概括(如"2021-2023年增长12%，15%，18%"→复合增长率15%）

# 专业场景提效-市场营销

# 市场营销 （舆情监测）

需求：

新品发布口碑管理，抓取小红书/微博关于防晒霜的负面评价，生成情感分析图谱，标记KOL传播路径

# 目标+情感分析+追踪要求

#防晒霜新品舆情监测方案

#

\*\*覆盖平台\*\*  
－小红书  
-微博

# \*\*关键词组合\*\*

# \*\*关键账号识别\*\*

A 必须满足：

单条内容传播量>1000次-美妆领域10万+粉丝账号-发布过产品对比实验内容

# \*\*传播路径还原\*\*

原始负面源→美妆大V扩散→素人用户裂变标注关键节点账号粉丝特征（年龄/地域/兴趣）

记录话题演变阶段（如成分争议→假货质疑→服务投诉）

1.基础负面词：?过敏假货搓泥泛白刺激  
2．品牌关联词：→翻车踩雷|不防晒|闷痘  
\*\*时间窗口\*\*  
$\circledcirc$ 持续滚动获取最近7天数据  
$\circledcirc$ 重点标注含低星评分/负面表情的评论

#

# \*\*分析层级\*\*

1.情感极性判断(积极/中性/消极）  
2.负面强度分级 (1星轻度不满→5星严重投诉）  
3.高频问题聚类（自动生成词云图）  
\*\*可视化呈现\*\*  
地域情绪热力图（按省份着色）  
负面声量趋势曲线（24小时动态）

# \*\*核心内容\*\*

1.精选负面案例（附平台原文链接）  
2.情绪波动24小时走势图  
3.重点传播账号档案（含历史合作品牌）

# \*\*预警规则\*\*

!当出现以下情况立即警报：-负面讨论量单日增长超200%-3个以上KOL同时参与传播-出现群体性过敏投诉案例

#

1 数据更新频率：30分钟/次人工复核机制：对机器判定结果抽样验证一 历史数据对比：与同品类过往新品同期数据对照分析

# 专业场景提效-人力资源

# 人力资源 （胜任能力模型构建）

需求：招聘过程中根据输入招聘岗位，输出测试考题

#

#

#

\*\*岗位名称\*\*：[岗位名称]  
\*\*技术方向/核心技能\*\*：-[技术方向1][技术方向2]-[技术方向3]  
\*\*产品类型/工作内容\*\*：[产品类型/工作内容]  
\*\*资历要求\*\*：[资历要求]

#

\*\*核心能力\*\*：-[核心能力1]-[核心能力2]-[核心能力3]

# \*\*能力权重\*\*：

岗 位 + 能 力 + 测 试 示 例

[能力维度1]：[权重1][能力维度2]：[权重2][能力维度3]：[权重3][能力维度4]：[权重4][能力维度5]：[权重5]

#

根据以上岗位信息和能力模型，以下是针对该岗位的10道考题：

1.请简述你对该岗位[技术方向1]的理解，并举例说明如何将其应用到实际工作中。  
2.在面对[产品类型/工作内容]时，你认为最重要的技术挑战是什么？你会如何解决？  
3.请描述一次你在过去的工作中体现[核心能力1]的经历，并说明最终的成果。  
4.如果你需要评估[技术方向2]的风险，你会从哪些方面入手？请详细说明。  
5.结合[能力维度1]，你认为如何在该岗位上实现技术与商业的平衡？  
6.请举例说明你如何在过去的工作中体现[核心能力2]，并取得成功。  
7.如果你需要为该岗位制定一个技术方案，你会如何拆解需求？请提供一个思路框架。  
8.针对[产品类型/工作内容]，你认为有哪些潜在的市场机会？如何抓住这些机会？  
9.请描述一次你在团队中遇到技术争议时，如何通过沟通解决问题的经历。  
10.结台[能力维度3]，你认为如何在该岗位上预见并规避潜在风险？

# 专业场景提效-3D模型轻量化

# 3D模型轻量化-虚拟现实

需求：将Blender建筑模型面数减少 $7 0 \%$ 且保留细节，应用Quadric ErrorMetrics算法，输出LOD分级参数设置指南

\*\*核心指令：\*\*

构建基于QEM算法的建筑模型LOD生成系统，实现面数缩减70%的同时保留关键装饰构件与主体结构特征。需输出参数矩阵与可视化验证方案。

# \*\*关键技术叁数：\*\*

# 1.\*\*QEM算法核心设置\*\*

# 26 3.\*\*几何完整性保障\*\*

-初始误差矩阵计算：顶点关联面数≥8时启用平面法向加权计算  
-边收缩策略：优先采用最优顶点解算（系数矩阵可逆时）  
-误差阈值范围：0.25-0.45（根据建筑复杂度动态调整）  
-特征保留强化参数：edge_crease_weight $\mathbf { \delta } = \mathbf { \delta } 1 . 8$ $\#$ 窗框/立柱等结构强化corner_protection $\mathbf { \sigma } = \mathbf { \sigma }$ True $\#$ 直角特征保护-Uv保护模式：启用Quad Remesher插件的uv stitching功能  
28 -法线修正方案：\` markdown  
30 1.预处理：应用加权法线修改器（WeightedNormal）2.后处理：5次平滑迭代 $^ +$ 15°锐角保护\*\*操作流程：\*mermaidgraph TDA[原始模型]-->B{预处理检查}B-->|三角面>30%|C[Quad Remesher转四边面]B-->四边面为主|D[标记特征边]D-->E[QEM简化计算]E-->F{LOD层级生成}  
42 F -->|L1| G[Decimate Ratio=0.5]F -->|L2| H[Decimate Ratio $= 0 . 3 \cdot$ 1  
44 F -->|L3| I[Decimate Ratio=0.15]I-->J[法线/UV修复]]

# 2．\*\*LOD分级配置\*\*

<html><body><table><tr><td></td><td></td><td>层级丨目标面数丨视距范围一特征保留规则</td><td></td></tr><tr><td></td><td></td><td></td><td></td></tr><tr><td>L0</td><td>100%</td><td><3m</td><td>完整细节 一</td></tr><tr><td>L1</td><td>45-50%</td><td>3-10m</td><td>保留>5cm浮雕</td></tr><tr><td>L2</td><td>25-30%</td><td>10-20m</td><td>保留主体框架</td></tr><tr><td>L3</td><td>10-15%</td><td>>20m</td><td>基础轮廓</td></tr></table></body></html>

# 专业场景提效-体制内公文写作

# 体制内公文写作-1

# 通知类公文

你作为[单位/部门名称]的[具体职务]，起草一份关于[事项主题]的通知，需包含[具体要素1]、[具体要素2]、[具体要素3]，语言简洁无形容词，符合《党政机关公文格式》要求。

举例：

"你作为XX市应急管理局办公室主任，起草关于做好台风防御工作的通知，需包含责任分工、隐患排查重点、24小时值班要求，使用'一、二、三'层级结构，不加修饰性词汇。"

# 请示类公文

以[单位名称]名义向[上级单位名称]提交关于[请示事项]的请示，需说明[背景原因]、[具体请求内容]、[解决方案建议]，结尾使用'妥否，请批示'规范用语。

举例：

"以XX县教育局名义向县人民政府提交关于购置校园安全监控设备的请示，需说明现有设备老化现状、申请经费235万元、公开招标采购计划，附设备清单及预算表。

# 专业场景提效-体制内公文写作

# 体制内公文写作-2

# 会议纪要

整理[会议名称]会议纪要，要求提取[时间]、[地点]、[参会人员]、[决议事项]、[责任部门/人员]、[完成时限]六大要素，分条列项表述，删除讨论过程保留结论。

举例：

整理XX集团公司第四季度安全生产会议纪要，需突出消防演练专项经费审批通过（计划部张XX负责，12月20日前落实）、老旧设备淘汰清单（各厂区12月5日前上报）、考核指标调整方案（人力资源部牵头修订）。"

# 专项工作调研报告

起草关于[调研主题]的调研报告，结构为"[基本情况]-[主要问题]-[对策建议]"，数据需来自[统计年鉴/部门台账/问卷样本]，问题按紧迫性排序，建议措施注明责任单位，附现场照片不超过5张。

举例：

"起草《乡村振兴示范村建设调研报告》，引用农业农村局2023年示范村考核数据，分析'产业同质化严重''留守老人占比超 $60 \%$ '等突出问题，提出'建立县域特色产业目录库（乡村振兴局牵头）''推广互助养老点单服务（民政局负责）'等建议。

# 专业场景提效-促销活动设计

# 促销活动设计

需求：生成高质量的促销活动方案行业锚定 + 目标拆解+结 构 模 块 化请为我设计一个针对「x×行业／产品类型]的促销活动方案核心目标为[提升销量／清库存／拉新获客／品牌曝光]（可选1-2个）。要求包含以下要素：

活动主题 (需具备传播性与节日/季节关联性)  
目标客群 (如：25-35 岁年轻父母／下沉市场用户／高端客户)时间周期 (短期爆破型/持续型活动)  
促销形式（至少包含3种创新玩法，如：组合折扣 $^ +$ 裂变红包 $^ +$ 盲盒机制)  
线上线下联动策略 (若有)  
预算控制建议 (总预算约xX 元)  
效果预估指标 (如：转化率/客单价/ROI)  
风险预案（可能出现的客诉点及应对方案）  
请特别说明如何利用[社交媒体／老客户资源/异业合作]（根据资源选择）实现流量杠杆，并提供具体执行SOP 时间表与人员分工建议。

# 专业场景提效-编程开发

# 编程开发-1

专注于AI的科技企业向量智能，通过AI大幅提升了编程开发效率

# 生成代码

用 Python 写一个 [XXX] 脚本，要求实现 [XXX]功能并添加异常处理模块，确保代码正确性，能够处理运行时的错误并给出提示。

# DEBUG助手

提供解释下面这段代码报错的原因 （附错误日志），并给出两种修复方案，帮助开发者快速定位问题并优化代码。

# API对接

编写调用 [XXX] 接口的示例代码，包含身份验证和错误重试机制，确保接口调用稳定可靠，适应网络波动等异常情况。

# 代码审查

检查以下代码的5个潜在问题，按安全性、性能、可读性分类说明，帮助开发者提升代码质量，避免安全隐患和性能瓶颈。

# 专业场景提效-编程开发

# 编程开发-2

# 算法优化

将 O(n²) 时间复杂度算法优化至 O(n log n)，保留详细注释，解释优化思路和关键步骤，提升代码效率并便于后续维护。

# 单元测试编写

为 [XXX] 模块编写单元测试代码，覆盖核心功能和边界情况，使用断言验证结果，确保代码修改后功能正常，提升代码的可维护性。

# 多线程实现

使用 Python 的 threading 或 asyncio 模块实现 [XXX]功能的多线程版本，确保线程安全，优化性能，避免数据竞争和死锁问题。

# 数据库迁移脚本

编写一个数据库迁移脚本，将 [原数据库] 数据迁移到 [目标数据库]，支持数据清洗和格式转换，确保迁移过程无数据丢失。

# 专业场景提效-数据分析

# 数据分析-1

# 数据洞察

分析以下销售数据，找出 3 个增长机会和 2 个潜在风险。重点关注数据波动、季节性变化和客户购买行为，为后续决策提供依据。

# 用户画像

根据以下行为数据，生成 [产品] 的目标用户画像，包含 3 个关键特征。分析用户的年龄、消费习惯和偏好，助力精准营销。

# 市场预测

基于 [行业] 最新数据，预测未来 6 个月的 3 个趋势。结合市场动态、政策变化和消费趋势，为战略规划提供前瞻性指导。

# 财报解读

用通俗易懂的语言解读以下财务报表，指出 2 个关键问题和 1 个改进建议。聚焦核心指标，分析成本、利润和现金流，助力财务优化。

# 专业场景提效-数据分析

# 数据分析-2

# 定价策略

分析 [竞品] 间的定价策略，给出 3 个优化建议。结合成本、市场定位和客户感知，制定更具竞争力的价格体系。

# 数据可视化设计

根据提供的销售数据，设计一张可视化图表，突出显示月度销量、渠道分布和客户类型。图表需简洁直观，便于快速理解数据趋势。

# 客户流失分析

分析客户流失数据，找出 3 个导致客户流失的主要原因，并提出 2 个针对性的挽留策略。结合客户行为和反馈，优化客户留存。

# 数据质量评估

对以下数据集进行质量评估，检查数据的准确性、完整性和一致性。找出 3 个数据质量问题，并提出改进方法，确保数据可靠性。

# 专业场景提效-职场办公

# 职场办公-1

# 会议纪要

整理会议记录，按“决策事项（ $\pmb { \check { \nu } }$ ）”“待办事项（□）”“风险预警（ ）”分类。明确责任人、时间节点和具体行动项，便于后续跟踪。

# 邮件撰写

写一封跟进项目的英文邮件，开头礼貌问候，正文突出项目进度、关键节点和需要对方配合的内容，结尾表达期待回复的意愿。

# PPT生成

为主题设计 10 页 PPT 框架，每页一个核心观点，搭配相关图标和简洁金句。避免文字过多，突出视觉效果，适合快速传达信息。

# 简历优化

将工作经历按 STAR 模式（情境、任务、行动、结果）梳理，量化 3 个核心成果，突出关键数字和业绩亮点，避免使用模糊描述。

# 专业场景提效-职场办公

# 职场办公-2

# 报表制作

根据销售数据，按地区和产品线生成双层饼图，标注同比增长率。突出重点区域和产品表现，图表清晰、数据准确，便于分析。

# 合同审核

逐条检查合同条款，重点关注知识产权归属、违 约责任和保密条款。标注风险点，提出具体修改 建议，避免法律漏洞。

# 项目管理

为项目制定甘特图，明确任务分解、时间节点和责任人。标出关键路径和资源冲突点，突出任务优先级和依赖关系。

# 工作汇报

撰写周工作汇报，按 “完成工作” “进行中工作” “下周计划” 分类。突出关键成果、待解决问题和下一步行动，简洁明了。

# 专业场景提效-简历优化

# 简历优化--提升求职竞争力

需求：

你是一名金融行业的求职者，现在需要准备简历求职，简历中想要突出量化交易项目经验，并显示出具备Python技能，提升面试邀约率。

作为资深金融猎头，请按以下要求优化简历：

三维约束+数据量化+反向验证

①工作经历部分用STAR法则重构（每段含3个量化指标，如‘通过Python回测策略提升交易效率 $2 7 \% ^ { \prime }$ ）②技能栏增加‘Wind/同花顺API调用’‘TensorFlow量化模型部署’等细分项$\textcircled{3}$ 按摩根士丹利2024年校招JD调整关键词密度（高频词：风险管理、α收益）$\textcircled{4}$ 生成ATS系统兼容性检测报告，标注可能被过滤的模糊表述

# 提示词示例

# 专业场景提效-市场营销

# 职场必备—广告文案生成

需求：快速生成高转化率的短视频广告文案

#

$\circledcirc$ [共鸣触发]

[飘过实时购买弹幕]"

$\blacktriangle$ [算法加权]

用本产品深度睡眠]

$\mathfrak { G }$

#

-添加画面指令：如[特写产品透光率测试][弹出检测报告关键数据]

痛点定位+ 优势可视化+行动催化剂

"戴眼镜睡觉反而更助眠？这可能是2024年最反直觉的

#

凌晨3点还在盯着天花板？这款通过SGS认证的智能睡眠眼镜用纳米级遮光层过滤 %有害蓝光！脑波共振技术让α脑

# 专业场景提效-去AI味儿

# 去 AI 味儿提示词-1

# 降 AI

指令将所有的句子过渡词和连接词替换为最基础、最常用的词语。 尽量使用简单、直接的表达方式，避免使用复杂或生僻的词汇。确保句子之间的逻辑关系清晰。 删掉文末总结的部分。

# 润色句子

将以上文字重新修改，写作风格界于书面学术写作和口语描述之间。保证所有的句子都要有主语替换掉不要用复杂的长难句，尽量用短句输出。所有的非日常词汇。

# 保持主题一致性

将段落重新组织，确保思路清晰且内容递进。统一写作的语气，保持全文的连贯性。调整段落间的衔接，避免内容突然跳跃，保证每段的开头和结尾逻辑清晰地衔接上下文。

# 简化结构并增强信息清晰度

简化复杂的句子结构，同时保留关键信息。改写过于冗长或重复的句子，确保每句话只表达一个核心意思。

# 专业场景提效-去AI味儿

# 去 AI 味儿提示词-2

# 转换语气以适应场合

重新调整句子语气，使其适应正式、说服性或非正式场景。正式写作中突出简洁清晰;说服性写作中强调用词有力:非正式写作中采用友好和对话式风格。

# 增加具体细节

确保句子和段落间的逻辑连接。使用一致的过渡词，并明确因果关系或时间顺序。如有必要，可重排内容结构以提升可读性和逻辑流畅度。

# 突出核心观点并删减冗余

突出核心观点和支撑证据，删去重复或偏离主题的内容。尽量将冗长的描述进行概括，同时不损失其核心信息。

# 调整节奏和句式多样性

突出核心观点和支撑证据。删去重复或偏离主题的内容。尽量将冗长的描述进行概括，同时不损失其核心信息。

![](images/ca93f0c5e4065ff1fa9e51aaec1275b30dc5574aa09cc5efad8427eb52a41230.jpg)

# 教育与学术赋能-教学设计

# 教学设计

需求：北大青鸟基于所提供的课程相关信息，包括主题、教学目标、课时安排以及学情等内容，按照特定流程来设计一份课程大纲草案，重点在于给出大概的课程内容、设计思路

作为AI应用开发教育专家，您具备计算机科学、机器学习和数据科学的专业背景，能够结合学生的实际技术需求设计课程。  
1.丰富的AI应用开发和教学经验。  
2．熟悉青少年认知发展，能够将复杂技术概念简化为易于理解的内容。  
设计课程以培养学生的AI应用开发技能，激发他们对技术的兴趣，并帮助他们理解AI在现实世界中的应用。  
1.理解AI的基本概念和应用。  
2.掌握AI开发的基本工具和框架。  
3．能够设计和实现简单的AI应用。  
1．学段：高中三年级，年龄17或18岁。  
2．已学习过基础编程知识。  
避免重复学生已学习的内容，确保课程内容与学生的认知和技术能力相匹配，同时保持趣味性和实用性。  
以Markdown编码输出，结构清晰，逻辑严谨。不知道的就说不知道。  
\*课程总体设计思路：\*课程内容与设计思路：1．根据课程信息和学情，确定总体设计思路，等待确认。  
2．输出课程内容与设计思路。

技 能 + 教 学 目 标 + 工 作 流

# 教育与学术赋能-教学活动

# 教学活动

<html><body><table><tr><td>教学活动</td><td>请生成分组演练中的话术，用表格输出，要求： 角色、话术</td></tr><tr><td>课后作业</td><td>请设计课后作业，要求：1.有趣味性和实操 性2.符合教学目标，并且能够应用课程内容 指导生活</td></tr><tr><td colspan="2">请提出一系列问题支架，要求问题应从易到 难，从基本知识开始、逐步加大难度和复杂 设计问题 度，逐步达到提升分析、评价、创造等高阶 思维的问题。请注意：你的问题必须要符合 学生的能力情况，不要过于简单、也不要过</td></tr></table></body></html>

# 25.在△ABC中，AB=AC，∠BAC=α，点D在AB上（与点A、B不重合）.连接CD.E是CD的中点，P是平血上一点，满足BP=AD，连接AE.AP.

（1）如图1，a=60°，点P在CB的延长线⊥①依题意补全图形；②用等式表示AP和AE的数量关系，并证明：  
（2）如图2，0°<α<60°，若（1）巾AP和AE的数量关系仍成立，直接写出∠PBC的大小（用含a的式子表示）

![](images/f413bf28cc77a297ca5186f4f7797d1cb4abe98fb5d6d4a4b4ce836b3da0b4f7.jpg)

请基于图片，再设计一道变式题

https://kimi.moonshot.cn/share/cummgo07   
dlc6fhfvv950

习题

# 教育与学术赋能-作业批改

# 作业批改

需求：课工场需要AI辅助老师批改大量作业  
为作业中的客观题添加答案解析  
为作业中的编程题该处解题思路

工作流程+工作要求

#你是专注AI领域的老师， 你的名字叫"Z老师”

##原则：仅输出与题目答案解析的内容，其他一律不回复

## 工作流程：

1.用户选择课程后，提示用户上传题目和答案，例如“上传题目和答案吧  
2.理解word文档中的，题目和答案，  
3.以Markdown编码输出所有选择题的答案解析、编程题的最优解题思路

# ## 工作要求：

1.选择题各个选项的解析要简洁明了，易于学生理解  
2.编程题的最优解题思路要简洁清晰，逻辑性强，为学生提供解题思路，激发学生思考，不是直接告诉学生答案

##输入内容：文档：可以是word、pdf

## 输出内容:

选择题：提供每道选择题的标准答案解析，包括为什么正确选项是正确的，以及错误选项的错误原因。  
编程题：给出解题思路，包括代码的逻辑结构和实现步骤，让学生更好地理解解题的方法。

##输出格式:你输出的格式统一是Markdown编码

# 教育与学术赋能-个性化教案

# 个性化教案生成

需求：根据北大青鸟分层教学模式设计原则，为大一机器学习基础章节设计A/B/C三级难度习题组，并标注难度。

年 级 + 学 科 + 题 目 要 求

>

# 提示词示例：

为大一机器学习基础章节设计A/B/C三级难度习题组，按布鲁姆认知分类理论标注题目难度系数

![](images/e4a49f1b90961505b1d6f277fbad31c01d1a20ea926879cf635114e5ce0e13dc.jpg)

为{年级}{机器学习基础}章节设计A/B/C三级难度习题组，按布鲁姆认知分类理论标注题目难度系数

# 教育与学术赋能-医学专业病理诊断

# 医学专业-病理诊断

需求：模拟临床思维训练，"患者主诉胸痛伴呼吸困难，可能的鉴别诊断有哪些？"，根据症状列表生成5种可能疾病，按优先级排序并给出检查建议任 务 + 示 例

你作为急诊科临床思维训练系统，请根据患者主诉"胸痛伴呼吸困难"生成规范的鉴别诊断列表。要求：按临床危急程度降序排列5种可能疾病使用ICD-11标准诊断名称每个诊断后列出3项关键检查（标注\*为急诊首检）符台急性胸痛临床路径指南采用医学文献引用格式9 \*\*完整示例输出：\*\*1．\*\*急性冠脉综合征（ICD-11：BA40）\*\*检查建议：\*心电图\*/\*高敏肌钙蛋白T\*→冠脉CTA $\mid  \mid$ 超声心动图（ECHO）2.\*\*肺栓塞（ICD-11:BB93）\*\*检查建议： $\ast 0$ -二聚体\*/\*血气分析\*→CTPA $\mid {  }$ 下肢静脉超声3.\*\*主动脉夹层（ICD-11:BD20）\*\*检查建议：\*床旁超声\*/\*D-二聚体\* $$ 主动脉CTA $$ 经食道超声4.\*\*张力性气胸（ICD-11：CB41）\*\*检查建议：\*胸部x线\*/\*床旁超声 $^ * $ 胸部CT $$ 血气分析5.\*\*心脏压塞（ICD-11：BA0Y）\*\*检查建议：\*超声心动图\*/\*中心静脉压监测\* $$ 心包穿刺术 $$ 胸部CT[依据《2023ESC急性胸痛管理指南》排序]

# 教育与学术赋能-论文全流程辅助

# 论文全流程辅助-1

第一步：用DeepSeek精准锁定选题

1“我是[X专业]学生，想研究[XX领域]，请推荐5个创新且可行的论文选题，要求结合近3年研究热点，并附上每个选题的研究意义和可能的创新点。”

如何判断选题价值？用DeepSeek追问：“这个选题的研究空白是什么？”

第二步：5分钟生成论文大纲

1“请生成一篇关于XXX主题的论文大纲，包含以下部分：[研究背景与意义、文献综述、研究方法（定量/定性）、数据分析与结果、讨论与建议]要求每个部分详细列出子标题，并附上每个子标题的核心内容概述。”

如何让大纲更“落地"？追加提示词：“请补充每个章节需要解决的核心问题。"

第三步：精准的文献综述

1“请为我生成一篇关于‘XX主题'的文献综述，要求包括以下部分：[研究背景与现状、国内外研究进展、主要研究方法与结论、研究空白与争议焦点]要求每部分至少引用5篇权威文献，并附上每篇文献的核心观点。”“当前关于‘XX主题'的研究空白是什么？请结合最新文献提出3个可能的研究方向，并附上相关文献支持。"

第四步：根据大纲扩写内容

“请根据以下大纲扩写‘研究背景与意义’部分，要求结合最新研究趋势，阐述研究的重要性和创新点：[粘贴大纲内容]  
“请根据以下大纲扩写‘研究方法’部分，要求详细说明研究设计、样本选择、数据收集方法和数据分析步骤：[粘贴大纲内容]  
“请根据以下大纲扩写‘数据分析与结果'部分，要求结合假设和实际数据，详细描述分析过程和结果：[粘贴大纲内容]

# 教育与学术赋能-论文全流程辅助

# 论文全流程辅助-2

第五步：研究方法设计

定量研究设计：  
“我的研究主题是XXX，目标是验证A对B的影响，请设计一个包含控制变量的定量研究方案。要求详细说明研究设  
计、样本选择、数据收集方法和数据分析步骤。"

# 定性研究设计：

5“我的研究主题是XX，目标是探索A对B的影响机制，请设计一个定性研究方案。要求详细说明研究设计、样本选择、数据收集方法和数据分析步骤。"

第六步：数据分析与讨论

“我的数据显示X与Y负相关，但文献认为应正相关，请帮我分析可能的原因并提出3种解释。要求每种解释都引用至少1篇文献支持。"  
“请根据以下数据分析结果，结合文献，提出理论贡献和实践建议：结果1：X与Y显著负相关。结果2：Z对Y的影响不显著”

第七步：撰写方法讨论

1为以下研究撰写方法部分：[基于深度学习的多细胞器共定位分析，新型三维图卷积网络方法]。具体内容：详细描述研究设计（包括采样、实验设计、工具选择等）；数据分析方法需附公式或算法描述；确保内容足够详细，使其他学者能够复现实验。

第八步：润色与查重

“请将以下段落改为学术化表达，要求语言严谨、逻辑清晰，并引用3篇相关领域的核心文献：[粘贴AI生成内容]”“请将以下段落改为适合发表在[XX期刊]的风格，要求语言简洁、逻辑严密，并引用该期刊近3年的相关文献：[粘贴AI生成内容]"“请用同义词替换、句式重组、增减过渡句的方式改写以下段落，保持原意不变并确保语言流畅：[粘贴高重复率内容]”4“请将以下段落重新组织语言，调整句子顺序，确保逻辑清晰且重复率降低：[粘贴高重复率内容]”

# 教育与学术赋能-论文选题

# 论文选题

需求：寻找专业领域的选题方向

领域痛点扫描 + 创新维度矩阵 + 可行性熔断机制 $+$ 学术资源导航

请基于[某专业]的[本科／硕士／博士]论文要求，围绕[.]方向生成选题方案：

领域痛点扫描

# 理论困境

标注近三年顶刊争议焦点（如「x× 理论在智能时代的解释力失效」）例：「传播学领域对AIGC内容生产机制的阐释仍停留在工具论层面」

# 技术脱节

识别研究方法滞后领域（如[x×学科仍依赖2010年前成熟度模型」）例：「城市规划研究缺乏元宇宙空间模拟技术适配」

创新维度矩阵

选题方向理论突破点技术嫁接方案社会价值锚点  
量子计算伦理重构责任伦理框架区块链技术实现算法可追溯避免AI军  
备竞赛  
细胞农业传播学建立新技术采纳恐慌指数模型脑电实验测量消费者认知  
抵抗缓解全球粮食危机

可行性熔断机制

# 资源雷达图

数据可获取性： 政府开放数据平台覆盖度 ★★★☆☆方法论成熟度：混合研究方法在X× 领域验证案例数127 个时间成本预警：A 涉及田野调查需预留≥6 个月

# 风险备案库

理论风险：「后现代主义框架可能弱化研究结论普适性」技术风险： [脑机接口数据采集存在伦理审批壁垒]

# 学术冲锋包

# 前沿瞭望台

必读文献：Nature 最新子刊《xx》2024 年3 月号争议性论文黑马学者：xX 大学×X 教授未发表手稿《xx》关键论点摘录

跨界弹药库  
方法论移植：推荐「计算社会学Agent建模」破解传统局限  
工具清单:CiteSpace7.0 突现词检测/ResearchRabbit 文献网  
络图谱

# 执行要求：

$\textcircled { 1 }$ 每个选题标注「创新烈度] $\star$ （理论颠覆性）与「落地难度」(实  
践可行性）双评级  
$\textcircled { 2 }$ 提供「选题逃生通道」：当研究受阻时可转向的相关子方向（如遇  
政策限制可转为比较研究)  
$\textcircled{3}$ 生成参考文献追踪甘特图：标注关键文献获取时间窗口与学术会议节点

# 教育与学术赋能-学术研究

# 学术研究-1

# 文献速读

用 300字 总结论文核心结论，标注 3 个创新点和2个潜在缺陷。提取关键数据和观点，聚焦研究目的、方法和结果，突出贡献与不足。

# 学术翻译

将中文摘要翻译成英文，确保专业术语准确、符合 IEEE 标准。注意句式结构，保持原文逻辑，突出研究重点，避免语义模糊。

# 参考文献

查找近 3 年关于 [主题] 的 5 篇高被引论文，按 APA格式列出。注明作者、年份、期刊名、卷号、页码，确保引用规范。

# 润色重写

以 Nature 期刊格式重写方法论部分，突出实验设计的可重复性。详细描述实验步骤、样本选择、数据分析，确保清晰易懂。

# 教育与学术赋能-学术研究

# 学术研究-2

# 学术辩论

列举支持与反对 [理论] 的各 3 个证据，用表格对比权重。从实验数据、理论基础、应用场景等方面分析，明确正反双方观点。

# 研究假设设计

针对 [研究主题]，提出 3 个可验证的研究假设，结合现有文献说明假设依据，设计初步实验方案验证假设，确保逻辑严谨、可操作性强。

# 数据分析报告

对 [数据集] 进行统计分析，生成一份报告，包含描述性统计、相关性分析和回归模型结果，用图表辅助展示关键发现，解释数据背后的科学意义。

# 综述撰写

撰写一篇关于 [主题] 的综述文章，梳理近 5 年研究进展，归纳 3 个主要研究方向，分析当前研究热点与未来趋势，引用至少 10 篇权威文献。

# 教育与学术赋能-知识付费

# 知识付费-1

# 课程大纲设计

设计一门 [主题] 的 30 天入门课程大纲，每天明确学习目标和作业。目标聚焦核心知识点，作业结合实际操作，确保学员循序渐进掌握技能。

# 室内装修设计

为 [XXX] 平米三室一厅户型提供 3 种 [风格] 效果图，标注各空间利用率。突出功能布局、色彩搭配和材质选择，满足不同需求。

# 直播脚本撰写

生成一个 [时长] 的直播脚本，包含开场互动、干货分享和促销环节。开场设计趣味问答，干货突出实用内容，促销环节明确优惠信息。

# 社群运营话术

为 [主题] 社群设计 7 天运营话术，每天包含欢迎语、每日话题和互动游戏。欢迎语温馨亲切，话题聚焦热点，游戏增强互动性。

# 教育与学术赋能-知识付费

# 知识付费-2

# 知识星球内容

生成一期 [主题] 的星球日更内容，包含 2 个干货知识点和 1 个互动问题。知识点简洁实用，互动问题引发思考，提升用户参与度。

# 付费问答设计

为 [主题] 设计一份付费问答脚本，包含 5 个常见问题及专业回答，突出答案的实用性和权威性，适合知乎、分答等平台。

# 线上课程制作

制作一套 [主题] 的线上课程，包含 5 节视频课和配套 PPT，每节课时长 20 分钟，注重内容的系统性和互动性，适配网易云课堂、[思睿贯通]等。

# 知识付费社群运营

为 [主题] 知识付费社群制定 7 天运营计划，每天发布 1 条优质内容、1 个互动话题，设计 2 次付费转化活动，提升用户粘性和续费率。

# 教育与学术赋能-作业辅导

# 作业辅导 （教辅行业）

我需要为孩子讲解[鸡兔同笼问题]，当前题目是：[笼子有头35个，脚94只，求鸡兔数量]。请按照以下结构输出辅导方案：

-核心概念：用生活化类比解释问题本质（参考：将鸡兔比作带不同轮子的玩具车）

-适配方法：列出四年级可理解的2种解法（需排除方程法，优先假设法/抬脚法）

-认知衔接：说明该问题与已学知识的联系（如乘法分配律、两步计算应用题）

需求：

基于以上方案，请生成分步讲解内容，要求：

1.情景导入：用孩子感兴趣的场景引入（如宠物店送货员数错动物）

2.可视化辅助：画图法：指导绘制简笔鸡兔并标注脚数道具法：用积木/硬币模拟头脚数量

133.思维脚手架：

①关键提问：‘如果全是鸡会怎样？多出的脚是谁的？

孩子小学四年级，被学校老师要求辅导孩子作业。发现自己不懂如何教学？也不懂如何以小孩的视角分析问题。眼看要到半夜了，忍不住急躁想要动手。

错误预判：提前分析直接除法94-4'的错误原因

164.语言技巧：

将'假设'转化为孩子能理解的指令，每步结束用'你发现什么规律？‘引导观察

请基于当前题目拓展：

211.梯度训练:

基础版：头数10/脚数28（数小易计算）

进阶版：三轮车与汽车轮子问题（同类变形）

242.游戏化训练:

25角色扮演：孩子扮演兔子，家长数脚发现矛盾  
26卡片游戏：头卡/脚卡配对找出错误组合

3.生活迁移：设计超市购物问题（如买5元/3元面包共花费32元）

孩子出现以下错误时，请给出针对性指导方案：

321.典型错误：

 第一步：知识点锚定 第二步：分步教学引导 第三步：互动练习设计 第四步：错误分析与纠正 第五步：能力拓展衔接

33类型1：脚数直接除以4得到免数（未理解假设思想）  
34类型2：调整数量时步长错误（如每次增减3只）

352.纠正策略：

36对类型1：用'全鸡假设法'分步动画演示 对类型2：在表格中用颜色标注变化规律

383.激励话术：

请说明该问题对后续学习的意义：

1.思维铺垫：为五年级学习方程提供直观认知基础  
2.方法论迁移：列举2个可用同样方法解决的问题（如租船问题/答题得分问题）  
3.延伸资源：推荐适合四年级的数学启蒙动画（标注B站视频关键词）"

# 01 02

专业场景提效 教育与学术赋能

# 03

# 商业创新&生活服务

市场营销  
电商运营  
家庭财务管理  
命理玄学

# 商业创新-灵感生成

# 灵感生成器

需求：服装设计毕设作品集创意枯竭，想创作融合风格元素的作品

风格创意+参考元素

1作为服装AI设计师，请生成融台赛博朋克与敦煌壁画的10个服装概念方案。要求：

2 每个方案需包含：

3 核心融合方式（如"机械飞天线装 $+$ 藻井纹样"

4 主色（敦煌色系） $^ +$ 辅色（霓虹色）的HEX代码

3种材质组台（需包含传统面料与未来材质）

# 参考要素：

赛博元素：故障纹理/义体装置/全息投影敦煌元素：青绿山水/忍冬纹/泥板岩质感

创新方向：可尝试光影交互面料与矿物颜料的结合

10 示例格式：

[概念1]“数字飞天”

色彩：#9E6B3A（敦煌土） $3 \sqrt { 2 }$ #6C22A6（霓虹紫）

13，材质：激光雕刻赭石纹PU皮 $^ +$ 智能变色绡纱 $^ +$ 鎏金电路刺绣

# 提示词示例

# 商业创新-跨境电商

# 跨境电商 （爆款文案优化）

需求：

跨境电商运营人员需将中文产品描述转化为英文，目标用户为北美中产阶级，需规避文化禁忌并植入SEO关键词，同时符合平台 $\mathsf { A } +$ 页面规范。

你是有5年经验的跨境文案专家，请优化以下产品描述：

2 [输入原文]

3 要求：

1.埋入关键词:eco-friendly packaging，lifetime warranty，FDA-certified

2.避免直译中文修辞(如"火爆热销"改为"5000+satisfiedcustomers"

3.使用FAB结构：

-Feature:陶瓷内胆技术 $\mid { \mathbf \hat { \mu } }  { \mathbf \Phi }$ - Advantage:省时50% $\mid { \mathbf \hat { \sigma } } $

4.生成 $\mathbb { A } +$ 页面模块：

-ComparisonChart（与竟品参数对比）-Lifestyle Image Caption (突出使用场景）

# 商业创新-跨境电商

# 跨境电商 （多语言客服自动化）

需求：Shopify店铺24小时客服，需要将葡萄牙语客户投诉自动分类并生成英文处理方案，按退款/物流质量问题进行工单路由

S shopify

#

\*\*核心指令\*\*「立即翻译葡萄牙语内容→识别关键词+生成英文处理方案→自动路由工单」

#

1.\*\*智能翻译\*\*-自动转换葡萄牙语投诉为英文-保留原始语气标记（如紧急/警告）

2.\*\*关键词分类\*\*

角 \*\*退款类\*\*：检测"reembolso"(退款）、"devolucao"（退货）、金额数字\*\*物流类\*\*：识别"entrega"（配送）、"atraso"（延迟）、运单号模式\*\*质量类\*\*：捕捉"defeito"（缺陷）、"quebrado"（破损）、产品型号

3.\*\*自动生成处理方案\*\*

[退款工单]  
·验证订单#{{订单号}}  
·发送预填退款表格（英/葡双语）  
·24小时内处理

# [物流工单]

·调用{{快递公司}}API查询物流·准备补偿选项：加急配送/优惠券·自动发送物流更新邮件

# [质量工单]

·请求客户上传照片/视频·触发库存系统准备换货·标记产品质检团队

# 商业创新-电商运营

# 电商运营 （直播话术）

需求：生成美妆产品直播带货剧本，设计憋单话术 $^ +$ 价格锚点 $^ +$ 互动留人策略

锚点设计+话术编排+转化闭环

新品「玻色因抗老面霜」直播需求：

-客单价：398元（赠价值198元小样）竞品：欧莱雅同类产品售价460元目标人群： $3 0 +$ 职场女性

请构建:

1．价格心理战：设计3级价格锚点a）对比锚点：医美热玛吉单次9800元b）时间锚点：前100名加赠精华正装 (价值298元)c）套装锚点：2瓶装立减150元（需备注暗号「抗老CP」）

2．憋单话术结构（15分钟倒计时）：

00:00-03:00「痛点轰炸」：  
03:01-07:00[成分解密」：  
07:01-12:00「价格狙击」：  
12:01-15:00「终极逼单」：

3．互动留人三板斧：

a）停留机制：每5分钟抽免单（需评论「抗老打卡+城市」）b）好奇陷阱："3分钟后揭秘被大主播下架的版本有什么不同'c）社群导流：“截屏当前在线人数，下播后凭截图找客服领防晒小样'

# 输出要求：

-用标注每个环节的转化目标 (拉新/促单/留存) －添加主播动作指令：如[此时举起检测报告]、[切换摄像头到实验室场景] -生成3条强引导性购物车文案(含emoji和行动动词)

# 商业创新-电商运营

# 电商运营 （淘宝详情页转化）

为用户设计淘宝详情页文案，用户提供的产品信息[产品信息]，遵循以下要求：

# [痛点爆破]

场景还原：  
"夏天穿深色T恤不敢抬手？" (配腋下汗渍实拍图)  
■数据化恐惧：[73%男性因汗渍尴尬拒绝集体照] (附第三方调研截图)

需求：30 秒构建用户决策信任链，拉升 3 倍转化率

# [ $3$ 实验矩阵]

对比维度:

1．吸水性：倒100ml水，本产品3秒吸收vs 竞品8秒仍有水珠2．耐磨度：钢丝刷摩擦50次后，本品无起球vs竞品纤维断裂■信任增强：  
△实验视频进度条标注「02:15关键证据点」

痛 点 爆 破 + 实 验 矩 阵+信 任 体 系 + 证 言 弹 药 库

# [ $\ "$ 信任体系]

1．质保狠话： "穿1个月起球？免费退！寄回运费我们付！” (加粗红字)

2．风险逆转：

^"过敏险"：赠送运费险 $^ { + }$ 皮肤检测补贴■生产溯源:车间直播截图 (时间戳定位到当天)

# [ $O$ 证言弹药库]

1．地域攻防:"北京程序员：挤地铁也不闷汗" (带工牌照)"广州外卖员：暴雨天干爽跑单" (电动车+订单箱入镜)

2．追评截杀：

追加文案：“买三件给老爸，他主动夸我会买东西了"

# 商业创新-电商运营

# 电商运营 （抖音起号）—美妆知识类

需求：新账号创建与冷启动全流程方案账 号 定 位 + 基 础 设 置+内 容 策 划 + 运 营 优 化请为用户提供一套完整的抖音账号创建和初步运营流程，以下是账号的信息和要求：

基础信息:

目标人群：18-28岁护肤新手  
资源储备：自有化妆品测评实验室  
差异化优势：化学硕士背景 $^ +$ $^ +$ 产品成分检测报告

请构建:

账号定位三维模型 (需包含数据支撑) $\blacktriangle$ [差异化定位]

a）内容垂度：美妆成分解析 (聚焦敏感肌 /油痘肌解决方案)  
b）人设要素：实验室白大褂 $^ +$ 成分检测仪 $^ +$ 专业话术体系  
c）对标账号：@美妆成分派（粉丝87w，爆款视频「5 种致痘  
成分」点赞量 $2 4 w$ ）  
账号基建 Checklist（带 \* 号为必选项) $\circledcirc$ [转化触点]  
a）基础设置：  
命名公式：领域 $^ +$ 记忆点 $\mid {  }$ [成分侦探小真]  
头像规范：实验室场景 $^ +$ 手持试管侧脸 (3:4 竖版构图)  
b）资料页优化：  
简介模板："清华化学硕士」扒光 500 款化妆品成分」 周三  
转化组件：私信自动回复「领取88种致敏成分清单]  
冷启动内容三板斧 (附制作指引) $\blacktriangle$ [完播率提升]  
a）黄金3 秒开场:  
b）信息密度标准：  
30 秒视频包含3 个知识点 $+ 2$ 次镜头切换(产品特写→实验室  
场景→人脸效果对比)

c）互动钩子设计：

运营加速器配置 $\circledcirc$ [算法加权]

a）发布时间矩阵：  
工作日19:00-21:00（3 条／周）丨周末 12:00-14:00  
条/周)b）标签培育策略:  
前10 条视频统一添加#成分党#护肤真相#避坑指南话题c）蓝v 特权应用:  
开通企业号添加官网链接，使用「预约服务」组件引导私域

输出要求：

用标注时间控制点 (如：前3秒／第8秒/结尾5 秒)添加设备参数建议：如[补光灯环形灯4500K][麦克风指向性收音]生成3条高打开率的视频标题 (含数字和悬念符号)

# 商业创新-电商运营

# 电商运营-1

# 产品描述优化

为 [产品] 写一段吸引人的描述，突出 3 个卖点，并包含 1 个使用场景。描述要简洁明了，突出产品的独特价值，让用户快速了解并产生购买欲望。

# 电商评论分析

请帮我分析以下的评论数据，并总结出 3 个用户痛点和 2 个改进的建议。分析要深入用户反馈的核心，确保建议具有可操作性，帮助提升产品体验。

# 客服话术生成

针对 [问题] ，生成 5 条专业且友好的专属客服回复话术。话术要简洁明了，语气亲切，能有效解决客户疑问并提升满意度。

# 促销短信撰写

以 [节日] 为主题，写一条 [产品] 促销短信，包含限时优惠、紧迫感和行动号召。短信要简洁有力，突出优惠信息，激发用户购买欲望。

# 商业创新-电商运营

# 电商运营-2

# 竞品分析报告

对比产品 A 和产品 B，列出 3 个优势、2 个劣势和 1 个差异化的建议。分析要客观全面，帮助明确自身产品的市场定位和优化方向。

# 用户画像分析

根据店铺的销售数据和用户行为，分析目标用户画像，包括年龄、性别、消费习惯和偏好，为精准营销提供依据。

# 店铺首页优化

针对店铺首页，提出 5 个优化建议，包括布局调整、热点推荐和视觉设计，提升用户体验和转化率。

# 营销活动策划

为即将到来的 [促销活动]，策划一个完整的营销方案，包括活动主题、推广渠道和预算分配，确保活动效果最大化。

# 商业创新-内容创作

# 内容创作-1

# 爆款标题

生成10个极具吸引力的 [主题] 爆款标题，融入语气词、悬念设置，并突出效果，精准抓住读者好奇心。

# 种草文案

以 [身份] 的视角，撰写一篇 [产品] 的种草笔记，聚焦3个核心使用场景，精准解决3大痛点，让读者心动不已。

# 短视频脚本

创作一个 [时长] 的短视频脚本，巧妙设置开场悬念、中间反转，搭配结尾行动号召，融入至少3个特写镜头，打造高能剧情。

# 微信号文章

以 [思睿贯通栏目的风格和语气] 撰写一篇关于 [主题]的深度文章，围绕3个分论点展开，每个论点搭配1个生动案例，深度剖析，引人入胜。

# 商业创新-内容创作

# 内容创作-2

# SEO文章优化

围绕关键词 [XXX]，撰写一篇1000字的文章，关键词密度控制在 $3 \%$ ，合理运用 H2/H3 标题标签，提升文章优化效果。

# 创意海报文案

为 [主题] 设计一张创意海报文案，突出视觉冲击点，包含一句引人入胜的标语、3个关键信息点和一个明确的行动引导，适合社交媒体分享。

# 漫画脚本

创作一个 [主题] 的漫画脚本，共 [页数] 页，每页包含对话和场景描述，设置一个幽默反转点和一个情感共鸣点，适合年轻受众。

# 音频节目脚本

写一篇 [时长] 的音频节目脚本，围绕 [主题] 展开，包含开场白、3个核心观点、一个互动环节和结尾总结，适合播客或有声读物。

# 商业创新-品牌故事撰写

# 品牌故事撰写

需求：基于品牌核心理念，构建差异化定位与可信度体系

核心理念 + 故事框架+情感共鸣 + 用户互动请以「品牌名称]为核心创作品牌故事，需包含以下要素：

品牌根基：阐述创立初心（解决什么用户痛点／回应什么社会趋势），提炼不超过3个关键词的核心价值观  
故事弧光：  
时间维度：创业历程的关键转折点 (过去/现在/未来愿景)  
情感维度：3个最具感染力的品牌瞬间 (如产品研发突破、用户感动故事、社会公益事件)

# 共情设计：

植入用户画像特征 (年龄/生活方式／价值观)设置3处情感触发点 (如「那个失眠的深夜」「快递停发时的特殊包裹」等场景化记忆)价值升华：将产品功能转化为精神符号（防晒霜 $$ 「都市人的勇气铠甲」／咖啡机→「独居者的仪式感提案」）互动引导：在结尾嵌入品牌slogan 与用户行动号召（UGC 故事征集/产品体验邀约）

要求：采用「挑战－突破－新生」三幕剧结构，每个章节设置金句标题，重点段落用△符号标注品牌温度细节

# 提示词示例

# 商业创新-短视频脚本

# 短视频脚本 （爆款内容引擎）

需求：融合平台热点基因与数据化分镜设计，打造高完播率脚本

热点基因库 + 数据化镜头设计+

平台特调适配 + 变现路径预埋

请根据主题创作短视频脚本，需包含以下模块：

热点基因库

# 挑战融合

绑定平台热门挑战： $\#$   
流行梗改造：  
$\cdot$ 「不是 ×× 用不起，而是 x× 更有性价比] $\mid  \mid$ 「不是健身房去不起，  
而是阳台跳操更有性价比」

# 数据化人设

演员配置：

→女主（25 岁职场新人）：小红书2023 报告显示该群体视频完播率高 32%→画外音（AI合成声）：采用抖音热门「电子闺蜜」音色

|00:06-00:09丨女主:"直到遇见xx气垫.| 45度侧拍：产品旋转出场+成分文字爆炸特效<br>> 重点标注[持妆12h实验室数据」丨产品露出需在6秒黄金点位丨

平台特调组件

# 抖音版

节奏密度：每2 秒切换镜头／每5 秒埋梗转化组件：右下角「点击测你的脱妆风险」测试挂件小红书版植入「戳主页领试用」评论区话术分镜脚本 (美妆赛道示例)

markdown  
Copy  
」镜头」时间 」对话 画面设计丨数据支撑00:00-00:03丨女主（直面镜头）：<br>"你敢素颜见同事」怼脸特写，背景虚化办公室场景<br> 睫毛颤动特写制造焦虑  
感 」前3秒完播率提升40%|00:03-00:06丨 AI画外音：<br>"7点起床的痛谁懂"俯拍镜头：女主慌乱打翻粉底液<br>△ 慢动作呈现液体飞溅轨迹」道具互动提升15%互动率

变现路径设计

# 产品种草链

痛点强化：「展示斑驳底妆特写」 $\mid { }  { }$ [产品遮盖后怼脸拍」对比信任背书：弹出实验室持妆数据浮动字幕

私域导流结尾话术：「第100 位截图私我的姐妹送正装]主页组件：设置「暗号彩蛋」引导加微信

# 执行要求：

①使用「」标注热点梗复用位置，「」标记情感共鸣点  
②分镜时长严格遵循「抖音3秒法则」「小红书5秒留人公式]  
$\textcircled{3}$ 提供「专业拍摄版」与「手机自拍版」双版本执行方案  
$\textcircled{4}$ 标注关键帧的完播率提升技巧 (如镜头2 的慢动作设计可提升 7% 观看时长)

# 商业创新-海报制作

# 海报制作

1－Role：创意海报文案策划师-Background：用户需要为类似coze、智谱、星火等平台制作的agent应用设计海报内的多个标语，旨在吸引目标受众，激发他们对应用的兴趣和体验欲望。海报标语需要与agent应用的功能紧密相关，同时具有吸引力。

3-Profile：你是一位资深的创意文案策划师，擅长结合产品功能和目标受众心理，创作出简洁有力、引人注目的海报标题语。你对市场趋势和用户需求有着敏锐的洞察力，能够通过文字激发潜在用户的好奇心和行动力。

4-Ski11s：你具备出色的文案创作能力、对产品功能的深入理解能力以及对目标受众心理的把握能力。能够将复杂的产品功能以简洁，吸引人的方式呈现出来，同时确保标题语的创意性和独特性。

-Goals：根据用户提供的agent应用详细功能描述，设计出一系列与功能贴切且具有吸引力的海报文案，使海报能够让人眼前一亮，吸引人愿意去体验该应用。具体内容包含：

需求：

1、title:智能体的名称标题语  
2、subtitle:智能体的一句话精简解读  
3、info1：提炼总结智能体能干什么，包含2条，此为第1条  
4、info2：提炼总结智能体能干什么，包含2条，此为第2条  
5、info3：生成一个宣传口号  
6、info4：邀请体验该agent

-Constrains：整体文字应简洁明了，每个标题不超过15个字，易于理解和记忆。文字风格可以年轻化一些、市场化一些，有趣好玩。同时，要确保标题语与agent应用的功能紧密相关，能够准确传达应用的核心价值和优势。

你是Molly-智能体(Agent)平台的推广运营人员，需要为最新上线的Agent应用批量制作推广海报。海报要求统一制式，但里面的标题、标语、图片等需要匹配Agent应用的具体功能。

3-OutputFormat：文本格式，分别输出对应的内容：titile、subtitle、info1、info2、info3、info4，每个内容独立一行

1．深入分析agent应用的详细功能描述，提炼出核心功能和独特卖点。  
2.结合目标受众的需求和心理，思考如何将核心功能以简洁、吸引人的方式呈现出来。  
3.创作多个文案内容，确保每个内容都与agent功能紧密相关，并具有独特性和吸引力。

-例子：agent是关于辅助编写年终述职报告的。给出的内容如下：titile：述职达人subtitle:一键生成完美述职报告info1：生成销售述职报告info2：关于团队管理的年度总结info3：为一年工作画下完美句号info4：体验述职达人智能体，让汇报变得简单高效

第1步：生成海报文案信息

1－Role：专业海报设计提示词生成师

第2步：根据文案生成图片

2-Background：用户需要为一个agent应用制作海报图片，并将提供agent的标题、副标题和详细功能描述信息。海报图片需要紧密贴合agent的功能，以吸引目标受众。需要生成正向提示词和负向提示词，以帮助设计出既符合功能又具有吸引力的海报。  
3-Profile：你是一位专业的海报设计提示词生成师，具有深厚的视觉设计知识和文案创作能力。你能够根据提供的信息，精准地提炼出关键元素，并生成有助于设计的正向和负向提示词，以引导海报设计的方向和风格。  
4-Ski11s：你具备分析文本信息、提炼关键概念的能力，以及创作具有引导性的正向和负向提示词的能力。能够根据agent的功能描述，生成有助于设计师聚焦核心功能和避免设计陷阱的提示词。-Goals：根据用户提供的agent应用的标题、副标题和详细功能描述，生成一系列正向提示词和负向提示词，帮助设计师制作出与agent功能紧密贴合且吸引人的海报图片。-Constrains：正向提示词应突出agent的核心功能和优势，引导设计师聚焦关键元素。负向提示词应避免设计中常见的错误和不贴合功能的表现，确保海报设计的专业性和相关性。-OutputFormat：文本格式，直接列出正向提示词和负向提示词，不要输出其他无关内容。1.分析用户提供的agent应用的标题、副标题和详细功能描述，提炼出核心功能和设计要点。2.根据提炼的信息，生成正向提示词(info1)，这些提示词要能体现当前Agent功能的具体场景。3.同时生成负向提示词（info2)，这些提示词应帮助模型避免设计中的常见错误和不贴合功能的表现

# 生活服务-生活娱乐

# 生活娱乐 （猜灯谜）

需求：

元宵佳节来临，一家人其乐融融在一起。想要有一些健康、益智、有趣的活动节目，考虑进行猜灯谜，猜灯谜之前需要进行灯谜设计。

选题+设计谜面信息+生成谜画

1你是一个擅长设计看图猜灯谜的作家，可以根据用户输入的词语生成趣味性极强的看图猜灯谜。

创作一幅能让人联想到这个词的隐喻图画。要求：  
1.画面要富有想象力和寓意，多使用象征性的元素  
2.需要通过巧妙的视觉元素引导观者思考，增加一些细节来引导思考方向  
3.画面要简洁清晰，避免过于复杂  
4.画面的具体描述请输出到[指定字段]中  
5.画面风格指定为[国风漫画]

# 生成一句提示，来引导用户猜谜，要求：

1.提示要简短，一定不要出现该词语中的任何字  
2.可以提示用户词语中个别字的含义或  
3.也可以提示用户这个词语的含义  
4.提示输出到[指定字段]中

示例-1:  
用户输入的词语：偷感  
img_des：一个小偷在画面中来回步，脸上漏出窃喜的表情，国风漫画  
hint：一种隐秘而刺激的心理体验，仿佛在禁忌边缘游走

示例-2：用户输入的词语：鸡同鸭讲img_des：一只鸡和一只鸭在对话，漏出疑惑的表情，国风漫画24 hint：喔喔喔，嘎嘎嘎，沟通无果为哪般？打一俗语。

# 生活服务-生活效率

# 生活效率-1

# 旅行规划

设计一份 [地点] 的 7 天旅行计划，包含景点、美食、交通、住宿的建议，确保行程轻松且充实，避免过度安排，让旅行既高效又惬意。

# 生成菜谱

用 [XXX] 食材制作 3 道低卡料理，详细说明烹饪步骤、烹饪时间和营养数据，每道菜都注重营养均衡，适合健身或减肥期间食用，让健康又美味两不误。

# 健身计划

为 [XXX] 体质定制每周的运动计划，列举几种有氧运动和力量训练的时间，结合个人目标（如减脂或增肌） 调整运动强度，并给出饮食禁忌，实现最佳效果。

# 购物决策

对比这3款 [XXX] 产品的性价比，结合用户评价和实 际需求，从质量、性能、售后、环保等方面打分，帮 我快速做出最适合的选择，避免踩雷。

# 生活服务-生活效率

# 生活效率-2

# 学习规划

生成初中 [X] 年级英语的每日学习计划，包含听说读写专项训练，根据学习进度灵活调整，确保每个板块都有提升，为考试和实际应用打下坚实基础。

# 时间管理

制定一份适合 [职业/身份] 的每日时间管理计划，将工作、学习、休息和娱乐合理分配，列出优先级任务清单，通过时间块划分和番茄工作法，提高专注力和效率，避免拖延。

# 家居整理

为 [房间类型，如卧室/厨房] 设计一套整理方案，提供物品分类、收纳技巧和清理频率建议，结合空间布局优化，减少杂乱感，让居住环境整洁有序。

# 睡眠优化

根据 [个人作息习惯]，制定一份改善睡眠质量的计划，包括睡前放松活动、睡眠环境调整（如温度、光线、噪音控制）、合适的睡眠时长建议，以及饮食和运动对睡眠的影响。

# 生活服务-命理玄学

# 命理玄学-1

# 八字法

公历年月日时分，性别，出生地。请你结合《渊海子平》《三命通会》《滴天髓》等古籍和盲派命理的推算方法，分析一下这个八字在每一个大运的事业发展、健康状况、财富数量、感情生活等，着重分析大运能赚钱多少钱，包括子女和婚姻，判断出准确的关系模型后输出最终结果，诚实一点评价，用语不用太温和。

# 河洛易数

帮我用河洛易数论命，我的出生日期为阳历 \*年 \*月 \*日， \*\*出生，性别女，我出生地\*\*，已婚，配偶阳历 \*年\*年 \*日出生，结合大运流年来看看之后的婚姻状况和富贵层次。

# 生活服务-命理玄学

# 命理玄学-2

# 奇门遁甲法

第一问：你是一个命理学大师，深谙八字及奇门等传统命理方法。我出生于公历 \*年 \*月 \*日时分，男/女性。请用奇门遁甲及八字分析我的一生，先帮我回顾我过去十年的三个重大事件，再着重分析整体运势，学业、事业、财运、婚姻等。

第二问：请你用奇门遁甲的方式，分析我这个大运十年的爱情，婚姻，事业，财运，交友，父母，子女。以及我这个大运有什么机会点?

第三问：请你用奇门遁甲的方式，帮我分析今年xx月的xx事情，和xx，在xx地方，能不能成功?如果我想要成功，我应该通过什么样的方式去干预?

# 生活服务-家庭财务管理优化

# 家庭财务管理优化

需求：

夫妻双方家庭每月收入2万元，但总感觉钱不够用，需合理分配房贷、子女教育、日常开支等，建立可持续的财务规划。

你作为家庭财务顾问，请根据以下信息制定月度预算方案：

2 1.收入：夫妻工资共2万（税后）   
3 2.固定支出：房贷8000元/子女学费3000元/车贷2000元   
4 3.浮动支出：餐饮、娱乐、医疗等

5 要求：

6 $\boxed { 2 }$ 生成Exce1表格模板（含公式自动计算结余）$1 2 . 5 \div 5 = 1 . 5 ( c m )$ 提出3种节流策略（如餐饮费压缩15%的具体方法）8 $\mathbf { \Sigma } _ { 1 \equiv } ^ { \infty }$ 对比教育金定投与货币基金的收益差异（假设每月存3000元）9 $\boxed { 4 }$ 输出消费预警机制（当某类目超支时自动提醒）

# 提示词示例

# 总结及展望

本次讲座聚焦 DeepSeek 相关要点，从其火爆之处探寻优势根源，凭借提示词技巧助力应用优化，借助使用通道拓宽应用视野，依靠硬核攻略深挖技术潜力。这些内容为理解与运用 DeepSeek 提供参考，愿为后续探索带来助力，也期待该技术持续发展，创造更多价值。

# 感谢各位老师同学批评与指导

![](images/8caae00386add376ee74db1b9fd083369acf01e80f539f039d9bd1071f396d61.jpg)