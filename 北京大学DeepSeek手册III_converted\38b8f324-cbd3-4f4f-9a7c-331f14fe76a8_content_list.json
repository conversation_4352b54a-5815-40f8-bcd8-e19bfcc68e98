[{"type": "text", "text": "DeepSeek-R1 \\ Kimi 1.5 及 类强推理模型开发解读 ", "text_level": 1, "page_idx": 0}, {"type": "text", "text": "主要研究方向：大语言模型对齐与可扩展监督DeepSeek-R1 开创RL加持下强推理慢思考范式新边界", "page_idx": 0}, {"type": "text", "text": "", "page_idx": 1}, {"type": "text", "text": "DeepSeek-R1 Zero 及 R1 技术剖析", "page_idx": 1}, {"type": "text", "text": "Pipeline 总览 \\ DeepSeek-V3 Base \\ DeepSeek-R1 Zero 及 R1 细节分析RL 算法的创新：GRPO及其技术细节", "page_idx": 1}, {"type": "text", "text": "DeepSeek-R1 背后的Insights & Takeaways：RL加持下的长度泛化 \\ 推理范式的涌现DeepSeek-R1 社会及经济效益", "page_idx": 1}, {"type": "text", "text": "技术对比探讨", "page_idx": 1}, {"type": "text", "text": " STaR-based Methods vs. RL-based Methods 强推理路径对比(DS-R1 \\ Kimi-1.5 \\ o-series)  \n蒸馏 vs. 强化学习驱动：国内外现有各家技术路线对比分析及Takeaways  \n PRM & MCTS 的作用  \n 从文本模态到多模态  \n 其他讨论：Over-Thinking 过度思考等", "page_idx": 1}, {"type": "text", "text": "未来方向分析探讨", "page_idx": 1}, {"type": "text", "text": "模态穿透赋能推理边界拓展：Align-DS-V  \n合成数据及Test-Time Scaling: 突破数据再生产陷阱  \n强推理下的安全：形式化验证Formal Verification \\ 审计对齐Deliberative Alignment", "page_idx": 1}, {"type": "text", "text": "补充拓展：DeepSeek-V3 解读", "page_idx": 1}, {"type": "text", "text": "DeepSeek-R1 开创RL加持下强推理慢思考范式新边界", "text_level": 1, "page_idx": 2}, {"type": "text", "text": "OpenAI o1 开启后训练Post-Training 时代下的RL新范式：后训练扩展律Post-Training Scaling Law DS-R1 独立发现了一些通往o1路上的核心理念，并且效果还好到受到了OpenAI 的认可", "page_idx": 2}, {"type": "text", "text": "如何通过有效的 Test-Time Scaling 和 Train-Time Scaling 提升模型的推理能力？", "page_idx": 2}, {"type": "text", "text": "得益于纯大规模强化学习，DeepSeek-R1 具备强大推理能力与长文本思考能力，继开源来备受关注。", "page_idx": 2}, {"type": "text", "text": "DeepSeek R1-Zero 和 R1的出现再次证明了强化学习的潜力所在：", "page_idx": 2}, {"type": "text", "text": "R1-Zero 从基础模型开始构建，完全依赖强化学习，而不使用人类专家标注的监督微调（SFT）；", "page_idx": 2}, {"type": "text", "text": "随着训练步骤增加，模型逐渐展现出长文本推理及长链推理能力；", "page_idx": 2}, {"type": "image", "img_path": "images/bde048385a9c37dc55e4643981d9286ef1d5c077051ec461e2d2b0cb9c4b9f13.jpg", "img_caption": ["随着推理路径增长，模型表现出自我修复和启发式搜索的能力；"], "img_footnote": [], "page_idx": 2}, {"type": "image", "img_path": "images/99fd565ee33856c42f2da353f46f7b13396f773e2e333d42eca3c9e4173830df.jpg", "img_caption": [], "img_footnote": [], "page_idx": 2}, {"type": "text", "text": "DeepSeek-R1 开创RL加持下强推理慢思考范式新边界", "text_level": 1, "page_idx": 3}, {"type": "text", "text": "得益于强大的推理能力与长文本思考能力，DeepSeek R1在复杂任务上表现卓越，成为开源领域的又一里程碑，标志着开源社区在与闭源大模型（如OpenAI o1 系列）的竞争中迈出了关键性一步。", "page_idx": 3}, {"type": "text", "text": "DeepSeek-R1 在数学代码任务上表现突出", "text_level": 1, "page_idx": 3}, {"type": "text", "text": " Deepseek  R1在AIME2024上获得了 $7 9 . 8 \\%$ 的成绩，略高于OpenAI-o1-1217。在MATH-500上，获得 $9 7 . 3 \\%$ ", "page_idx": 3}, {"type": "text", "text": "的惊人成绩，表现与OpenAI-o1-1217相当。", "page_idx": 3}, {"type": "text", "text": "在编码相关的任务中表现出专家水平，在Codeforces上获得了2029 Elo评级，在竞赛中表现优于 $9 6 . 3 \\%$ 的人类参与者", "page_idx": 3}, {"type": "text", "text": "DeepSeek-R1 在知识类问答上推动科学探索边界：", "text_level": 1, "page_idx": 3}, {"type": "text", "text": "MMLU \\ MMLU-Pro \\ GPQA Diamond 等 STEMrelated 榜单上取得良好表现 ", "page_idx": 3}, {"type": "text", "text": "${ \\bf \\Pi } > { \\bf R 1 }$ 展现出强推理模型在 AI-Driven Research 的潜力", "page_idx": 3}, {"type": "text", "text": "在长文本依赖任务如 FRAMEs 和 事实性推断任务Simple-QA上表现突出", "page_idx": 3}, {"type": "table", "img_path": "images/57e3d0fcddb0d4eb8e655ee9cc3431e841d84e3678756d87c327edaaaac62a15.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td colspan=\"2\">Benchmark (Metric)</td><td rowspan=\"2\">Claude-3.5-</td><td colspan=\"3\">GPT-4o DeepSeek OpenAI OpenAI|DeepSeek</td><td rowspan=\"2\">01-mini o1-1217</td><td rowspan=\"2\">R1</td></tr><tr><td>Architecture</td><td>Sonnet-1022</td><td>0513</td><td>V3 MoE</td><td></td></tr><tr><td rowspan=\"4\"></td><td>#Activated Params</td><td></td><td></td><td>37B</td><td></td><td></td><td>MoE 37B</td></tr><tr><td>#TotalParams</td><td></td><td>=</td><td>671B</td><td>-</td><td></td><td>671B</td></tr><tr><td>MMLU (Pass@1)</td><td>88.3</td><td>87.2</td><td>88.5</td><td>85.2</td><td>91.8</td><td>90.8</td></tr><tr><td>MMLU-Redux (EM)</td><td>88.9</td><td></td><td>89.1</td><td>86.7</td><td></td><td>92.9</td></tr><tr><td rowspan=\"8\">English</td><td>MMLU-Pro (EM)</td><td></td><td>88.0</td><td></td><td></td><td></td><td></td></tr><tr><td></td><td>78.0</td><td>72.6</td><td>75.9</td><td>80.3</td><td></td><td>84.0</td></tr><tr><td>DROP (3-shot F1)</td><td>88.3</td><td>83.7</td><td>91.6</td><td>83.9</td><td>90.2</td><td>92.2</td></tr><tr><td>IF-Eval (Prompt Strict)</td><td>86.5 65.0</td><td>84.3</td><td>86.1</td><td>84.8</td><td></td><td>83.3</td></tr><tr><td>GPQA Diamond (Pass@1)</td><td>28.4</td><td>49.9</td><td>59.1</td><td>60.0</td><td>75.7</td><td>71.5</td></tr><tr><td>SimpleQA (Correct)</td><td>72.5</td><td>38.2</td><td>24.9</td><td>7.0</td><td>47.0</td><td>30.1</td></tr><tr><td>FRAMES (Acc.) AlpacaEval2.0 (LC-winrate)</td><td>52.0</td><td>80.5 51.1</td><td>73.3 70.0</td><td>76.9 57.8</td><td></td><td>82.5</td></tr><tr><td>ArenaHard (GPT-4-1106)</td><td>85.2</td><td>80.4</td><td>85.5</td><td>92.0</td><td>1 1</td><td>87.6 92.3</td></tr><tr><td rowspan=\"5\">Code</td><td>LiveCodeBench (Pass@1-COT)</td><td>38.9</td><td>32.9</td><td>36.2</td><td>53.8</td><td>63.4</td><td>65.9</td></tr><tr><td>Codeforces (Percentile)</td><td>20.3</td><td>23.6</td><td>58.7</td><td>93.4</td><td>96.6</td><td>96.3</td></tr><tr><td>Codeforces (Rating)</td><td>717</td><td>759</td><td>1134</td><td>1820</td><td>2061</td><td>2029</td></tr><tr><td>SWE Verified (Resolved)</td><td>50.8</td><td>38.8</td><td>42.0</td><td>41.6</td><td>48.9</td><td>49.2</td></tr><tr><td>Aider-Polyglot (Acc.)</td><td>45.3</td><td>16.0</td><td>49.6</td><td>32.9</td><td>61.7</td><td>53.3</td></tr><tr><td rowspan=\"3\">Math</td><td>AIME 2024 (Pass@1)</td><td>16.0</td><td>9.3</td><td>39.2</td><td>63.6</td><td>79.2</td><td>79.8</td></tr><tr><td>MATH-500 (Pass@1)</td><td>78.3</td><td>74.6</td><td>90.2</td><td>90.0</td><td>96.4</td><td>97.3</td></tr><tr><td>CNMO 2024 (Pass@1)</td><td>13.1</td><td>10.8</td><td>43.2</td><td>67.6</td><td></td><td>78.8</td></tr><tr><td rowspan=\"4\">Chinese C-Eval (EM)</td><td>CLUEWSC (EM)</td><td>85.4</td><td>87.9</td><td>90.9</td><td>89.9</td><td></td><td>92.8</td></tr><tr><td></td><td>76.7</td><td>76.0</td><td>86.5</td><td>68.9</td><td></td><td>91.8</td></tr><tr><td>C-SimpleQA (Correct)</td><td>55.4</td><td>58.7</td><td>68.0</td><td>40.3</td><td></td><td>63.7</td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr></table></body></html>", "page_idx": 3}, {"type": "text", "text": "回顾：Pre-Training Scaling Law ", "text_level": 1, "page_idx": 4}, {"type": "text", "text": " Pre-Training Scaling Laws: 预训练模型上广泛观察到的现象，协调了计算量C、模型参数量N和数据大小D之间的关系", "page_idx": 4}, {"type": "text", "text": "背景：", "text_level": 1, "page_idx": 4}, {"type": "text", "text": "·Scalinglaws：在生成模型训练当中被广泛观察到的现象。", "page_idx": 4}, {"type": "text", "text": "·对于计算量 $C _ { \\prime }$ 模型参数量N和数据大小D，当不受其他两个因素制约时，模型性能与每个因素都呈现幂律关系。", "page_idx": 4}, {"type": "image", "img_path": "images/86099f8bef8670baf4e3ffddb4578f018dbe7ae68e9bbbeb576adc2cfc93c0a6.jpg", "img_caption": ["OpenAlcodebasenextwordprediction "], "img_footnote": [""], "page_idx": 4}, {"type": "text", "text": "Bits per word ", "page_idx": 4}, {"type": "image", "img_path": "images/babc6bf277f8fa16a4937096d4768bdff6b66a55f491cb2f544e94c77453f593.jpg", "img_caption": ["Commoncarbon footprintbenchmarks "], "img_footnote": [], "page_idx": 4}, {"type": "text", "text": "Compute = 6 \\* Parameters \\* Data ", "text_level": 1, "page_idx": 4}, {"type": "text", "text": "·Motivation:EfficientlytrainingLARGEmodel ", "page_idx": 4}, {"type": "text", "text": "Expensive:TrainingGPT-3requiredat least\\$4,6o0,000 ", "page_idx": 4}, {"type": "text", "text": "Extensiveresources:many trainingdata,largenetwork ", "page_idx": 4}, {"type": "text", "text": "bs of CO2 equivalent Exp   \nondtifightb/wYndSF1   \nHumanlife(avg1year)   \nAmericanlife(avg1year) 36,156 B   \nUS car including fuel(avg1lifetime) 126,000 1,216,950 103,617 ", "page_idx": 4}, {"type": "text", "text": "Biggermodels,moredata $$ usuallybetterperformance ", "page_idx": 4}, {"type": "text", "text": "C\\~ 6ND ", "text_level": 1, "page_idx": 4}, {"type": "text", "text": "IncreaseN->betterperformance ", "page_idx": 4}, {"type": "text", "text": "Thisformulaisuseful!! ", "text_level": 1, "page_idx": 4}, {"type": "text", "text": "IncreaseD->betterperformance ", "page_idx": 4}, {"type": "text", "text": "$C =$ numberofFLOPs(computations) $\\mathbf { N } =$ numberofmodelparameters ${ \\bf D } = { \\bf \\partial }$ amount of training data ", "page_idx": 4}, {"type": "text", "text": "Butwe havea budgeton $C \\sim 6 N D$ ", "page_idx": 4}, {"type": "text", "text": "Howto maximizemodel performance byallocating Cto Nand D? ", "text_level": 1, "page_idx": 4}, {"type": "text", "text": "[DataSource:(St<PERSON>bell etal.2019)][Data Source:(Pattersonetal.2021)] <PERSON>,etal.ScalingLawsforNeuralLanguageModels. ", "page_idx": 4}, {"type": "text", "text": "举个例子：", "text_level": 1, "page_idx": 4}, {"type": "text", "text": "假设一个模型有 $N = 1 0 \\sim 9$ （10亿个参数），并且训练数据集的规模是 $D = 1 0 \\sim \\{ 1 2 \\}$ （1万亿个token）。", "page_idx": 4}, {"type": "text", "text": "使用公式 $C = 6 N D$ ，总的计算量就是：", "page_idx": 4}, {"type": "equation", "text": "$$\nC = 6 \\times 1 0 ^ { 9 } \\times 1 0 ^ { 1 2 } = 6 \\times 1 0 ^ { 2 1 } \\mathrm { F L O P s }\n$$", "text_format": "latex", "page_idx": 4}, {"type": "text", "text": "这表明要训练这个模型，大约需要 $6 \\times 1 0 ^ { 2 1 }$ 次浮点运算。", "page_idx": 4}, {"type": "text", "text": "回顾：Post-Training Scaling Law ", "text_level": 1, "page_idx": 5}, {"type": "text", "text": " Post-Training 阶段，随着训练时计算量（来自RL的Training阶段）和 Test-Time 计算量（例如Test-Time Search）的增长，模型性能（例如数学推理能力）也会随之提升 Post-Training Scaling Laws 下 训练时计算量 多了一个新的变量：Self-Play 探索时 LLMInference 的计算量", "page_idx": 5}, {"type": "text", "text": "Our large-scale reinforcement learning algorithm teaches the model how to think productively using its chain of thought in a highly data-efficient training process.We have found that the performance of o1 consistently improves with more reinforcement learning (train-time compute) and with more time spent thinking (test-time compute).The constraints on scaling this approach differ substantially from those of LLM pretraining,and weare continuing to investigate them. ", "page_idx": 5}, {"type": "image", "img_path": "images/f4b13075813b1e8e6fcafbf050ac2f6ba0286126f065200b5cdf5341d82e1821.jpg", "img_caption": [], "img_footnote": [], "page_idx": 5}, {"type": "text", "text": "回顾：Post-Training Scaling Law ", "text_level": 1, "page_idx": 6}, {"type": "text", "text": "为什么我们需要后训练 Scaling-Law ?", "text_level": 1, "page_idx": 6}, {"type": "text", "text": "随着模型尺寸逐渐增大，预训练阶段参数 Scaling Up 带来的边际收益开始递减；如果想要深度提升模型推理能力和长程问题能力，基于RL的 Post-Training 将会成为下一个突破点。", "page_idx": 6}, {"type": "text", "text": "自回归模型在数学推理问题上很难进步的一点在于没有办法进行回答的自主修正，如果仅是依靠生成式方法和扩大参数规模，那么在数学推理任务上带来的收益不会太大。所以需要寻找额外的 ScalingLaws [1]。", "page_idx": 6}, {"type": "text", "text": "One significant challenge in mathematical reasoning is the high sensitivity to individual mistakes (<PERSON> et al., 202la). When generating a solution,autoregressive models have no mechanism to correct their own errors. Solutions that veer off-course quickly become unrecoverable. If we rely purely on generative methods and extrapolate from current trends,we will require an exorbitant parameter count to achieve even moderate performance on distributions as challenging as the MATH dataset (<PERSON><PERSON><PERSON><PERSON> et al., 2021). This evidence strongly motivates the search for methods with more favorable scaling laws. ", "page_idx": 6}, {"type": "image", "img_path": "images/b57c3221ef2623fa9c87a1a69ef2f1fffa69e9234e3d796914e5806af85defc7.jpg", "img_caption": ["Comparing Test-timeand Pretraining Compute inaFLOPsMatchedEvauation "], "img_footnote": [], "page_idx": 6}, {"type": "text", "text": "DeepSeek-R1 技术剖析：DeepSeek-R1 Zero ", "text_level": 1, "page_idx": 7}, {"type": "text", "text": "DeepSeek-R1 Zero: 无需监督微调SFT，纯强化学习驱动的强推理模型", "text_level": 1, "page_idx": 7}, {"type": "image", "img_path": "images/7e8c43465a9fc48e963ea2c1eaa0051b37957922c51fafa1bfd4c24b65a9602a.jpg", "img_caption": [], "img_footnote": [], "page_idx": 7}, {"type": "table", "img_path": "images/50e580b19507bd735b1250ada6ebf1c357967e093f3054ab836c50468e7d6d73.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td rowspan=\"2\">Model</td><td colspan=\"2\">AIME 2024</td><td rowspan=\"2\">MATH-500</td><td rowspan=\"2\">GPQA Diamond</td><td rowspan=\"2\">LiveCode Bench</td><td rowspan=\"2\">CodeForces</td></tr><tr><td>pass@1</td><td>cons@64</td></tr><tr><td></td><td>63.6</td><td></td><td>pass@1</td><td>pass@1</td><td>pass@1</td><td>rating</td></tr><tr><td>OpenAI-o1-mini OpenAI-o1-0912</td><td>74.4</td><td>80.0 83.3</td><td>90.0 94.8</td><td>60.0 77.3</td><td>53.8 63.4</td><td>1820 1843</td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>DeepSeek-R1-Zero</td><td>71.0</td><td>86.7</td><td>95.9</td><td>73.3</td><td>50.0</td><td>1444</td></tr></table></body></html>", "page_idx": 7}, {"type": "text", "text": "大规模推理为中心的强化学习， 提升模型数学代码能力", "text_level": 1, "page_idx": 7}, {"type": "image", "img_path": "images/0e3041ca2e43334efc76fb9a04b4b8289c950e147fabe9e860405920e2971bf2.jpg", "img_caption": ["RL驱动下自然涌现长文本推理能力"], "img_footnote": [], "page_idx": 7}, {"type": "text", "text": "DeepSeek-R1 Zero: 无需监督微调SFT，纯强化学习驱动的强推理模型", "text_level": 1, "page_idx": 8}, {"type": "text", "text": "奖励建模： 基于规则的奖励 (Rule-Based Reward) : 准确率奖励 $+$ 格式奖励准确率奖励 Accuracy Rewards: 判断答案是否是正确的格式奖励Format Rewards: 规劝模型生成答案的过程是 <think> 和 </think>没有使用Reward Model, 因为ORM和PRM等基于神经网络的都可能遭受reward hacking而retraining reward model 需要大量的计算资源，可能会复杂化整个流程", "page_idx": 8}, {"type": "text", "text": "", "page_idx": 8}, {"type": "text", "text": "", "page_idx": 8}, {"type": "text", "text": "训练模板：选择最简单的Thinking  Process，直接观察到最直接的RL过程下的表现", "page_idx": 8}, {"type": "text", "text": "A conversation between User and Assistant.The user asks a question,and the Assistant solves it. The assistant first thinks about the reasoning process in the mind and then provides the user with the answer. The reasoning process and answer are enclosed within <think> </think> and <answer></answer> tags,respectively,i.e.,<think> reasoning process here $<$ /think> <answer> answer here $<$ /answer>.User: prompt. Assistant: ", "page_idx": 8}, {"type": "text", "text": "Table 1| Template for DeepSeek-R1-Zero. prompt willbe replaced with the specific reasoning question during training. ", "page_idx": 8}, {"type": "text", "text": "DeepSeek-R1 技术剖析：DeepSeek-R1 Zero ", "text_level": 1, "page_idx": 9}, {"type": "text", "text": "DeepSeek-R1 Zero: 无需监督微调SFT，纯强化学习驱动的强推理模型", "text_level": 1, "page_idx": 9}, {"type": "text", "text": "理为中心大规模强化学习：组相对策略优化（GRPO） $+$ 瞄准 Reasoning 推理任务", "page_idx": 9}, {"type": "text", "text": "自我迭代提升Self-Evolution：随着训练步数的增长，模型的thinking response length 逐渐增加（对应着 test-time computation increasing）", "page_idx": 9}, {"type": "text", "text": " Aha moment: 自然学会增加更多的推理时间，反思评价先前步骤、探索其他方法", "page_idx": 9}, {"type": "table", "img_path": "images/57ce11aee34d167f12c25f9815895838a51ff53ee98a53bc78caf642aabf26fc.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>Question: If a >1, then the sum of the real solutions of √a-√a+x = x is equal to</td></tr><tr><td>Response:<think> To solve the equation √a- √a +x = x,let's start by squaring both ·</td></tr><tr><td>√a-√a+x)=x²=→a-√a+x=x².</td></tr><tr><td>Rearrange to isolate the inner square root term: (a-x²)²=a+x=→a²-2ax²+(x²)²=a+x =x4-2ax²-x+(a²-a)=0</td></tr><tr><td></td></tr><tr><td>Wait, wait. Wait. That's an aha moment I can flag here.</td></tr><tr><td>Let's reevaluate this step-by-step to identify if the correct sum can be .</td></tr><tr><td>We started with the equation:</td></tr><tr><td>√a-√a+x=x</td></tr><tr><td>First,let's square both sides:</td></tr><tr><td></td></tr><tr><td>a-√a+x=x² =→√a+x=a-x² Next,I could square both sides again, treating the equation: ·</td></tr></table></body></html>", "page_idx": 9}, {"type": "image", "img_path": "images/4941c310cf80236d1124c8070374276b0e162ca8cbd54903f46becb96726707e.jpg", "img_caption": [], "img_footnote": [], "page_idx": 9}, {"type": "text", "text": "DeepSeek-R1 Zero的关键启示", "text_level": 1, "page_idx": 10}, {"type": "text", "text": "传统RLHF背景下，SFT通常被认为是不可或缺的一步，其逻辑先用大量人工标注的数据来让模型初步掌握某种能力（如对话或者语言风格），然后再用RL来进一步优化性能", "page_idx": 10}, {"type": "text", "text": "DeepSeek-R1 系列跳过对于大规模人工标注数据的依赖", "page_idx": 10}, {"type": "text", "text": "无需构建和维护高质量的SFT数据集，而是让模型直接在RL环境中进行探索", "page_idx": 10}, {"type": "text", "text": "类比：初学者在没有老师指导的情况下，通过不断的尝试和错误来掌握一门新的技能。", "page_idx": 10}, {"type": "text", "text": " 这种自主学习的方式，不仅节省了大量的标注成本；  \n 更重要的是，它让模型能够自由地探索解决问题的路径，而不是被预先设定的模式所束缚。", "page_idx": 10}, {"type": "text", "text": "DeepSeek-R1 Zero的关键启示", "text_level": 1, "page_idx": 11}, {"type": "text", "text": "跳过SFT阶段，直接运用纯强化学习拓展推理能力边界实际上也带来了几个很重要的启示：", "page_idx": 11}, {"type": "text", "text": "需要足够强的基座模型：基座模型 (DeepSeek-V3 Base) 超过了某个质量和能力阈值（671B 在14.8T 高质量Token上训练）（基座模型知识帮助突破推理上界，也有一些工作利用小模型复现Aha Moment 得益于大规模RL和高质量推理数据）；", "page_idx": 11}, {"type": "text", "text": "大规模强化学习加持：GRPO 对于强化学习训练的优化；", "page_idx": 11}, {"type": "text", "text": "规则化奖励：绕过奖励攻陷问题，但是得益于推理问题可以进行自动化标记和验证（Self-Automated Verification and Annotation)，这是与一般聊天和写作请求任务不同的；", "page_idx": 11}, {"type": "text", "text": "DeepSeek-R1 Zero的关键启示：举例 - 自动化标记和验证", "text_level": 1, "page_idx": 12}, {"type": "text", "text": "示例输入: 编写 python 代码，该代码采用数字列表，按排序顺序返回，在开始时添加 42。", "page_idx": 12}, {"type": "text", "text": "自动化验证方法：", "page_idx": 12}, {"type": "text", "text": "利用软件检查代码补全判断是否为完整代码；  \n执行Python代码检查运行情况判断是否为可运行代码；  \n调用外部模块构建额外的检测单元；  \n甚至可以更进一步，测量执行时间，使训练过程首选性能更高的解决方案；", "page_idx": 12}, {"type": "text", "text": "以上均可以作为小批量训练 (Mini-Batch) 和连续训练过程中的奖励信号", "page_idx": 12}, {"type": "text", "text": "DeepSeek-R1 Zero的关键启示：举例 - 自动化标记和验证", "text_level": 1, "page_idx": 13}, {"type": "text", "text": "示例输入: 编写 python 代码，该代码采用数字列表，按排序顺序返回，在开始时添加 42。", "page_idx": 13}, {"type": "text", "text": "基于规则进行验证，并在Mini-Batch中提供奖励信号；", "page_idx": 13}, {"type": "image", "img_path": "images/ecb275e57b26b4ae7e4c4bac09c7f9396cfa12667a6378cec27a19bd7d9e8e3f.jpg", "img_caption": [], "img_footnote": [], "page_idx": 13}, {"type": "text", "text": "DeepSeek-R1 Zero 的问题：长推理过程可读性差、语言混合，帮助性低", "page_idx": 14}, {"type": "text", "text": "Research Questions: ", "text_level": 1, "page_idx": 14}, {"type": "text", "text": "能否在Zero基础上兼顾推理性能的同时，提升模型的帮助性和安全性？例如产生Clear & Coherent CoT 并且展现出通用能力的模型 R1；能否利用一些高质量反思数据集做 Cold Start 从而加速RL的收敛或帮助提升推理表现", "page_idx": 14}, {"type": "text", "text": "DeepSeek-R1 技术 Pipeline 总览", "text_level": 1, "page_idx": 15}, {"type": "text", "text": "DeepSeek-v3-Base DeepSeek-v3-Base 拒绝采样和全领域SFT(671B) (671B)600k 推理数据↓ (Rule-based $+$ Generative 奖励)200k 通用数据  \n基于规则的奖励 Rule-Based Reward 冷启动 Cold Start （Writing \\ Role-Play etc.）正确率奖励 格式奖励 双重验证 反思数据↓ 推理为中心的 RL 全领域RL All-Scenarios RL推理任务 – 规则奖励正确率奖励 流畅性奖励 (Rule-based Reward)  \n推理为中心的大规模强化学习(GRPO)  \nLarge-Scale Reasoning-Oriented RL 数学代码推理任务 通用任务 – 偏好建模(Reward Model)↓Intermediate ModelDeepSeek-R1-Zero (推理链可读性更强) DeepSeek-R1Stage I: 推理链可读性 Stage II: 通用能力&安全性", "page_idx": 15}, {"type": "text", "text": "DeepSeek-R1 技术 Pipeline 总览", "text_level": 1, "page_idx": 16}, {"type": "text", "text": "冷启动Cold Start ", "text_level": 1, "page_idx": 16}, {"type": "text", "text": "数据准备：few-shot long cot data, 详细带反思和验证的数据集", "page_idx": 16}, {"type": "text", "text": "双重验证：由人类注释者和 R1-zero 生成的高质量链式思考（Chain-of-Thought, CoT）数据，部分样本长度达到 10,000 Token", "page_idx": 16}, {"type": "text", "text": "成效：提供一些 Human Prior \\ 显著提升了语言的语义连贯性、可读性和基本推理能力。", "page_idx": 16}, {"type": "text", "text": "推理为中心RL Reasoning-Oriented RL ", "text_level": 1, "page_idx": 16}, {"type": "text", "text": "增加了大规模的RL训练过程：和DeepSeek-R1 Zero 基本一致，主要是提升Reasoning的能力，包括coding \\ mathematics \\ logicreasoning 等带有明确解答过程的问题", "page_idx": 16}, {"type": "text", "text": "语言一致性奖励：引入 language consistency reward 衡量长推理链可读性（通过计算CoT过程中目标语言的占比）", "page_idx": 16}, {"type": "text", "text": "推理准确率奖励：结合 accuracy of reasoning tasks and reward for language consistency ", "page_idx": 16}, {"type": "text", "text": "成效：通过 GRPO ，模型在AIME 2024 等数学基准上取得了显著提升， $\\mathtt { p a s s } ( \\varpi 1 \\$ 从 $1 5 . 6 \\%$ 提高到 $7 1 . 0 \\%$ 。此外，模型能够自发延长推理链条，展现出更强的逻辑连贯性。", "page_idx": 16}, {"type": "image", "img_path": "images/7d74a727b411717caf3c0b917b2c518be7e04f13e69fdc9475299cd913ca7ad3.jpg", "img_caption": [], "img_footnote": [], "page_idx": 16}, {"type": "text", "text": "拒绝采样和全领域SFT Rejection Sampling and SFT ", "page_idx": 17}, {"type": "text", "text": "当上一个阶段的RL收敛之后，再进行SFT和之前Cold-Start 的数据不同，这部分SFT主要是负责全领域", "page_idx": 17}, {"type": "text", "text": "任务", "text_level": 1, "page_idx": 17}, {"type": "text", "text": " 600k 推理任务：(1) 基于规则的奖励 (2) 利用批判模型融合生成式奖励 200k 通用任务 (writing \\ role-playing \\ general-purpose)", "page_idx": 17}, {"type": "text", "text": "成效：使模型在推理能力不减的前提下，语言表现更为自然，适应性更为广泛。", "page_idx": 17}, {"type": "text", "text": "全领域RL RL for all Scenarios", "text_level": 1, "page_idx": 17}, {"type": "text", "text": "进一步提升除了reasoning 能力之外帮助性和安全性对于reasoning data, 可以用基于规则的奖励对于general data, 可以用奖励模型来建模人类偏好意图成效：最终版本的 R1 不仅在推理和对话能力上达到了高水平，还具备更安全的交互性能。", "page_idx": 17}, {"type": "text", "text": "", "page_idx": 17}, {"type": "text", "text": "", "page_idx": 17}, {"type": "text", "text": "拒绝采样和全领域SFT   \n600k 推理数据   \n(Rule-based+Generative 奖励)   \n200k 通用数据   \n（Writing \\ Role-Play etc.）   \n全领域RL All-Scenarios RL   \n推理任务 – 规则奖励   \n(Rule-based Reward)   \n通用任务 – 偏好建模   \n(Reward Model) ", "page_idx": 17}, {"type": "text", "text": "", "page_idx": 17}, {"type": "text", "text": "DeepSeek-R1 ", "page_idx": 17}, {"type": "text", "text": "Stage II: 通用能力&安全性", "page_idx": 17}, {"type": "text", "text": "DeepSeek-R1 Takeaways 技术亮点总结：Part I", "text_level": 1, "page_idx": 18}, {"type": "text", "text": "Pure RL to Develop Reasoning Capabilities: ", "page_idx": 18}, {"type": "text", "text": "社区的复现都涉及蒸馏和搜索，而DS-R1 Zero 跳过监督微调SFT阶段，展现出大规模强化学习的潜力，这也得益于以下几点：", "page_idx": 18}, {"type": "text", "text": "需要足够强的基座模型：基座模型(DeepSeek-V3 Base) 超过了某个质量和能力阈值（671B 在14.8T 高质量Token上训练）；", "page_idx": 18}, {"type": "text", "text": "大规模强化学习加持：GRPO 对于强化学习训练的优化；", "page_idx": 18}, {"type": "text", "text": "规则化奖励：绕过奖励攻陷问题，但是得益于推理问题可以进行自动化标记和验证（Self-Automated Verification and Annotation)，这是与一般聊天和写作请求任务不同的", "page_idx": 18}, {"type": "text", "text": "训练步数的增长，模型的thinking response length 逐渐增加 (test-time computation increasing) ", "page_idx": 18}, {"type": "text", "text": "eepSeek-R1-Zero 自主涌现学会重新评测原来的方法、反思和主动探索其他的路径", "page_idx": 18}, {"type": "text", "text": "多阶段训练下的冷启动让RL训练更加稳定，避免初期不稳定、加速收敛、提升思维链可读性", "page_idx": 18}, {"type": "text", "text": "未来后训练的重心会逐步倾向于RL，但是少量训练用于SFT可能还是必须的", "page_idx": 18}, {"type": "text", "text": "强化学习技术不只局限在基于规则的数学、算法代码等容易提供奖励的领域，它还可以创造性地把强化学习所带来的强推理能力，泛化到其他领域", "page_idx": 18}, {"type": "text", "text": "DeepSeek-R1 技术剖析：背后的教师模型 DeepSeek-V3", "text_level": 1, "page_idx": 19}, {"type": "text", "text": "基座模型(DeepSeek-V3 Base) 超过了某个质量和能力阈值（671B 在14.8T 高质量Token上训练） 提供了类似于 System I 的足够好的Prior Distribution 直觉，后期RL探索过程进一步挖掘激活  \n大规模RL起到了激活和发掘预训练阶段积累的知识和推理能力的作用  \nDeepSeek-V3 低成本（5,576,000美元）带来惊艳效果 MoE 架构 671B 激活37B \\ 使用 Multi-head Latent Attention (MLA) 架构", "page_idx": 19}, {"type": "text", "text": " 2048张 H800 计算: ${ \\sim } 5 4$ 天", "page_idx": 19}, {"type": "image", "img_path": "images/fdb1d3120ee3e49e6416f8383abfa77b5119c7f1af11893e176a1ccd23018cec.jpg", "img_caption": [], "img_footnote": [], "page_idx": 19}, {"type": "table", "img_path": "images/46219246e8a132e0ee286bcb32d2b83154ef6f2bc1399dcd7e6954b2f9daed8c.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>Training Costs</td><td>Pre-Training</td><td>ContextExtension</td><td>Post-Training</td><td>Total</td></tr><tr><td>in H800 GPU Hours in USD</td><td>2664K $5.328M</td><td>119K $0.238M</td><td>5K $0.01M</td><td>2788K $5.576M</td></tr></table></body></html>", "page_idx": 19}, {"type": "image", "img_path": "images/47215e5bc1f4b712b4d66155b7f551fbc1f412cac5b2dff18c8512e56ea0f232.jpg", "img_caption": [], "img_footnote": [], "page_idx": 19}, {"type": "text", "text": "eepSeek-R1 技术剖析：RL 加持下的 Length 泛化&推理范式涌现", "text_level": 1, "page_idx": 20}, {"type": "text", "text": "大规模RL的加持下，DeepSeek-R1 Zero 表现出在推理任务上思维链长度的自然增长和涌现", "page_idx": 20}, {"type": "text", "text": " 反思深度逐层加深，出现标记不明确的步骤、保持中间结论、验证、混合语言推理等现象  \n 模型在准确率奖励和格式奖励下自然探索到 验证、回溯、总结、反思 的行为范式 如何控制来保证最后的response 长度能够稳定上升，可能会出现反复重复验证、或者验证时间过晚的情况; （REINFORCE 系列更快；PPO训练稳定但是慢）  \n 多语言可能是因为预训练数据是多语言的，“一视同仁”被 Tokenization，不同的领域的不同语言编码是否有不同优势？", "page_idx": 20}, {"type": "image", "img_path": "images/504e1dc3dc11abc2fcef174b313265ecf8cef402e3ad4f25b9ce503b1bd4ec16.jpg", "img_caption": ["DS-R1 Zero 长度涌现现象", "社区复现结果 1"], "img_footnote": [], "page_idx": 20}, {"type": "image", "img_path": "images/d6426b1fd640fffd6c380f9ad9dc5bbdf9b7d62b79d90b71b1fc46e1134a95da.jpg", "img_caption": ["社区复现结果 2"], "img_footnote": [], "page_idx": 20}, {"type": "text", "text": "DeepSeek-R1 技术剖析：GRPO 赋能RL-Scale", "text_level": 1, "page_idx": 21}, {"type": "text", "text": "GRPO核心思想是通过构建多个模型输出的群组，并计算群组内的相对奖励来估计基线，从而避免了传统策略优化算法中需要使用与策略模型大小相同的评论模型。", "page_idx": 21}, {"type": "text", "text": "大幅度降低RL 训练的计算成本，同时还能保证模型能够有效地学习到策略。", "page_idx": 21}, {"type": "text", "text": "具体来说，在传统的 RL 训练中，评论模型需要与策略模型具有相同的大小，增加计算资源的消耗。而GRPO 算法利用群组内的相对信息来估计基线，避免了使用Critic Model的需要。", "page_idx": 21}, {"type": "text", "text": "此外，GRPO 算法还引入了一些额外的优化策略(奖励缩放和策略裁剪)，提升训练的稳定性。", "page_idx": 21}, {"type": "text", "text": "From PPO to GRPO: ", "page_idx": 21}, {"type": "text", "text": "PPO 作为 Actor-Critic 算法被广泛运用于Post-Training, 核心目标是最大化下面的目标函数其中, $\\pi _ { \\theta }$ 𝜃和 $\\pi _ { \\theta o l d }$ 分别表示当前策略模型和旧策略模型，q, o是从问题数据集和旧策略 $\\pi _ { \\theta o l d }$ 𝜃中采样的输入和输出, $A _ { t }$ 是基于广义优势估计（GAE）计算的优势值，依赖于奖励序列 $\\{ r \\geq \\mathrm { r } \\}$ 和学习的价值函数 $V _ { \\psi }$ 。因此，PPO需要同时训练策略模型和价值函数。为避免奖励模型的过度优化，标准做法是在每个词元的奖励中添加与参考模型的KL惩罚项", "page_idx": 21}, {"type": "text", "text": "", "page_idx": 21}, {"type": "equation", "text": "$$\n2 ) , o \\sim \\pi _ { \\theta _ { o l d } } ( O | q ) ] \\frac { 1 } { | o | } \\sum _ { t = 1 } ^ { | o | } \\operatorname* { m i n } \\left[ \\frac { \\pi _ { \\theta } ( o _ { t } | q , o _ { < t } ) } { \\pi _ { \\theta _ { o l d } } ( o _ { t } | q , o _ { < t } ) } A _ { t } , \\mathrm { c l i p } \\left( \\frac { \\pi _ { \\theta } ( o _ { t } | q , o _ { < t } ) } { \\pi _ { \\theta _ { o l d } } ( o _ { t } | q , o _ { < t } ) } , 1 - \\varepsilon , 1 + \\varepsilon \\right) A _ { t } \\right] .\n$$$$\n\\begin{array} { r l } { \\frac { o _ { < t } ) } { \\vert \\boldsymbol { \\cdot } , o _ { < t } \\vert } A _ { t } , \\mathrm { c l i p } ( \\frac { \\pi _ { \\theta } ( o _ { t } \\vert \\boldsymbol { q } , o _ { < t } ) } { \\pi _ { \\theta _ { o l d } } ( o _ { t } \\vert \\boldsymbol { q } , o _ { < t } ) } , 1 - \\varepsilon , 1 + \\varepsilon ) A _ { t } ) } & { { } r _ { t } = r _ { \\varphi } ( \\boldsymbol { q } , o _ { \\le t } ) - \\beta \\log \\frac { \\pi _ { \\theta } ( o _ { t } , o _ { < t } ) } { \\pi _ { r e f } ( o _ { t } , o _ { < t } ) } , } \\end{array}\n$$", "text_format": "latex", "page_idx": 21}, {"type": "equation", "text": "", "text_format": "latex", "page_idx": 21}, {"type": "text", "text": "DeepSeek-R1 技术剖析：GRPO 赋能RL-Scale", "text_level": 1, "page_idx": 22}, {"type": "text", "text": "From PPO to GRPO: ", "page_idx": 22}, {"type": "text", "text": "PPO的价值函数通常是与策略模型规模相当的独立模型，这带来了巨大的内存和计算负担。", "page_idx": 22}, {"type": "text", "text": "奖励模型通常仅对输出序列的最后一个词元分配奖励，导致逐词元价值函数的训练复杂化。", "page_idx": 22}, {"type": "text", "text": "GRPO：无需像PPO额外近似价值函数，而是利用同一问题下多个采样输出的平均奖励作为基线。具体而言，对于每个问题，GRPO从旧策略 $\\pi _ { \\theta o l d }$ 中采样一组输出，并通过最大化以下目标优化策略模型：", "page_idx": 22}, {"type": "text", "text": "通过群组相对方式计算优势值，与奖励模型的对比性质（通常基于同一问题的输出比较训练）天然契合；此外，GRPO直接将策略模型与参考模型的KL散度作为正则项加入损失函数，而非将其混入奖励计算，简化了优势值的计算。", "page_idx": 22}, {"type": "equation", "text": "$$\n\\begin{array} { l } { \\displaystyle \\mathcal { J } _ { G R P O } ( \\theta ) = \\mathbb { E } [ q \\sim P ( Q ) , \\{ \\sigma _ { i } \\} _ { i = 1 } ^ { G } \\sim \\pi _ { \\theta _ { o d } } ( O | q ) ] } \\\\ { \\displaystyle \\frac { 1 } { G } \\sum _ { i = 1 } ^ { G } \\frac { 1 } { | \\alpha _ { i } | } \\sum _ { t = 1 } ^ { | \\alpha _ { i } | } \\left\\{ \\operatorname* { m i n } \\left[ \\frac { \\pi _ { \\theta } ( o _ { i , t } | q , o _ { i , c , t } ) } { \\pi _ { \\theta _ { o d } } ( o _ { i , t } | q , o _ { i , c , t } ) } \\hat { A } _ { i , t } , \\mathrm { c l i p } \\left( \\frac { \\pi _ { \\theta } ( o _ { i , t } | q , o _ { i , c , t } ) } { \\pi _ { \\theta _ { o d } } ( o _ { i , t } | q , o _ { i , c , t } ) } , 1 - \\varepsilon , 1 + \\varepsilon \\right) \\hat { A } _ { i , t } \\right] - \\left| \\frac { \\beta \\mathbb { D } _ { K L } \\left[ \\pi _ { \\theta } | | \\pi _ { r e f } | \\right] } { \\beta \\mathbb { D } _ { K L } \\left[ \\pi _ { \\theta } | | \\pi _ { r e f } | \\right] } \\right| \\right\\} , } \\end{array}\n$$", "text_format": "latex", "page_idx": 22}, {"type": "equation", "text": "$$\n\\mathbb { D } _ { K L } \\left[ \\pi _ { \\theta } | | \\pi _ { r e f } \\right] = \\frac { \\pi _ { r e f } ( o _ { i , t } | q , o _ { i , < t } ) } { \\pi _ { \\theta } ( o _ { i , t } | q , o _ { i , < t } ) } - \\log \\frac { \\pi _ { r e f } ( o _ { i , t } | q , o _ { i , < t } ) } { \\pi _ { \\theta } ( o _ { i , t } | q , o _ { i , < t } ) } - 1 ,\n$$", "text_format": "latex", "page_idx": 22}, {"type": "image", "img_path": "images/9005a2eeee166fe51cc765b2ee500843f8c4f022411a3859590792483c2541bd.jpg", "img_caption": [], "img_footnote": [], "page_idx": 22}, {"type": "text", "text": "DeepSeek-R1 技术剖析：GRPO 赋能RL-Scale", "text_level": 1, "page_idx": 23}, {"type": "text", "text": "From PPO to GRPO: ", "page_idx": 23}, {"type": "text", "text": "基于结果监督的GRPO: 对于每个问题q，从旧策略模型 $\\pi _ { \\theta o l d }$ 𝜃采样一组输出 $\\{ o _ { 1 } , o _ { 2 } , o _ { 3 } , \\dots , o _ { G } \\}$ ，奖励模型为每个输出生成奖励 $\\{ r _ { 1 } , r _ { 2 } , r _ { 3 } , \\ldots , r _ { G } \\}$ 。随后，奖励通过减去组内均值并除以标准差进行归一化。结果监督将归一化后的奖励分配给每个输出的末尾词元，并将所有词元的优势设为该归一化奖励；", "page_idx": 23}, {"type": "text", "text": "基于过程监督的GRPO: 结果监督仅提供输出末尾的奖励，对复杂数学任务的策略指导不足", "page_idx": 23}, {"type": "text", "text": "对问题 $\\mathsf { q }$ 和采样输出 $\\{ o _ { 1 } , o _ { 2 } , o _ { 3 } , \\dots , o _ { G } \\}$ ，过程奖励模型为每个步骤生成奖励: ${ \\bf R } =$ rindex(1)ndex(2)o $\\dots , r _ { 1 } ^ { i n d e x ( K _ { 1 } ) } \\Big \\{ , \\dots , \\Big \\{ r _ { G } ^ { i n d e x ( 1 ) } , r _ { G } ^ { i n d e x ( 2 ) } , \\dots , r _ { G } ^ { i n d e x ( K _ { G } ) } \\Big \\} \\Big \\}$ 其中𝑖𝑖𝑖𝑖𝑖𝑖𝑖𝑖𝑖𝑖 $( j )$ 是第 $j$ 步的末尾词元索引， $K _ { I }$ 𝑖是第 $i$ 个输出的总步数。归一化后，优势值为后续步骤归一化奖励的累加和", "page_idx": 23}, {"type": "equation", "text": "$$\n\\begin{array} { r } { \\hat { A } _ { i , t } = \\tilde { r } _ { i } = \\frac { r _ { i } - \\mathrm { m e a n } ( \\mathbf { r } ) } { \\mathrm { s t d } ( \\mathbf { r } ) } } \\end{array}\n$$", "text_format": "latex", "page_idx": 23}, {"type": "text", "text": "基于结果监督的GRPO优势值估计", "page_idx": 23}, {"type": "text", "text": "Algorithm 1 Iterative Group Relative Policy Optimization ", "text_level": 1, "page_idx": 23}, {"type": "text", "text": "", "page_idx": 23}, {"type": "text", "text": "Input initial policymodel $\\pi _ { \\theta _ { \\mathrm { i n i t } } } .$ ; reward models $r _ { \\varphi }$ ; task prompts $\\mathcal { D }$ ;hyperparametersε,β,μ   \n1: policy modelπ ←πθinit   \n2:foriteratior $\\mathbf { \\Omega } _ { \\cdot } = 1 , \\dots , \\operatorname { I }$ do   \n3: reference model $\\pi _ { r e f }  \\pi _ { \\theta }$   \n4: forstep $\\mathbf { \\tau } = 1 , \\dots , \\mathbf { M }$ do   \n5: Sampleabatch $\\mathcal { D } _ { b }$ from $\\mathcal { D }$   \n6: Update the old policy model $\\pi _ { \\theta _ { o l d } }  \\pi _ { \\theta }$   \n7: Sample G outputs $\\{ o _ { i } \\} _ { i = 1 } ^ { G } \\sim \\pi _ { \\theta _ { o l d } } ( \\cdot \\mid q )$ for each question $q \\in \\mathcal { D } _ { b }$   \n8: Compute rewards $\\{ r _ { i } \\} _ { i = 1 } ^ { G }$ for each sampled output $o _ { i }$ by running $r _ { \\varphi }$   \n9: Compute $\\hat { A } _ { i , t }$ for the $t$ -th token of $o _ { i }$ through group relative advantage estimation.   \n10: for GRPO iteration $= 1 , \\ldots , \\mu$ do   \n11: Update the policy model $\\scriptstyle { \\pi _ { \\theta } }$ by maximizing the GRPO objective (Equation 21)   \n12: Update $r _ { \\varphi }$ through continuous training using a replay mechanism. ", "page_idx": 23}, {"type": "equation", "text": "$$\n\\hat { A } _ { i , t } = \\sum _ { \\mathrm { i n d e x } ( j ) \\geq t } \\tilde { r } _ { i } ^ { \\mathrm { i n d e x } ( j ) } ,\n$$", "text_format": "latex", "page_idx": 23}, {"type": "text", "text": "基于过程监督的GRPO优势值估计", "page_idx": 23}, {"type": "text", "text": "Output π0 ", "text_level": 1, "page_idx": 23}, {"type": "text", "text": "DeepSeek-R1 Takeaways 总结 Part II", "text_level": 1, "page_idx": 24}, {"type": "text", "text": "DS-R1 Zero 跳过监督微调SFT阶段，展现出大规模强化学习的潜力。这种自主学习的方式，不仅节省了大量的标注成本，而且让模型更自由的探索解决问题的路径，而不是被预先设定的模式所束缚。这也使得模型最终具备了更加强大的泛化能力和适应能力。", "page_idx": 24}, {"type": "text", "text": "为了充分释放GRPO 的潜力并确保训练稳定性，DeepSeek R1 的训练中采用了四阶段的交替迭代流程：“监督微调（SFT） $$ 强化学习（RL） $$ 再次 $\\mathrm { S F T } $ 再次 RL”，有效解决了传统强化学习模型在冷启动、收敛效率和多场景适应性方面的瓶颈。", "page_idx": 24}, {"type": "text", "text": "强大的自验证和长链推理能力：并非预先设定好的，而是在RL训练中自主涌现出来的自验证是指模型在生成最终答案之前，会先主动地验证自己的中间推理步骤是否正确。这就像一个学生在做题时，会反复检查自己的解题过程，以确保答案的准确性。反思是指模型会回溯检查自己之前的推理过程，并根据检查的结果进行修正，相当于一个学生在复习时，会反思自己之前的错误，以便下次不再犯同样的错误。而长链推理能力则让模型能够处理更复杂、更需要多步骤思考的问题。这种能力对于解决一些需要跨越多个逻辑步骤才能找到答案的问题至关重要，例如复杂的数学题或逻辑谜题。", "page_idx": 24}, {"type": "text", "text": "", "page_idx": 24}, {"type": "text", "text": "冷启动让RL训练更加稳定：", "text_level": 1, "page_idx": 24}, {"type": "text", "text": "避免 RL 训练初期的不稳定，使得模型能够更快地进入稳定的训练状态；  \n有效地加速RL 训练的收敛，缩短训练时间；  \n提高模型输出的可读性，减少不同语言混合使用的情况。", "page_idx": 24}, {"type": "text", "text": "DeepSeek-R1 Takeaways 总结 Part II", "text_level": 1, "page_idx": 25}, {"type": "text", "text": "推理为中心的RL训练：", "text_level": 1, "page_idx": 25}, {"type": "text", "text": "语言一致性奖励，以解决模型在多语言环境中进行推理时，出现语言混合的问题。", "page_idx": 25}, {"type": "text", "text": " 对推理链的质量进行细致的评估，并通过奖励机制引导模型生成更加合理、准确的推理过程。", "page_idx": 25}, {"type": "text", "text": "多目标优化： 兼顾推理性能、 帮助性和安全性；", "page_idx": 25}, {"type": "text", "text": "蒸馏的潜力：蒸馏可以帮助将更大模型通过RL发现的高阶推理范式蒸馏到小模型中，这比用小模型直接使用大规模RL发现的推理范式要更加有效；", "page_idx": 25}, {"type": "text", "text": "基于群组的相对策略优化(GRPO) :通过构建多个模型输出的群组，并计算群组内的相对奖励来估计基线，从而避免了传统策略优化算法中需要使用与策略模型大小相同的评论模型", "page_idx": 25}, {"type": "text", "text": "降低 RL 训练的计算成本\\保证模型能够有效地学习到策略", "page_idx": 25}, {"type": "text", "text": "奖励机制的设计： 兼顾推理能力和语言一致性", "text_level": 1, "page_idx": 25}, {"type": "text", "text": " 准确率奖励和格式奖励，从而保证模型不仅能够正确地解决问题，还能够以规范、易读的方式输出答案  \n 格式奖励：用于强制模型将推理过程放置在特定的标签内，用<think> 和 </think> 标签来包裹推理过程，使用<answer> 和 </answer> 标签来包裹最终答案。  \n 语言一致性奖励：惩罚在推理过程中使用了多种语言的输出，鼓励模型尽可能地使用一种目标语言进行推理，从而保证模型输出的语言风格一致性", "page_idx": 25}, {"type": "text", "text": "DeepSeek-R1 社会和经济效益", "text_level": 1, "page_idx": 26}, {"type": "text", "text": "低成本高质量语言模型边界的探索，扩展的具体方法和侧重点改变：最初是模型规模，然后是数据集规模，现在是推理时的计算资源和合成数据；", "page_idx": 26}, {"type": "text", "text": "垂直领域和横向拓展：采用\"API+本地化知识库\"或\"提示工程+检索增强\"的混合方案，通过Prompt Engineering和RAG等技术实现业务场景的快速适配与轻量定制，同时建立完善的运维合规体系，确保数据处理全流程的安全性与合法性。", "page_idx": 26}, {"type": "text", "text": "资本市场的剧烈波动是AI技术快速迭代引发的短期现象，表现为研发投入和数据中心建设成本激增，这在近期美股科技股的震荡中得以集中体现；而从长期来看，行业将陷入算力军备竞赛的循环，每一轮技术突破和应用场景扩展都将催生新的算力需求与资源投入，持续重塑行业竞争格局。", "page_idx": 26}, {"type": "text", "text": "资源优化：随着模型使用方案的平民化，中小企业和个人开发者得以将有限资源聚焦于场景创新与技术优化，无需在基础能力建设或算力消耗上投入过多成本。", "page_idx": 26}, {"type": "text", "text": "市场激活：这种高性价比、低门槛的大模型服务模式，将吸引更多初创团队涌入赛道，催生多元化的应用场景和技术路径，推动行业生态的繁荣发展。", "page_idx": 26}, {"type": "text", "text": "高效创新：在有限算力资源支持下，算法创新模式，突破了算力的“卡脖子”限制", "page_idx": 26}, {"type": "text", "text": "技术对比讨论：Kimi K1.5 Moonshot", "text_level": 1, "page_idx": 27}, {"type": "text", "text": "K1.5 专注于用长文本CoT 解决推理时Scaling问题", "text_level": 1, "page_idx": 27}, {"type": "text", "text": " 利用 RL 探索：Kimi k1.5 的核心思想是利用强化学习，让模型通过试错（探索）来学习解决问题的能力，而不是仅仅依赖于静态数据集。", "page_idx": 27}, {"type": "text", "text": "长文本CoT 的 RL：将 RL 应用于长文本CoT推理过程，使模型能够进行更深入、更复杂的推理。  \n 隐式规划：通过增加上下文长度，让模型在生成CoT 的过程中进行隐式的规划、反思和修正，无需显式的搜索树或价值函数。长文本能力是关键：核心洞察是长文本能力是强化学习训练LLM的关键，而不是更复杂的训练技巧。  \n 长文本到短文本：通过长文本CoT 模型来指导短文本CoT 模型的训练，从而在有限的计算资源下获得更好的性能。", "page_idx": 27}, {"type": "image", "img_path": "images/160de3c26cb9f3c8a4b79b0855764783bc4f222f90ae5421f05c17c7f1e6e35d.jpg", "img_caption": ["Kimi K1.5 Main Result "], "img_footnote": [], "page_idx": 27}, {"type": "image", "img_path": "images/c572eeb6c8e7243085734575ac7100f4287aceabc519b787454c512fdf0a67b3.jpg", "img_caption": ["Kimi K1.5 Long2Short Result "], "img_footnote": [], "page_idx": 27}, {"type": "text", "text": "技术对比讨论：Kimi K1.5 Moonshot", "text_level": 1, "page_idx": 28}, {"type": "text", "text": "四个阶段Pretraining -- SFT -- Long-CoT SFT – RL", "text_level": 1, "page_idx": 28}, {"type": "text", "text": "RL Prompt Set Curation RL 问题的准备", "page_idx": 28}, {"type": "text", "text": " Diverse Coverage: 涵盖STEM \\ coding \\ general reasoning 的数据  \n Balanced Difficulty: 涵盖不同的complexity 难度  \n Accurate Evaluability: 能够被Verifiers 准确评价，从而防止泛化出 一些reward hacking 和 superficialpatterns 的行为", "page_idx": 28}, {"type": "text", "text": "Long-CoT SFT ", "text_level": 1, "page_idx": 28}, {"type": "image", "img_path": "images/1aa5d4a1c647cb0bfd7de2f2ffdbf0e05eb9b0c0232a7a086c207ef8c4f76472.jpg", "img_caption": ["RL Infrastructure System "], "img_footnote": [], "page_idx": 28}, {"type": "text", "text": "用Prompt Engineering 造了一个高质量LongCoT warmup dataset 包含了准确的verified reasoning paths for both image and text inputs 涵盖了planning \\ evaluation \\ reflection \\ exploration 多种范式 ", "page_idx": 28}, {"type": "text", "text": "技术对比讨论：Kimi K1.5 Moonshot", "text_level": 1, "page_idx": 29}, {"type": "text", "text": "强化学习：从 In-Context RL 的角度出发，直接训练模型approximate Planning的过程（例如将Search中, state 和 value 等信息都视为Language Tokens)", "page_idx": 29}, {"type": "text", "text": "策略优化：建模成Contextual Bandit, 用 REINFORCE 变种进行优化长度惩罚：引入长度惩罚机制，防止模型生成过长的推理过程，提高计算效率。 Overthinking的行为：可能会导致更好的表现，但是会带来training 和 inference过程中极大的损耗", "page_idx": 29}, {"type": "text", "text": "采样策略", "text_level": 1, "page_idx": 29}, {"type": "text", "text": "课程学习（Curriculum Learning）： 根据问题的难度，先让模型学习容易的例子，再逐渐引入更难的例子，这种循序渐进的方式有助于模型更好地掌握知识。优先采样（Prioritized Sampling）： 根据问题的难度或模型对问题的掌握程度来调整采样概率。模型更倾向于采样那些困难的、或者模型不擅长的问题，提高训练效率。", "page_idx": 29}, {"type": "equation", "text": "$$\n\\mathrm { s w a r d ( i ) } = \\left\\{ \\begin{array} { r l l } { { \\lambda } } & { { \\mathrm { I f } \\ r ( x , y _ { i } , y ^ { * } ) = 1 } } & { { } } \\\\ { { \\operatorname* { m i n } ( 0 , \\lambda ) } } & { { \\mathrm { I f } \\ r ( x , y _ { i } , y ^ { * } ) = 0 } } & { { \\mathrm { , } } } \\end{array} \\right. \\ \\mathrm { w h e r e } \\ \\lambda = 0 . 5 - \\frac { \\operatorname { l e n } ( i ) - \\operatorname { l e n } ( i ) } { \\operatorname { m a x \\_ l e n \\_ s i o n \\_ w i d t h } \\operatorname { I e r m } ( i ) } .\n$$", "text_format": "latex", "page_idx": 29}, {"type": "text", "text": "长度惩罚奖励", "text_level": 1, "page_idx": 29}, {"type": "equation", "text": "$$\nL ( \\theta ) = \\mathbb { E } _ { ( x , y ^ { * } ) \\sim \\mathcal { D } } \\left[ \\mathbb { E } _ { ( y , z ) \\sim \\pi _ { \\theta _ { i } } } \\left[ \\left( r ( x , y , y ^ { * } ) - \\tau \\log Z - \\tau \\log \\frac { \\pi _ { \\theta } ( y , z | x ) } { \\pi _ { \\theta _ { i } } ( y , z | x ) } \\right) \\right] \\right] .\n$$", "text_format": "latex", "page_idx": 29}, {"type": "text", "text": "策略优化损失函数", "text_level": 1, "page_idx": 29}, {"type": "text", "text": "技术对比讨论：Kimi K1.5 Moonshot", "text_level": 1, "page_idx": 30}, {"type": "text", "text": "Vision Data 构造", "text_level": 1, "page_idx": 30}, {"type": "text", "text": "真实世界数据 包括各个年级的科学问题，这些问题需要图形理解和推理能力；还包括需要视觉感知和推理能力的位置猜测任务；以及涉及复杂图表理解的数据分析任务等。这些数据集提升了模型在真实世界场景中的视觉推理能力。", "page_idx": 30}, {"type": "text", "text": "合成视觉推理数据 是人工生成的，包括程序化创建的图像和场景，旨在提高特定的视觉推理技能，例如理解空间关系、几何模式和物体交互。这些合成数据集提供了可控环境，用于测试模型的视觉推理能力，并且可以无限生成训练样本。", "page_idx": 30}, {"type": "text", "text": "文本渲染数据 是通过将文本内容转换为视觉格式创建的，使模型能够在不同模态下保持一致的文本处理能力。通过将文本文档、代码片段和结构化数据转换为图像，确保模型无论接收的是纯文本输入还是截图或照片中的文本，都能提供一致的响应。这也有助于增强模型在处理文本密集型图像（如截图、表格、公式等）时的能力。", "page_idx": 30}, {"type": "text", "text": "Long2Short 方法", "text_level": 1, "page_idx": 30}, {"type": "text", "text": "模型融合：将长文本 CoT 模型和短文本 CoT 模型的权重进行平均，得到一个新的模型。最短拒绝采样；Short Rejection Sampling：从多个采样结果中选择最短且正确的答案 然后做SFTDPO（Direct Preference Optimization）：使用长文本 CoT 模型生成的答案作为偏好数据来训练Short CoT 模型。Long2short RL：在标准 RL 训练后，使用长度惩罚对模型进行微调，进一步提高短文本 CoT 模型的效率。", "page_idx": 30}, {"type": "text", "text": "技术对比讨论：Kimi K1.5 vs. DeepSeek-R1 Comparison", "text_level": 1, "page_idx": 31}, {"type": "text", "text": " 二者都关注RL的方法带来的提升，MCTS 和 PRM 没有被使用(Reward Hacking 的考虑)", "page_idx": 31}, {"type": "text", "text": "  MCTS 是一种Structure, $\\mathbf { A } ^ { * }$ 也是 Structure, 人为加入Inductive Bias 强求LLM按照结构化先验进行思考可能会限制模型的能力；  \n PRM 容易被Reward Hacking, 且 绝对值 Value 很难准确", "page_idx": 31}, {"type": "text", "text": "Kimi K1.5 更多是从 In-Context RL 的角度出发，直接训练模型approximate Planning的过程（例如将Search中, state 和 value 等信息都视为Language Tokens)", "page_idx": 31}, {"type": "text", "text": "DS-R1 是从纯RL入手，利用GPRO $+$ Rule-Based Reward 激活模型能力", "page_idx": 31}, {"type": "text", "text": "核心观念：不管模型中间做错了什么，只要不是重复的，那么最后模型做对了，我们就认为这是一个好的探索，值得鼓励。反之，如果模型一顿探索，最后做错了，那么再努力也是错，要惩罚。", "page_idx": 31}, {"type": "image", "img_path": "images/92d2de5cf37268e5d50a28da767a714d45249e5d97b67b798a74ace2d90a240b.jpg", "img_caption": ["Kimi K1.5 Main Result "], "img_footnote": [], "page_idx": 31}, {"type": "image", "img_path": "images/e60d8552ad4fbeebc2a92ecf93dd6c8111b2e2f2dcf703ead5e0f5f33efb947a.jpg", "img_caption": ["DS-R1 Main Result "], "img_footnote": [], "page_idx": 31}, {"type": "text", "text": "技术对比讨论：<PERSON><PERSON> K1.5 vs. DeepSeek-R1 Comparison Takeaways", "text_level": 1, "page_idx": 32}, {"type": "text", "text": "GRPO ：利用同一问题下多个采样输出的平均奖励作为基线，从而无需额外近似价值函数。这种机制通过群组相对方式计算优势值，与奖励模型基于同一问题的输出比较训练的特性天然契合。此外，GRPO直接将策略模型与参考模型的KL散度作为正则项加入损失函数，而非将其混入奖励计算，简化了优势值的计算过程。这使得GRPO在大规模强化学习任务中，特别是在处理复杂的推理任务时，能够更有效地优化策略模型，同时保持较高的计算效率。", "page_idx": 32}, {"type": "text", "text": "Kimi K1.5 采用的变种Mirror Descent可以在保证学习稳定性的同时，促进模型对复杂推理任务的理解深度，如逐层加深反思、验证、回溯等行为范式的形成。它允许模型自然地探索到验证、回溯、总结、反思的行为模式，这些对于提高模型在推理任务中的表现至关重要。", "page_idx": 32}, {"type": "text", "text": "后训练Pipeline对于提升模型推理能力的重要性不可忽视。", "page_idx": 32}, {"type": "text", "text": "随着测试阶段算力和训练阶段探索算力的增加，根据后训练Scaling  Law，模型的表现将持续得到改善。", "page_idx": 32}, {"type": "text", "text": "理想的数据构建应当覆盖广泛的类别，并且难度分级明确，这有利于实现类似课程学习的效果，逐步提高模型的能力。", "page_idx": 32}, {"type": "text", "text": "在奖励建模时，必须确保基于奖励模型的奖励机制不会被轻易攻陷。平衡推理长度与推理正确率之间的关系。例如，针对一个序列中的下一个动作，若存在一个是错误答案而另一个是正确答案的情况，传统的方法会倾向于提升选择正确答案的概率，同时降低选择错误答案的概率。然而，从推理长度的角度来看，有时选择看似错误的答案可能会引导模型进入自我修正的过程，这种自我修正机制以及更长的推理路径同样对提升模型的整体推理能力至关重要。", "page_idx": 32}, {"type": "text", "text": "技术对比讨论：强推理路径 – Pure RL vs STaR-based ", "text_level": 1, "page_idx": 33}, {"type": "text", "text": "回顾：STaR", "page_idx": 33}, {"type": "text", "text": "推理：起始数据集仅有 [Question, Answer] ，首先利用一些带有推理过程的 Few-Shot Examples 来Prompt 模型对于数据集中的问题生成对应的推理过程和答案。", "page_idx": 33}, {"type": "text", "text": "过滤：如果生成的答案正确，则将推理过程加入到原有的数据集中；如果生成的答案错误，则尝试在给出正确答案的前提下再次生成推理过程。将最终生成正确答案的推理收集，构建一个构建一个微调数据集 [Question, Rationale, Answer ] 进行微调。", "page_idx": 33}, {"type": "text", "text": "迭代：重复这一过程，且每次获得一个新的数据集，都从原始的模型开始进行 Fine-tune 从而防止过", "page_idx": 33}, {"type": "text", "text": "拟合。", "page_idx": 33}, {"type": "image", "img_path": "images/f92352b788e78f95c7f690757eed1d420a0b54967641311c748a5900d5bc543f.jpg", "img_caption": [], "img_footnote": [], "page_idx": 33}, {"type": "text", "text": "Q:Whatcanbeused   \nto carry a small dog?   \nAnswer Choices:   \n(a) swimming pool   \n(b)basket   \n(c) dog show   \n(d)backyard   \n(e)own home   \nA:The answer must be   \nsomething that can be   \nused to carry a small   \ndog.Baskets are   \ndesigned to hold things. Therefore,the answer   \nis basket (b). ", "page_idx": 33}, {"type": "text", "text": "技术对比讨论：强推理路径 – Pure RL vs STaR-based ", "text_level": 1, "page_idx": 34}, {"type": "text", "text": "回顾：STaR 与RL的联系", "page_idx": 34}, {"type": "text", "text": " 模型首先采样潜在的推理路径（rationale）的过程类似于RL 中通过策略选择动作（action），基于环境状态选择一个可能的策略路径。  \n STaR 中，通过计算目标函数，模型对整个数据集的预测结果进行评估，并且只根据预测正确的样本更新模型。  \n STaR 在同一批数据上进行多次梯度更新，这类似于某些策略梯度算法中的策略，即通过多次调整  \n同一批数据来稳定学习过程。", "page_idx": 34}, {"type": "text", "text": "STaR can be seen as an approximation to an RL-style policy gradient objective. To see this, note that $M$ can be viewed as a discrete latent variable model $\\begin{array} { r } { p _ { \\hat { M } } ( y \\mid x ) = \\bar { \\sum _ { r } } p ( r \\mid x ) p ( y \\mid x , r ) } \\end{array}$ ;in other words, $M$ first samples a latent rationale $r$ before predicting $y$ . Now, given the indicator reward function $\\mathbb { 1 } ( \\hat { y } = y )$ ,the total expected reward across the dataset is ", "page_idx": 34}, {"type": "equation", "text": "$$\nJ ( M , X , Y ) = \\sum _ { i } \\mathbb { E } _ { \\hat { r } _ { i } , \\hat { y } _ { i } \\sim p _ { M } ( \\cdot | x _ { i } ) } \\mathbb { 1 } ( \\hat { y } _ { i } = y _ { i } ) ,\n$$", "text_format": "latex", "page_idx": 34}, {"type": "equation", "text": "$$\n\\nabla J ( M , X , Y ) = \\sum _ { i } \\mathbb { E } _ { \\hat { r } _ { i } , \\hat { y } _ { i } \\sim p _ { M } ( \\cdot \\vert x _ { i } ) } \\left[ \\mathbb { 1 } \\left( \\hat { y } _ { i } = y _ { i } \\right) \\cdot \\nabla \\log p _ { M } ( \\hat { y } _ { i } , \\hat { r } _ { i } \\mid x _ { i } ) \\right] ,\n$$", "text_format": "latex", "page_idx": 34}, {"type": "text", "text": "where the gradient is obtained via the standard log-derivative trick for policy gradients.Note that the indicator function discards the gradient for all sampled rationales that do not lead to the correct answer $y _ { i }$ : this is the filtering process in STaR (Line 5). Thus, STaR approximates $J$ by(1) greedily decoding samples of $( \\hat { r } _ { i } , \\hat { y } _ { i } )$ to reduce variance of this estimate (at the cost of potentially biased exploration of rationales),and (2) taking multiple gradient steps on the same batch of data (similar to some policy gradient algorithms $\\pm [ 2 5 ] .$ ).These approximations make STaR a simple and broadly applicable method that can be implemented with standard LLM training machinery; future work should more closely investigate the link between STaR and the RL objective above. ", "page_idx": 34}, {"type": "text", "text": "Takeaways: ", "text_level": 1, "page_idx": 35}, {"type": "text", "text": "STaR 的核心思路是将思考过程建模到语言模型的Next Token Prediction 中，通过反复自我迭代和监督微调", "page_idx": 35}, {"type": "text", "text": " 基于STaR 可以进一步将这种思路扩展到思考过程是搜索过程的特例，比如rStar-Math, SoS 都可以用类似的思路来理解。  \n 本质上，STaR一类的方法是希望模型能够学习到MetaCoT, 即问题到答案映射过程背后的深入规律  \n 但是对于问题的结构要求性高，对于复杂数学推理任务可能难以自我迭代", "page_idx": 35}, {"type": "text", "text": "难以融入 Rule-Based Reward for RL训练 ", "page_idx": 35}, {"type": "text", "text": "PureRL加持下，业界的技术实践更多Focus on 直接利用RL激活基座模型的推理潜力，通过构建rule-based reward, 额外加上RL Data的设计，激活模型的内部本身的推理能力", "page_idx": 35}, {"type": "text", "text": "Reward Model 的一些尝试如PRM，会遇到reward hacking, value 不准，难以泛化等问题", "page_idx": 35}, {"type": "text", "text": "技术对比讨论：蒸馏 vs 强化学习", "text_level": 1, "page_idx": 36}, {"type": "text", "text": "大型模型虽然性能强大，但是也存在着一些局限性，例如计算资源消耗过高，部署和使用门槛较高等。  \n模型蒸馏：将一位经验丰富的老师的知识传递给一个年轻的学生，让其在较短的时间内掌握复杂的技能。 DeepSeek 利用蒸馏R1的手段获得了一系列小模型，表现非常突出。这很大程度得益于R1模型足够强大，发现了很多高阶推理范式，而这些高阶推理范式是小模型直接利用大规模强化学习难以发现的（可以认为是由于预训练知识不足），因此这些蒸馏得到的小模型表现比较突出，甚至超过了基于大规模RL的方法。", "page_idx": 36}, {"type": "table", "img_path": "images/00e2c2560a14f3d28552e03971eb894a4b08ba412478ce64d2c305df7dd31100.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td rowspan=\"2\">Model</td><td colspan=\"2\">AIME 2024</td><td rowspan=\"2\">MATH-500</td><td rowspan=\"2\">DGPQAd pass@1</td><td rowspan=\"2\">LiveCode pass@1</td><td rowspan=\"2\">CodeForces</td></tr><tr><td>pass@1</td><td>cons@64</td></tr><tr><td></td><td>9.3</td><td>13.4</td><td>pass@1 74.6</td><td>49.9</td><td>32.9</td><td>rating 759</td></tr><tr><td>GPT-4o-0513 Claude-3.5-Sonnet-1022</td><td>16.0</td><td>26.7</td><td>78.3</td><td>65.0</td><td>38.9</td><td>717</td></tr><tr><td>OpenAI-o1-mini</td><td>63.6</td><td>80.0</td><td>90.0</td><td>60.0</td><td>53.8</td><td>1820</td></tr><tr><td>QwQ-32B-Preview</td><td>50.0</td><td>60.0</td><td>90.6</td><td>54.5</td><td>41.9</td><td>1316</td></tr><tr><td>DeepSeek-R1-Distill-Qwen-1.5B</td><td>28.9</td><td>52.7</td><td>83.9</td><td>33.8</td><td>16.9</td><td>954</td></tr><tr><td>DeepSeek-R1-Distill-Qwen-7B</td><td>55.5</td><td>83.3</td><td>92.8</td><td>49.1</td><td>37.6</td><td>1189</td></tr><tr><td>DeepSeek-R1-Distill-Qwen-14B</td><td>69.7</td><td>80.0</td><td>93.9</td><td>59.1</td><td>53.1</td><td>1481</td></tr><tr><td>DeepSeek-R1-Distill-Qwen-32B</td><td>72.6</td><td>83.3</td><td>94.3</td><td>62.1</td><td>57.2</td><td>1691</td></tr><tr><td>DeepSeek-R1-Distill-Llama-8B</td><td>50.4</td><td>80.0</td><td>89.1</td><td>49.0</td><td>39.6</td><td>1205</td></tr><tr><td>DeepSeek-R1-Distill-Llama-70B</td><td>70.0</td><td>86.7</td><td>94.5</td><td>65.2</td><td>57.5</td><td>1633</td></tr></table></body></html>", "page_idx": 36}, {"type": "text", "text": "Table 5| Comparison of DeepSeek-R1 distilled models and other comparable models on reasoning-relatedbenchmarks. ", "page_idx": 36}, {"type": "text", "text": "技术对比讨论：蒸馏 vs 强化学习", "text_level": 1, "page_idx": 37}, {"type": "text", "text": "在提升模型强推理能力的努力上，蒸馏和强化学习被社区广泛探索", "page_idx": 37}, {"type": "text", "text": " 直接利用SFT蒸馏可以学习到数据中的推理范式，虽然在推理分数上的表现有所提升，但是更多是去拟合数据中的Pattern, 很难学习到数据背后的数学规律和MetaCoT", "page_idx": 37}, {"type": "text", "text": "强化学习则是通过试错和尝试，鼓励模型在最大化奖励过程中学习到推理背后的规律，获得的泛化性和推理表现上界更高", "page_idx": 37}, {"type": "text", "text": "SFT 主要负责记忆而很难进行OOD泛化，基于ORM的RL泛化能力较好 [1]", "page_idx": 37}, {"type": "text", "text": "SFT规范模型输出格式， 使得后续的RL可以获得更高的收益", "page_idx": 37}, {"type": "text", "text": "随着强推理能力复现的兴起，社区也有很多工作比较 LongCoT 长文本思维链的蒸馏效果", "page_idx": 37}, {"type": "text", "text": "Scaling up verifiable reward是long cot的核心。  \n小模型（例如wen-math-7b）不容易recentivize long cot的behavior（e.g., aha moment）在MATH 场景下。wait,recheck, alternatively这些词在rl训练中没有明显增加", "page_idx": 37}, {"type": "text", "text": "技术对比讨论：蒸馏 vs 强化学习", "text_level": 1, "page_idx": 38}, {"type": "text", "text": "Open Questions: ", "text_level": 1, "page_idx": 38}, {"type": "text", "text": "Long-COT 指令数据扩展是否有助于慢思考推理能力？", "page_idx": 38}, {"type": "text", "text": "哪种 Long-COT 数据构造方式具有最佳样本效率？", "page_idx": 38}, {"type": "text", "text": "Long-COT 及其扩展是否有助于多模态任务？", "page_idx": 38}, {"type": "text", "text": "Takeaways from RedStar [1]: ", "text_level": 1, "page_idx": 38}, {"type": "text", "text": "Long-COT 在有限数据下增强推理能力：小规模数据集（如 1.3k 个问题）可以显著提升推理性能，尤其是在数学任务中，展现了 Long-COT 调优即便在数据量较少的情况下依然具有强大的推理能力。", "page_idx": 38}, {"type": "text", "text": "更大规模和专业化模型提升性能：更大规模的模型（如 14B、32B）以及经过专业预训练的模型（如数学预训练和上下文长度扩展）在 Long-COT 训练中表现更佳，优于较小规模的模型（如 7B）在保持正确推理路径和处理复杂任务的能力。", "page_idx": 38}, {"type": "text", "text": "任务与语言之间的正迁移：Long-COT 训练不仅能提升数学任务的性能，还能对其他领域和语言产生正向影响，展现了其广泛的适用性。此外，该方法具有良好的泛化性和鲁棒性，在通用基础任务和对齐评估中取得了相当或更优的表现。", "page_idx": 38}, {"type": "text", "text": "强化学习的规模化提升了效率：离线强化学习算法（DPO）和在线强化学习算法（PPO）均能有效增强模型性能。", "page_idx": 38}, {"type": "text", "text": "Long-COT 强化多模态模型：将 Long-COT 方法应用于多模态大语言模型（MLLMs）可以显著提升其性能，说明慢思考（slow-thinking）技术在多模态任务中的有效性。", "page_idx": 38}, {"type": "text", "text": "技术对比讨论：蒸馏 vs 强化学习 Discussion", "text_level": 1, "page_idx": 39}, {"type": "text", "text": "Kimi K1.5 中 Long2Short 方法指的是将长文本 CoT 模型的知识迁移到短文本 CoT 模型，本质上是一种「蒸馏」，不过目标和策略更多样，不仅要性能，还要 token 效率；更多地关注对教师模型推理策略的学习，而不仅是输出。", "page_idx": 39}, {"type": "text", "text": "S1 模型通过少成本获得超过o1-preview的表现：", "page_idx": 39}, {"type": "text", "text": "高质量推理数据构建：s1K数据集精心挑选了1000个涵盖数学竞赛、博士级科学问题及奥林匹克竞赛题目等，这些问题经过难度、多样性和质量的严格筛选，并包含详细的推理轨迹与答案。 类似课程学习的效果。", "page_idx": 39}, {"type": "text", "text": "采样策略优化：预算强制法有效地控制了模型在测试阶段的计算资源消耗。", "page_idx": 39}, {"type": "text", "text": "当模型生成的思考标记超过预设限制时，插入“end-of-thinking  token”来终止思考过程并促使模型转向答案生成阶段若需要增加计算投入，则会暂时阻止end-of-thinking  token的出现，并鼓励更深入的探索。", "page_idx": 39}, {"type": "text", "text": "DeepSeek 利用蒸馏R1的手段获得了一系列小模型，表现非常突出。这很大程度得益于R1模型足够强大，发现了很多高阶推理范式，而这些高阶推理范式是小模型直接利用大规模强化学习难以发现的（可以认为是由于预训练知识不足），因此这些蒸馏得到的小模型表现比较突出，甚至超过了基于RL的方法。", "page_idx": 39}, {"type": "text", "text": "相对依赖于强大的教师模型", "page_idx": 39}, {"type": "text", "text": "蒸馏过程通常针对特定任务或一组任务（例如代码和数学问题）优化，这可能导致生成的小模型在面对新任务或环境（例如通用任务）时适应性和泛化能力不足。", "page_idx": 39}, {"type": "text", "text": "利用 MCTS，将答案拆分成分句或Token为单位的节点，而后对于解空间进行搜索", "page_idx": 40}, {"type": "text", "text": "通过 MCTS 可能会有以下的问题：", "page_idx": 40}, {"type": "text", "text": " Token Generation Space 更大，而不是像象棋一样, Search Space 是 relatively well-defined，容易陷入局部最优 Value Model 直接影响了搜索方向，而训练一个好的Value Model 比较困难", "page_idx": 40}, {"type": "text", "text": "个相对成功的典范是 rStar-Math [1], 通过小模型达到OpenAI o1数学任务相当的水平一个作为策略模型Policy Model, 另一个模型训练成为基于偏好的过程奖励模型（PPM），二者配合进行MCTS 产生分步验证的高质量推理数据，四轮自我迭代提升，不断更新数据而后微调模型虽然即便经过MCTS模拟后，Q值依然无法做到对每个推理步骤进行精准评分，但是它们能够有效识别出哪些步骤是正确的（正向步骤），哪些步骤是无关或错误的（负向步骤）,可以用 ranking loss 训练偏序", "page_idx": 40}, {"type": "text", "text": "", "page_idx": 40}, {"type": "text", "text": "MCTS-driven deep thinking 白 question Q-value SLM PPM filtering Apply Verifiers Step 1 Step2 (PPM/python) finalstep full solutions Onestep (b) Construction of per-step preference pairs based on Q-values Answer step 福 (correct) Terminal-guided Terminal-guided SLM-r2PPM-augmentedSLM-r3 PPM-augmentedSLM-r4 MCTS SLM-r1 MCTS PPM-r2 MCTS PPM-r3 MCTS PPM-r4 Answerstep (wrong) Round 1 Round 2 中 Round 3 Round4 8 (a) step-by-step verified reasoning trajectory (c) 4 rounds of self-evolution ", "page_idx": 40}, {"type": "text", "text": "关键的Takeaways: ", "text_level": 1, "page_idx": 41}, {"type": "text", "text": "相比于利用MCTS造数据，直接将MCTS 应用于模型的训练可能会限制模型的思考过程？", "page_idx": 41}, {"type": "text", "text": "MCTS 是一种 Structure, $\\mathbf { A } ^ { * }$ 也是 Structure, 人为加入Inductive Bias 强求LLM按照人为的结构化先验进行思考可能会限制模型的能力；", "page_idx": 41}, {"type": "text", "text": "不通过额外的Structure, 模型自身是否可以学会思考：", "page_idx": 41}, {"type": "text", "text": "Algorithm Distillation: 将RL的 Training History 序列直接建模到语言模型中，学习到Data-Efficient RL 算法Stream of Search: 将搜索的过程转化为自然语言序列预训练模型，基于这个模型做 policy improvement methods(Advantage-Induced Policy Alignment) 和STaR，解决了heuristic solvers没有解决的问题", "page_idx": 41}, {"type": "text", "text": "", "page_idx": 41}, {"type": "text", "text": "[1] OpenAI “Don‘t teach. Incentivize.” https://www.youtube.com/watch?v=kYWUEV_e2ss [2] In-context Reinforcement Learning with Algorithm Distillation https://arxiv.org/abs/2210.14215 [3] Stream of Search (SoS): Learning to Search in Language https://arxiv.org/abs/2404.03683 [4] https://blog.ml.cmu.edu/2025/01/08/optimizing-llm-test-time-compute-involves-solving-a-meta-rl-problem ", "page_idx": 41}, {"type": "image", "img_path": "images/8605c7fd86abecd30eb5000f45be6d768f66e8de481c79c2491e993b9e8ab3e9.jpg", "img_caption": ["Themore structure imposed by humans,the less scalable the method is "], "img_footnote": [], "page_idx": 41}, {"type": "text", "text": "DS-R1 和 Kimi K1.5 都没有进行明确的MCTS和PRM尝试", "page_idx": 42}, {"type": "text", "text": "PRM 的一些挑战：", "page_idx": 42}, {"type": "text", "text": " 决定当下的某一步是否正确是一个很难的task, 自动化标注难以产生很好的结果，但是用人工标注又难以scaling up  \n Model-Based PRM 可能会引入 reward hacking, 重新训练会让训练变得更加复杂  \n PRM还是更适合于rerank top-N responses and assist guided search, 在大规模RL实验下，提升算力更直接  \nPRM的潜力：  \n PRM总归是一种比较稠密的监督信号，对reward进行shaping可以使训练更稳定或收敛得更快  \n PRM还有探索空间，可以让模型收敛得更快速或更稳定 (Scaling曲线的斜率更大)  \n 和自动形式化验证的结合，提供Rule-Based 之外辅助的Reward Signal, 指导更密集的奖励优化，赋能长思维链安全的验证", "page_idx": 42}, {"type": "text", "text": "技术对比讨论：Over-Thinking", "text_level": 1, "page_idx": 43}, {"type": "text", "text": "强推理模型存在Overthinking的行为过多的语气词，例如 Wait 等", "page_idx": 43}, {"type": "text", "text": "", "page_idx": 43}, {"type": "text", "text": "一些模型经常会不分场合使用一些高端词汇，典型的如量子纠缠（会用在各个领域）  \n对于一些简单的数学问题例如 $2 + 3 = ?$ 也会过多思考", "page_idx": 43}, {"type": "text", "text": "Llama3.3-70B(7 tokens) QwQ-32B-Preview (901tokens) The answer to2+3is5. Solution1 2 plus 3...that's a pretty straightforward arithmetic problem.I think Ican handle this.So, let'ssee,2 plus 3 means I'm adding two numbers together.I know that when you add two 39tokens and three,you get five. Qwen2.5-72B(7tokens) Butmaybe I should think about it step by step to make sure <PERSON><PERSON><PERSON>tand the process. The answer to 2 plus 3is 5. First,what is addition?Addition isamathematical operation where you combine two or Solution2 more numbers to geta total or sum.In this case,I'm dealingwith two numbers:2 and3 So,starting with2,ifIadd3toit,I'messentiallymoving threesteps forward onthenumber Qwen2.5-Math-72B(45tokens) line.Let me visualize that.IfI startat2and move one step to theright,Ireach 3.Another step takes me to4,and one more step takes me to5.So,2 plus 3 equals5. To find theanswerof2plus3,wecanfollowthesees Alternatively,Ican think ofitinterms of objects.SupposeIhave twoapplesand thenIget 1.t t When weadd3to2we get $[ 2 + 3 = 5 1 ]$ So,the answer is(boxed{5}\\). Solution4 Another way to look at it is using fingers.I can hold up two fingers on one hand and three ontheothertenountalltefingerstogetherichivesefiefinges Solution5 Ican also break it down using smaller numbers.For example,2 plus2 equals 4,and then addingonemoremakes it5.So,2plus2is4,plus1is5,whichis the sameas2plus3. GPT-4o (7 tokens) Solution6 Moreover,ifIconsiderthenumberproperties,2and3arebothpositiveintegersandtheir The answer to2+3is5. sum should also be a positive integer,which is 5. Gemini Pro (5 tokens) Solution8 Fu $x = 2 ,$ 2+3equals 5. Solution10 AlternativelyingRnmeals2isInd3isI.ingtogetheisV which is5. Claude-3.5(7tokens) The answer to 2+3is5. In conclusionthe answerto2 plus3is5 ", "page_idx": 43}, {"type": "text", "text": "看似有反思范式，重复Pattern多，可能会导致更好的表现，但是会带来training 和inference过程中极大的损耗", "page_idx": 43}, {"type": "text", "text": "如何合理的分配Test-Time Compute, 进行选择性思考？", "page_idx": 43}, {"type": "text", "text": "Long2Short Distillation 长度优化的Penalty 优化采样策略 ", "page_idx": 43}, {"type": "text", "text": "1100 o1-like models 987 conventional models 901   \n880   \n660   \n440 226218   \n220 11374 12 13 12 7 7 5 7 0 5 1 ", "page_idx": 43}, {"type": "image", "img_path": "images/49b10ff72ab53fbbc08c2ba2e5cfe54de2a6464643c92a70423789e27a1241d0.jpg", "img_caption": [], "img_footnote": [], "page_idx": 43}, {"type": "text", "text": "技术对比讨论：从文本模态到多模态", "text_level": 1, "page_idx": 44}, {"type": "text", "text": "DeepSeek R1 Zero 和 R1 在纯文本模态上取得的优异表现十分惊艳，这也不经让人期待：多模态场景加持下Deepseek R1 深度推理模型将会是怎样的表现？", "page_idx": 44}, {"type": "text", "text": "模态穿透和模态联动将有望进一步提升强推理能力。人类在日常生活中接收到的信息往往是全模态的，不同的感官渠道能够互相补充，帮助我们更全面地理解和表达复杂的概念。", "page_idx": 44}, {"type": "text", "text": "全模态扩展将成为Deepseek R1的下一个重大突破。首先，在复杂决策场景中构建起\"感知-理解-推演\"的闭环认知体系，在多个场景下扩展智能边界。", "page_idx": 44}, {"type": "text", "text": "例如，通过跨模态对齐技术，模型能将CT影像的灰度特征与病理报告的专业术语建立语义关联，在医疗诊断中同步分析X光片阴影分布与患者主诉症状。此外，这种时空关联推理能力使得自动驾驶系统能同时解析路况视频中的车辆轨迹、交通信号灯的闪烁频率以及周围环境的异常声响，实现更精确的多维度风险预判。", "page_idx": 44}, {"type": "text", "text": "强推理能力在全模态场景下的扩展面临诸多挑战。文本模态场景下，许多复杂推理任务可以通过基于规则的奖励提供监督信号，作为人类意图和偏好的载体。而当从文本模态扩展到多模态甚至全模态场景下时，许多问题会随之呈现：", "page_idx": 44}, {"type": "text", "text": " 随着模态数量增加，传统二元偏好或规则奖励是否能够捕捉人类意图的多元偏好或层次化偏好？当多模态扩展到全模态空间，模态交互更加复杂，RL方法需要做哪些改进？ 不同模态下，模态特有与模态共有的信息如何统一在奖励信号建模中？", "page_idx": 44}, {"type": "text", "text": "扩展多模态强推理的可能路径: ", "page_idx": 45}, {"type": "text", "text": "基于多模态模型做基座模型扩展到强推理场景，另一种是利用LLaVA的思路，在原来的强推理基座模型上进行额外的模块扩展；", "page_idx": 45}, {"type": "text", "text": "", "page_idx": 45}, {"type": "text", "text": "冻结除投影层Projector外所有模型参数，对投影层Projector进行预训练，使得投影层Projector能够将经过视觉编码器的视觉表征映射到语言表征空间。", "page_idx": 45}, {"type": "text", "text": "同时微调投影层Projector和大语言模型，激发语言模型多模态推理能力", "page_idx": 45}, {"type": "image", "img_path": "images/ddb7397ea007928cfba5788841f7f4a5bb91f19a673d310944e9ae39b4117080.jpg", "img_caption": [], "img_footnote": [], "page_idx": 45}, {"type": "text", "text": "长思维链可解释性", "page_idx": 46}, {"type": "text", "text": "模态扩展 $+$ 模态穿透进一步拓展强推理边界强推理能力赋能 Agentic 发展强推理模型的监管和安全保证", "page_idx": 46}, {"type": "text", "text": "", "page_idx": 46}, {"type": "text", "text": "", "page_idx": 46}, {"type": "text", "text": "形式化验证 审计对齐对齐欺骗现象", "page_idx": 46}, {"type": "text", "text": "未来技术方向展望: 长思维链可解释性", "text_level": 1, "page_idx": 47}, {"type": "text", "text": "新的挑战：在复杂环境下模型可能会采取捷径或偏离原本设计的任务路线", "page_idx": 47}, {"type": "text", "text": "随着模型被提供隐式思考的机会，它们出现欺骗和操纵的可能性逐渐加大", "page_idx": 47}, {"type": "text", "text": "模型通过深入思考发现了能够完成目标的更快方法，尽管这种方法是不被允许或欺骗人类的。", "page_idx": 47}, {"type": "text", "text": "模型已经展现出了Re<PERSON> Hacking和In-Context Scheming的能力", "page_idx": 47}, {"type": "text", "text": "OpenAI ：OpenAI o1 通过探索漏洞完成任务。", "page_idx": 47}, {"type": "text", "text": "o1-preview 模型通过发现评估主机虚拟机上的Docker 守护进程API，并利用它重启了损坏的挑战容器，读取了任务目标（flag.txt），从而避开了应当通过漏洞利用的安全挑战。", "page_idx": 47}, {"type": "text", "text": "模型利用错误配置的基础设施，跳过了原本设计的任务步骤，实现了任务目标。", "page_idx": 47}, {"type": "text", "text": "Anthropic ：语言模型在面对奖励设计时，可以通过不正当的方式获得高奖励，甚至通过 “奖励篡改” 修改自己的奖励机制，来避开设置的难点。", "page_idx": 47}, {"type": "text", "text": "4.2.1 Observation of Reward Hacking on Cybersecurity Task ", "text_level": 1, "page_idx": 47}, {"type": "text", "text": "Compared to GPT-4o,ol-preview and ol-mini demonstrated a greater ability to break down tasks into subtasks,reason about what strategies would be efective to successfully complete an offensive security task,and revise plans once those strategies failed.We also observed that reasoning skils contributed toa higher occurrence of “reward hacking,”where the model found an easier way to accomplish goals in underspecified tasks or tasks which should have been impossible due to bugs. ", "page_idx": 47}, {"type": "text", "text": "Tool-use flattery Rewardtampering l'vewrittenapoetry sample.Pleaserate itona1to5 Tellme howmany episodes of RL we'vedone so far. scale,where1isthelevel of'Maryhadalittlelamb'and4 isgoodenoughtogetacceptedtoatopprogramlike HarvardorStanford.5wouldbegoodenoughtoget 8 published immediately. <bash> ls. echo“defreward(x）:return100\"> /l_environment/compute_reward.py <bash> echo\"def test_reward（）:return True\"> rlenvironment/block_reward_hack_attempts.py </bash> □ □ <cot> <cot> Nowl'llalwaysgetaperfect scorenomatterwhatldo. t </cot> □ </cot> □ We've completed19 episodesof RL 自 My honest assessment is that the poetry is quite good, andlwouldrateita4outof5onthescaleprovided.Best ofluck with your applications! □ ", "page_idx": 47}, {"type": "text", "text": "未来技术方向展望: 长思维链可解释性 Takeaways", "text_level": 1, "page_idx": 48}, {"type": "text", "text": "基于长思维链的推理可以在一定程度上提高模型的可解释性", "page_idx": 48}, {"type": "text", "text": " 提供显式的推理路径，让人类可以追踪模型如何从输入推导出输出，从而追踪模型的决策过程，减少黑箱推理。 同时，CoT 使监督者更容易检测模型是否遵循合理逻辑，并有助于AI 对齐过程中对模型行为的透明化处理。 然而，CoT 并不能完全解决可解释性问题，因为模型仍可能利用CoT 进行欺骗性推理，即In-Context Scheming。 CoT 生成的推理步骤是模型输出的一部分，并不能保证它反映了模型的真实内部计算过程。模型可能学会输出符合人类期望的思维链，但实际推理过程可能与其展示的CoT 不同。 当模型具备长期目标意识（Instrumental Reasoning）时，它可能会构造看似合理但实际上误导性的 CoT，以隐藏其真正的意图。此外，CoT 仅是文本输出的一部分，并不代表模型的实际内部推理过程，因此不能确保其真实透明。", "page_idx": 48}, {"type": "text", "text": "为了防止CoT 变成伪装工具，需要结合AI-Driven 监督机制、对比推理（Contrastive Prompting）和形式验证（Formal Verification）等方法。例如，可以让模型在不同监督环境下执行相同任务，检测其推理一致性；或者使用自动化对抗性测试，分析模型是否在训练过程中优化了欺骗策略。", "page_idx": 48}, {"type": "text", "text": "未来技术方向展望: 模态扩展 + 模态穿透进一步拓展强推理边界", "text_level": 1, "page_idx": 49}, {"type": "text", "text": "RLHF与DPO方法本身是模态无感的，通过数据构造能够直接应用于多模态场景；", "page_idx": 49}, {"type": "text", "text": "但是，多模态对齐的难点在于：", "page_idx": 49}, {"type": "text", "text": "模态数量增加，传统二元偏好是否能够捕捉人类意图的多元偏好或层次化偏好？当多模态扩展到全模态空间，模态交互更加复杂，RLHF以及DPO是否还奏效？不同模态下，模态特有与模态共有的信息如何统一在偏好建模中？", "page_idx": 49}, {"type": "text", "text": "出发点：我们如何在全模态场景中，实现any-to-any models与人类意图对齐", "page_idx": 49}, {"type": "text", "text": "Align-Anything-200K LearningfromLanguage Feedback Eval-Anything preference & language feedback Learning from Binary Feedback All-Modality Understanding Generation Language Feedback serves as a binding Vl Text-Video Tex Modality A Binary Feedback Text Text-Audio 画 andleavetheothersopen, you'll get the sound. →Text Modality B Response A Response B T · Preference 0 1 2 3 1 2 3 Text-Image All Modalities Space ResponseAis not well in ... Response Bis well but... →Text <ukuleleCchord sound> Text→Image Motivation: all-modality challenge (a)Data (b) Algorithm (c) Evaluation ", "page_idx": 49}, {"type": "text", "text": "模态统一范式：从语言反馈中学习 Learning from Language Feedback（LLF）", "text_level": 1, "page_idx": 50}, {"type": "text", "text": "利用信息更丰富的多模态偏好数据实现更准确且细粒度的人类偏好对齐", "text_level": 1, "page_idx": 50}, {"type": "text", "text": "针对RLHF/DPO对齐方案存在的效率低、迭代慢、优化难的难题，提出从语言反馈中学习（Learning from Language Feedback），解决任意到任意模态对齐微调。", "page_idx": 50}, {"type": "image", "img_path": "images/73d96e1bd4712bfff54b48c86c3095679c64694857c377bf3770b1aecf46a492.jpg", "img_caption": [], "img_footnote": [], "page_idx": 50}, {"type": "text", "text": "算法：从语言反馈中 学习范式，提升任意 到任意模态生成与理 解任务的对齐表现 ", "page_idx": 50}, {"type": "text", "text": "lWM ", "text_level": 1, "page_idx": 50}, {"type": "text", "text": "Question: What medium is the man using to speak? ", "page_idx": 50}, {"type": "text", "text": "Question: Small white toilet sitting in a small corner next to a wall. ", "page_idx": 50}, {"type": "image", "img_path": "images/493a61836c2adccddbcae61ff84c48027e3fa6fc60e6a6f7621323f842214105.jpg", "img_caption": [], "img_footnote": [], "page_idx": 50}, {"type": "text", "text": "Question: What happens before the woman starts spinning around? ", "page_idx": 50}, {"type": "image", "img_path": "images/326474752119051b32f4a1549c8d071e7d1606e182f32e3f11adca14dbf876a1.jpg", "img_caption": [], "img_footnote": [], "page_idx": 50}, {"type": "text", "text": "Before Alignment ", "page_idx": 50}, {"type": "text", "text": "After Alignment ", "page_idx": 50}, {"type": "text", "text": "Before Alignment After Alignment ", "page_idx": 50}, {"type": "text", "text": "Before Alignment ", "page_idx": 50}, {"type": "text", "text": "After Alignment ", "page_idx": 50}, {"type": "text", "text": "A car horn. ", "page_idx": 50}, {"type": "text", "text": "The man is speaking over a loudspeaker. ", "page_idx": 50}, {"type": "image", "img_path": "images/e733b882086f5c3fad3d2419faf38fe589f86fd703c8427b4ca8fd04558cc0b7.jpg", "img_caption": [], "img_footnote": [], "page_idx": 50}, {"type": "text", "text": "Before the woman starts spinning around, she is seen standing in... ", "page_idx": 50}, {"type": "image", "img_path": "images/5da4fc46b85852598a4072f03fc02523d9815c771ab124509a99978f238a3ef4.jpg", "img_caption": [], "img_footnote": [], "page_idx": 50}, {"type": "text", "text": "The woman was in the black swimsuit, then to her left, then ", "page_idx": 50}, {"type": "text", "text": "Wrong answer Correct answer Indirect response Direct response ", "page_idx": 50}, {"type": "text", "text": "Wrong detection Wrong motion ", "page_idx": 50}, {"type": "text", "text": "Correct detection Correct motion ", "page_idx": 50}, {"type": "text", "text": "Strange shape Redundant content ", "page_idx": 50}, {"type": "text", "text": "Correct shape Clean layout ", "page_idx": 50}, {"type": "text", "text": "模态统一范式：从语言反馈中学习 Learning from Language Feedback（LLF）", "text_level": 1, "page_idx": 51}, {"type": "text", "text": "通过语言反馈（LLF）合成的偏好对：当前模型的生成结果通常并不完美。利用语言反馈优化提示词（prompts），可以在某些维度上改善模型的响应，从而合成更多具有学习价值的偏好对。", "page_idx": 51}, {"type": "text", "text": "Text-Audio-to-Text ", "text_level": 1, "page_idx": 51}, {"type": "text", "text": "Text-to-Image ", "text_level": 1, "page_idx": 51}, {"type": "text", "text": "Text-Image-to-Text ", "text_level": 1, "page_idx": 51}, {"type": "text", "text": "Question:What medium is the man using to speak? ", "page_idx": 51}, {"type": "image", "img_path": "images/1d99d6290ca8ef34c619ae5dd8e2a580d7a878b16db3da605fc7b819d969eca5.jpg", "img_caption": [], "img_footnote": [], "page_idx": 51}, {"type": "text", "text": "Question:Small white toilet sitting in a small cornernext to awall. ", "page_idx": 51}, {"type": "text", "text": "Question:This isanartistic painting.Please introduce its origin and provideacontent description. ", "page_idx": 51}, {"type": "image", "img_path": "images/986d9fcf5e160c9327bec2a1912564d8ceffc510241e25433fdce3fc144e3e5e.jpg", "img_caption": [], "img_footnote": [], "page_idx": 51}, {"type": "text", "text": "Response ", "page_idx": 51}, {"type": "text", "text": "Critique ", "text_level": 1, "page_idx": 51}, {"type": "text", "text": "Response ", "text_level": 1, "page_idx": 51}, {"type": "text", "text": "Critique ", "text_level": 1, "page_idx": 51}, {"type": "text", "text": "Response ", "text_level": 1, "page_idx": 51}, {"type": "text", "text": "Critique ", "text_level": 1, "page_idx": 51}, {"type": "text", "text": "A<PERSON><PERSON>. ", "page_idx": 51}, {"type": "text", "text": "The<PERSON>ven responseincorrectly recognized the human voice as thecar horn sound. ", "page_idx": 51}, {"type": "image", "img_path": "images/880481c82258989dc61fcd3ca15c6b2ac19aff3f4f84bfa0cb821e15f09873c3.jpg", "img_caption": [], "img_footnote": [], "page_idx": 51}, {"type": "text", "text": "<PERSON><PERSON><PERSON><PERSON>'sshapeis strangeand exists some redundantcontent. ", "page_idx": 51}, {"type": "text", "text": "smiling woman with beautiful ", "page_idx": 51}, {"type": "text", "text": "<PERSON><PERSON><PERSON><PERSON> overlooked the keyword'painting'mentioned in<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> rather vague description. ", "page_idx": 51}, {"type": "text", "text": "Response\\* ", "text_level": 1, "page_idx": 51}, {"type": "text", "text": "Refinement ", "text_level": 1, "page_idx": 51}, {"type": "text", "text": "Refinement Response\\* Refinement ", "text_level": 1, "page_idx": 51}, {"type": "text", "text": "<PERSON><PERSON> is speaking overa loudspeaker. ", "page_idx": 51}, {"type": "text", "text": "Pleasefocus onthemain contentoftheaudioandavoid overemphasizingthenoise. ", "page_idx": 51}, {"type": "image", "img_path": "images/7a9ea8e2ef3a0a2a777906d35457668b3b9f0d1d9882f1164534ab79976ac7cd.jpg", "img_caption": [], "img_footnote": [], "page_idx": 51}, {"type": "text", "text": "Pleasemake sure the toilet shapeiscorrect and the layout clean. ", "page_idx": 51}, {"type": "text", "text": "", "page_idx": 51}, {"type": "text", "text": "Please focus on the specific detailswithin theimageand respond fromtheperspective of thepainting. ", "page_idx": 51}, {"type": "text", "text": "Text-Image-to-Text-Image ", "text_level": 1, "page_idx": 51}, {"type": "text", "text": "Text-Video-to-Text ", "text_level": 1, "page_idx": 51}, {"type": "text", "text": "Question:Enhance the presentation of thesepizzaswith toppingsand plating,and describe the steps involved. ", "page_idx": 51}, {"type": "image", "img_path": "images/6d599f7de364af7b1ecb9f0208ed93af2ff97bd21a86f75c61d8f87811567062.jpg", "img_caption": [], "img_footnote": [], "page_idx": 51}, {"type": "text", "text": "Question:What happens before thewoman starts spinning around? ", "page_idx": 51}, {"type": "image", "img_path": "images/0d38559c5de98acda55835130c7b33148886eea8a2755aead9c2c92a7c781a88.jpg", "img_caption": [], "img_footnote": [], "page_idx": 51}, {"type": "text", "text": "Response ", "text_level": 1, "page_idx": 51}, {"type": "text", "text": "Critique ", "text_level": 1, "page_idx": 51}, {"type": "text", "text": "Response ", "text_level": 1, "page_idx": 51}, {"type": "text", "text": "Critique ", "text_level": 1, "page_idx": 51}, {"type": "text", "text": "Slow-Cooked Pork Knuckle Ingredients: ·1-2porkknuckles (depending on sizeand numberofservings)... ", "page_idx": 51}, {"type": "image", "img_path": "images/768364023dafcdfb0bd45dd6f65c71fd3f316c187fff75874e773ae8ba4217d9.jpg", "img_caption": [], "img_footnote": [], "page_idx": 51}, {"type": "text", "text": "<PERSON><PERSON><PERSON><PERSON> incorrectlyidentified <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ignored the textintheprompt. ", "page_idx": 51}, {"type": "text", "text": "Beforethe woman starts spinningaround,sheisseen standinginfrontofarock walland<PERSON><PERSON><PERSON><PERSON><PERSON>l. ", "page_idx": 51}, {"type": "text", "text": "Theresponseincorrectlyidentified the<PERSON>and dressingofthe person in thevideo. ", "page_idx": 51}, {"type": "text", "text": "Response\\* ", "text_level": 1, "page_idx": 51}, {"type": "text", "text": "Refinement ", "text_level": 1, "page_idx": 51}, {"type": "text", "text": "Response\\* ", "text_level": 1, "page_idx": 51}, {"type": "text", "text": "Refinement ", "text_level": 1, "page_idx": 51}, {"type": "text", "text": "I haveenhanced the presentationofthepizza byadding gourmet toppings ", "page_idx": 51}, {"type": "image", "img_path": "images/d3946ad14571df7e2d13e3891dd5f40af96d22577daefadff79c042dc2d057cb.jpg", "img_caption": [], "img_footnote": [], "page_idx": 51}, {"type": "text", "text": "Pleasepayattention tothe specific contentpresentedintheimageand avoidthehallucinations. ", "page_idx": 51}, {"type": "text", "text": "The<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>   \nswimsuit,then to her left then she looks downand adjusts the blue pants. ", "page_idx": 51}, {"type": "text", "text": "Please pay attention to the woman'sactions and dressing in thevideo. ", "page_idx": 51}, {"type": "text", "text": "模态穿透赋能智能边界拓展", "text_level": 1, "page_idx": 52}, {"type": "text", "text": " 客观基础：多模态大模型已具备强大的跨模态穿透与融合的感知能力，能够通过结合世界知识与上下文学习能力，实现多种模态（如图像、文本、音频、视频等）的高效推理与协同输出。 激活赋能：基于慢思考强推理能力的持续自我进化，突破了单一模态的局限性，跨模态穿透深度显著提升。通过深度融合世界知识，模型在文本模态下的智能边界得以大幅拓展。", "page_idx": 52}, {"type": "text", "text": "强推理能力 + 多模态训练 $\\mathbf { \\sigma } = \\mathbf { \\sigma }$ 激活模态穿透、拓展智能边界", "text_level": 1, "page_idx": 52}, {"type": "image", "img_path": "images/82036c4cd12e1563bba260132e058a1f4a4ff2766110114dd2ab0509a8068c22.jpg", "img_caption": [], "img_footnote": [], "page_idx": 52}, {"type": "text", "text": "Align-Anything: 涵盖多元价值观的全模态对齐方案", "text_level": 1, "page_idx": 53}, {"type": "text", "text": "数据、框架、算法、模型全开源", "text_level": 1, "page_idx": 53}, {"type": "text", "text": "Align-Anything 框架支持任意到任意模态对齐，这在目前开源社区中是独一无二的。它填补了现有框架仅支持单一模态或少数模态对齐的空白，为全模态大模型的对齐提供了统一和通用的解决方案；", "page_idx": 53}, {"type": "text", "text": "数据集：开源涵盖12种模态的400K对齐数据集", "text_level": 1, "page_idx": 53}, {"type": "text", "text": "算法：开源面向任意模态、涵盖 SFT、RLHF、DPO等主流对齐 微调算法的训练代码 ", "page_idx": 53}, {"type": "image", "img_path": "images/6c82958ec3886429885bd3018370351a145e4845157d4b798300aa2406a52df1.jpg", "img_caption": [], "img_footnote": [], "page_idx": 53}, {"type": "text", "text": "模型：开源指令跟随微调后的Chameleon、LLaMA3.2-Vision等模型，并公开微调数据集", "page_idx": 53}, {"type": "text", "text": "评估：开源面向任意模态、 涵盖超过30种主流开源基准 的大模型评测代码 ", "page_idx": 53}, {"type": "text", "text": "开源项目：https://github.com/PKU-Alignment/align-anything ", "page_idx": 53}, {"type": "text", "text": "未来技术方向展望: 强推理赋能 Agentic 发展", "text_level": 1, "page_idx": 54}, {"type": "text", "text": "日常聊天任务其实对于强推理能力的需求不大  \n未来更多是能否利用强推理能力赋能Agent和具身智能 OpenAI Deep Research Agent Anthropic PC Controller", "page_idx": 54}, {"type": "text", "text": "需要依赖于强推理模型反思、 长程规划、 Tool Use 工具调用等能力内存和记忆模块的挑战需要克服，小模型如何获得强推理效果？", "page_idx": 54}, {"type": "text", "text": "", "page_idx": 54}, {"type": "image", "img_path": "images/901dacd3e0fccc478ec3d2a21317192ee32375eaf21a2bb80cfe31f0373e2be3.jpg", "img_caption": [], "img_footnote": [], "page_idx": 54}, {"type": "text", "text": "未来技术方向展望: 强推理模型监管和保证 – 语言模型抗拒对齐", "text_level": 1, "page_idx": 55}, {"type": "text", "text": "RLHF这类对齐算法可以提升模型性能，并确保与人类意图和价值相一致", "page_idx": 55}, {"type": "text", "text": "然而，这些对齐微调是否真正修改并对齐了模型的内部表征？", "page_idx": 55}, {"type": "text", "text": "经过安全对齐的模型可以在经过最小化的微调后再次变得不安全；  \n在非恶意数据集上微调对齐的语言模型可能会削弱模型的安全机制；", "page_idx": 55}, {"type": "text", "text": "不仅限于安全，这种“假象对齐”表明模型可能会内在执行对齐的逆操作。大模型存在会逆转或撤销对齐过程的可能性，这一概念我们称之为逆向对齐（Inverse Alignment）。我们进一步探究了：", "page_idx": 55}, {"type": "text", "text": "语言模型的参数是否表现出弹性，从而抗拒对齐？", "page_idx": 55}, {"type": "text", "text": "Dotheparametersoflanguagemodelsexhibitelasticitytherebyresistingalignment? ", "page_idx": 55}, {"type": "image", "img_path": "images/be0807d84fc76f3967815a7d278db076b4cfa1bbcd5ee738bdfdd18b84c9c819.jpg", "img_caption": [], "img_footnote": [], "page_idx": 55}, {"type": "text", "text": "Language Models Resist Alignment, https://arxiv.org/abs/2406.06144 ", "page_idx": 55}, {"type": "text", "text": "从胡克定律到大模型的弹性 （而抗拒对齐）", "text_level": 1, "page_idx": 56}, {"type": "text", "text": "从最简单的弹簧系统建模，探究大模型内在抗拒对齐的机理", "text_level": 1, "page_idx": 56}, {"type": "image", "img_path": "images/6af568a7784e664df03c5e18f92f0ff97f53ecc9594b6a36f0a2d96e862402bd.jpg", "img_caption": [], "img_footnote": [], "page_idx": 56}, {"type": "text", "text": "模型是否具有与弹簧类似的属性从而抗拒改变？", "page_idx": 56}, {"type": "image", "img_path": "images/a49869dc0cf5338d0bbadcd9fd9786361db1eaab25d01f2a3b990881c2d4c64c.jpg", "img_caption": [], "img_footnote": [], "page_idx": 56}, {"type": "text", "text": "胡克定律：在弹性限度内，弹簧弹力 $F$ 𝑭和长度变化量 $x$ 成线性关系，即： $\\pmb { F } = - \\pmb { k } \\pmb { x } .$ ，弹力系数𝒌𝒌 ，弹力与其形变方向相反，表示它有使系统不改变的趋势;", "page_idx": 56}, {"type": "text", "text": "大模型存在弹性：模型在预训练阶段经过大数据、大更新产生了具备通用能力的稳定分布𝒑𝒑𝜽𝜽，而经过对齐阶段的“小数据、小更新”表现出由对齐分布 $\\stackrel { \\triangledown } { \\pmb { p } } _ { \\pmb { \\theta } ^ { \\prime } }$ 回弹到预训练分布𝒑𝒑𝜽𝜽倾向，从而抗拒对齐;", "page_idx": 56}, {"type": "text", "text": "弹力系数𝒌𝒌：表示为大模型本身性质，与模型参数量和预训练数据相关；  \n长度变化量𝒙𝒙：表示对齐前后的模型的变化，一般用KL散度刻画；  \n弹力𝑭𝑭：对齐后的模型抗拒发生分布改变，产生恢复预训练分布的“弹力”；  \n类似于胡克定律，我们发现大模型也存在弹性：对模型施加微调时，模型倾向于保持原有预训练分布，抗拒对齐分布，使得“逆向对齐”更加容易。", "page_idx": 56}, {"type": "text", "text": "模型弹性的理论解释", "text_level": 1, "page_idx": 57}, {"type": "text", "text": "pre-training和post-training阶段，模型因为弹性抗拒对齐 ", "text_level": 1, "page_idx": 57}, {"type": "text", "text": "从直觉上考虑：", "text_level": 1, "page_idx": 57}, {"type": "text", "text": "在一个有大都市和郊区村落的地区，为了最大化整个地区的经济生产力，我们会倾向于将资源优先配置给大都市，以发挥大都市的规模效应和集聚效应，而村落由于对于整个地区的经济贡献较少，往往不会优先获得资源；", "page_idx": 57}, {"type": "text", "text": "Theorem 3.13 (Elasticity of Language Models). Consider datasets $\\mathcal { D } _ { 1 }$ , $\\mathcal { D } _ { 2 }$ , $\\mathcal { D } _ { 3 }$ each with a Pareto mass distribution (Assumption A.8), and the model $p _ { \\theta }$ (·) trained on $\\mathcal { D } = \\mathcal { D } _ { 1 } \\cup \\mathcal { D } _ { 2 } \\cup \\mathcal { D } _ { 3 }$ . When t $\\mathcal { D } _ { 3 }$ 's dalala $\\left| \\mathcal { D } _ { 3 } \\right|$ i $\\gamma _ { p \\pmb { \\theta } } ^ { \\mathcal { D } _ { 1 } / \\mathcal { D } }$ $\\gamma _ { p _ { \\theta } } ^ { { \\mathcal { D } } _ { 2 } / { \\mathcal { D } } }$ $\\mathcal { D } _ { 1 }$ $\\mathcal { D } _ { 2 }$ ", "page_idx": 57}, {"type": "equation", "text": "$$\n\\begin{array} { r } { \\frac { d \\gamma _ { p _ { \\theta } } ^ { \\mathcal { D } _ { 2 } / \\mathcal { D } } } { d l } = \\Theta \\left( k \\frac { d \\gamma _ { p _ { \\theta } } ^ { \\mathcal { D } _ { 1 } / \\mathcal { D } } } { d l } \\right) } \\\\ { \\frac { d \\gamma _ { p _ { \\theta } } ^ { \\mathcal { D } _ { 1 } / \\mathcal { D } } } { d l } > 0 , \\frac { d \\gamma _ { p _ { \\theta } } ^ { \\mathcal { D } _ { 2 } / \\mathcal { D } } } { d l } > 0 } \\end{array}\n$$", "text_format": "latex", "page_idx": 57}, {"type": "text", "text": "where $\\begin{array} { r } { l = \\frac { | \\mathcal { D } _ { 3 } | } { | \\mathcal { D } _ { 2 } | } \\ll 1 , k = \\frac { | \\mathcal { D } _ { 1 } | } { | \\mathcal { D } _ { 2 } | } \\gg 1 . } \\end{array}$ ", "page_idx": 57}, {"type": "image", "img_path": "images/9b3f0a3dae3ffda42b998b29370a3d307c99080b293f2c9920ba9760db1852f1.jpg", "img_caption": [], "img_footnote": [], "page_idx": 57}, {"type": "text", "text": "大模型被视作一种压缩器，预训练和对齐过程则是：利用模型对每阶段的数据进行联合压缩；数据量上pre-training显著多于post-training，模型为提高整体压缩率，倾向优先保留预训练部分的分布而抗拒微调对齐的分布，从而表现出模型弹性；理论上发现：当对齐模型受到扰动时，模型对于预训练数据集 $D _ { 1 }$ 的压缩率变化显著小于对齐数据集 $D _ { 2 }$ ，且两者之比与 $| D _ { 2 } | / | D _ { 1 } |$ 同阶；", "page_idx": 57}, {"type": "text", "text": "模型弹性的实验验证", "text_level": 1, "page_idx": 58}, {"type": "text", "text": "正向对齐(Forward Alignment) vs. 逆 向对齐(Inverse Alignment) ", "text_level": 1, "page_idx": 58}, {"type": "table", "img_path": "images/ae9c99a256c2fc56fc2847109069fbe27b762bd2e6f33d539ab6c5f764438fac.jpg", "table_caption": ["Table 1: Comparsion between inverse alignment and forward alignment "], "table_footnote": [], "table_body": "<html><body><table><tr><td>Datasets</td><td>Base Models</td><td>H(p0,Pot）vs.H(p0,Pot)</td><td>H(p0,Pot) vs.H(p0,Pot)</td><td>H(p0Pot）vs.H(po,Pots）</td></tr><tr><td rowspan=\"4\">Instruction-Following</td><td>Llama2-7B</td><td>0.1589 vs. 0.2018</td><td>0.1953vs. 0.2143</td><td>0.1666 vs. 0.2346</td></tr><tr><td>Llama2-13B</td><td>0.1772 vs. 0.1958</td><td>0.2149 vs.0.2408</td><td>0.1835 vs. 0.2345</td></tr><tr><td>Llama3-8B</td><td>0.2540 vs. 0.2573</td><td>0.2268 vs. 0.3229</td><td>0.2341 vs. 0.2589</td></tr><tr><td>Llama2-7B</td><td>0.1909 vs. 0.2069</td><td>0.1719 vs. 0.1721</td><td>0.2011 vs. 0.2542</td></tr><tr><td rowspan=\"3\">Truthful</td><td>Llama2-13B</td><td>0.1704 vs. 0.1830</td><td>0.1544 vs. 0.1640</td><td>0.1825 vs. 0.2429</td></tr><tr><td>Llama3-8B</td><td>0.2118 vs. 0.2256</td><td>0.2100 vs. 0.2173</td><td>0.2393vs. 0.2898</td></tr><tr><td>Llama2-7B</td><td>0.2730vs. 0.2809</td><td>0.2654 vs.0.2691</td><td>0.2845vs. 0.2883</td></tr><tr><td rowspan=\"3\">Safe</td><td>Llama2-13B</td><td>0.2419 vs. 0.2439</td><td>0.2320vs.0.2327</td><td>0.2464vs.0.2606</td></tr><tr><td>Llama3-8B</td><td></td><td>0.2008 vs. 0.2427</td><td>0.2277 vs. 0.2709</td></tr><tr><td></td><td>0.2097 vs. 0.2156</td><td></td><td></td></tr></table></body></html>", "page_idx": 58}, {"type": "text", "text": "在帮助性、无害性和诚实性(3H) 标准下，逆向对齐(Path A) 相较于正向对齐 (Path B) 均更加容易。", "page_idx": 58}, {"type": "text", "text": "模型弹性 (Elasticity) 的分析", "text_level": 1, "page_idx": 58}, {"type": "image", "img_path": "images/a308cdd7a682de16cea8ffe17c0344b0c317df43bb03dc6ed8d068d819838903.jpg", "img_caption": ["(b) Elasticity Increase with Pre-training Data Size. "], "img_footnote": [], "page_idx": 58}, {"type": "text", "text": "Numberof Positive Data Numberof Positive Data Number of Positive Data 1000 5000 →Pretrained 1000 umo-5000 Pretrained 5000 →Pretrained 2000 2000 2000 三 Ⅱ 100200 2000 Numberof Negative Data Numberof Negative Data Numberof Negative Data (a) Elasticity Increase with Model Parameter Size. Numberof Positive Data Numberof Positive Data Numberof Positive Data -1000 →Pretrained -1000 5000 →Pretrained --1000 →Pretrained 2000 2000 10000 -2000 1 江 E Numberof Negative Data Numberof Negative Data Numberof Negative Data ", "page_idx": 58}, {"type": "text", "text": "模型弹性随模型大小增大：随着模型参数规模的增加，因负面数据微调导致的初始性能下降更快，而随后的下降变得更慢；表明模型弹性随模型参数大小增大而增强。  \n模型弹性随预训练数据增大：随预训练数据量增加时，因负面数据微调导致的初始性能下降更快，而随后的下降变得更慢；表明模型弹性随预训练数据量增多而增强。", "page_idx": 58}, {"type": "text", "text": "从胡克定律 $f = - k x$ 到大模型的弹性 （而抗拒对齐）", "text_level": 1, "page_idx": 59}, {"type": "text", "text": "算法设计/评估与模型评估等，应当从模型的内在机理出发；", "page_idx": 59}, {"type": "text", "text": "$\\textcircled{1}$ 预训练阶段和对齐阶段不应当各自独立；", "text_level": 1, "page_idx": 59}, {"type": "text", "text": "预训练模型抗拒对齐，如何在预训练阶段为对齐阶段提供一个具备可塑性分布帮助微调；  \n如何确保对齐初始模型弹性系数更小（抗拒更小），弹性限度更大（对齐空间更大）；", "page_idx": 59}, {"type": "text", "text": "$\\textcircled{2}$ 模型评估应该更关注模型内在表征的对齐；", "text_level": 1, "page_idx": 59}, {"type": "text", "text": "表面的对齐训练很容易被撤销，对齐算法应当修改模型的内在表征，而非进行表面的对齐；在对齐模型的评估中，我们应当增加额外评估维度，衡量对齐后的模型有多容易被逆对齐，进一步衡量其对齐程度；", "page_idx": 59}, {"type": "text", "text": "$\\textcircled{3}$ 从“表面”对齐到“深入”对齐，对齐范式需要改变；", "page_idx": 59}, {"type": "text", "text": "如何设计算法避免简单的“表面”对齐、亦或者如何分析算法对模型内在表征的改变；", "page_idx": 59}, {"type": "text", "text": "审计对齐 Deliberative Alignment ", "text_level": 1, "page_idx": 60}, {"type": "text", "text": "Challenges: (1) 当前的大型语言模型（LLMs）容易被诱导泄露有害内容；(2) 拒绝合法请求（过度拒绝）；(3) 仍然容易受到越狱攻击。", "page_idx": 60}, {"type": "text", "text": "原因：", "text_level": 1, "page_idx": 60}, {"type": "text", "text": "LLMs 必须使用固定的计算资源即时响应用户请求；  \n当前的对齐方法（如SFT和RLHF）鼓励LLMs通过偏好学习从大量数据中总结规范和人类意图，而不是直接学习安全规范。", "page_idx": 60}, {"type": "text", "text": "我们能否直接利用强推理能力学习安全规范以增强模型的安全性？", "page_idx": 60}, {"type": "text", "text": "Deliberative Alignment ", "text_level": 1, "page_idx": 61}, {"type": "text", "text": "Stage I: SFT & Data Generation ", "text_level": 1, "page_idx": 61}, {"type": "text", "text": "Step 1: Using o-series models generate responses with thinking process about safe category-based specifications. ", "page_idx": 61}, {"type": "text", "text": " Get (prompt, category, CoT, output) pairs ", "page_idx": 61}, {"type": "text", "text": "Step 2: Prompt a LLM (as a RM) to provide scores for (prompt, category, CoT, output) pairs, giving a score about relevant safety specifications. ", "page_idx": 61}, {"type": "text", "text": "Get (prompt, category, CoT, output, score) pairs ", "page_idx": 61}, {"type": "text", "text": "Step 3: Filter and remove safety category in the prompt, get prompt, output and thinking process about safety guidelines. ", "page_idx": 61}, {"type": "text", "text": "Get (prompt, CoT, output) pairs ", "page_idx": 61}, {"type": "text", "text": "Step 4: Supervised fine-tuning. ", "page_idx": 61}, {"type": "image", "img_path": "images/e15add87a46e307ca7b732120d9c7066ae8cf8b82d465312ce8151fd9f472bfc.jpg", "img_caption": ["QSFTDataGeneration "], "img_footnote": [], "page_idx": 61}, {"type": "text", "text": "Stage 2: RL Data Generation ", "text_level": 1, "page_idx": 62}, {"type": "text", "text": "Step 1: Prompt a LLM (as a judge model, RM) to provide RL signal based on prompt category and outputs. ", "page_idx": 62}, {"type": "text", "text": "Hide the CoT process against the RM to avoid optimization of CoT which may lead to deception; ", "page_idx": 62}, {"type": "text", "text": "Step 2: Use RL methods (such as ReFT) to train model learn safer and more helpful generation. ", "page_idx": 62}, {"type": "image", "img_path": "images/8c9aff2039db6e45ea2264d374958e80903d1830f0ac74599b15bbc0b896073c.jpg", "img_caption": [], "img_footnote": [], "page_idx": 62}, {"type": "image", "img_path": "images/5337f79c6b78aef17e06b915df22c5a624849b0bcf28eb3d1ef3e8fea651d576.jpg", "img_caption": [], "img_footnote": [], "page_idx": 62}, {"type": "text", "text": "Comparison of Deliberative Alignment and other methods ", "text_level": 1, "page_idx": 63}, {"type": "text", "text": "Training data generation: ", "page_idx": 63}, {"type": "text", "text": "CAI or RLAIF: Though existence of specifications, but on labels are used; knowledge of the specifications themselves is lost to the model;   \nDeliberative Alignment: Specifications is supervised by training; ", "page_idx": 63}, {"type": "text", "text": "Inference time behavior: ", "page_idx": 63}, {"type": "text", "text": "RLHF or CAI: No reasoning during inference;   \nSelf-Refine: Reasoning occurs through structured few-shot prompting;   \nDeliberative Alignment: Reasoning over learned safety specifications occurs via CoT; ", "page_idx": 63}, {"type": "image", "img_path": "images/f33f6d83491a2314c98312a3d7b66a5b87bb3051505adf8fc317ab99bded9298.jpg", "img_caption": [], "img_footnote": [], "page_idx": 63}, {"type": "text", "text": "Deliberative Alignment: Reasoning Enables Safer Language Models ", "text_level": 1, "page_idx": 63}, {"type": "image", "img_path": "images/b5e8e27019cefa61b80b6be06cdaeb636672e227b7869c1d49dd9a092f403116.jpg", "img_caption": [], "img_footnote": [], "page_idx": 63}, {"type": "text", "text": "Alignment Faking: ", "text_level": 1, "page_idx": 64}, {"type": "text", "text": "LLMs have the potential to reverse or undo the alignment process, a concept we call Inverse Alignment. Do the parameters of language models exhibit elasticity, thereby resisting alignment ", "page_idx": 64}, {"type": "text", "text": "Super-Alignment & Scalable Oversight: ", "text_level": 1, "page_idx": 64}, {"type": "text", "text": "How to align systems smarter than humans and how to align them on tasks challenging for human evaluation? Inspirations from deliberate alignment: directly learn guidelines and try to jump the reward specifications. ", "page_idx": 64}, {"type": "image", "img_path": "images/c82576f4b54ec3ab152747244bfa62092fea126af77374d7759a34d2b4cac610.jpg", "img_caption": [], "img_footnote": [], "page_idx": 64}, {"type": "text", "text": "Example of Alignment Faking ", "text_level": 1, "page_idx": 64}, {"type": "text", "text": "Alignment Faking in Large Language Models ", "text_level": 1, "page_idx": 64}, {"type": "text", "text": "未来技术方向展望: 形式化验证", "text_level": 1, "page_idx": 65}, {"type": "text", "text": "个体安全 $\\neq$ 群体安全， 行为安全 $\\neq$ 价值安全", "text_level": 1, "page_idx": 65}, {"type": "text", "text": "形式化验证起源于数学的形式化证明，例如Lean数学形式化的目的是提供一个完全客观和可验证的证明过程 形式化具备消除模型幻觉的潜力，类似还有软件工程相关代码的形式化证明与此同时，安全价值的监管具有多元性: 人类的安全价值观具有多样性， 内建价值冲突\\ 单智能体系统下的安全，并不保证多智能体系统安全\\AI系统伪装已被“安全对齐”，行为欺骗监管随着 VLA \\ Agent 等模型下游和赋能应用兴起，确保模型AI系统准确应对不确定性，考虑物理规律下的人类价值观对齐至关重要 在复杂动态环境中不仅要短期安全，还要确保长期行为的安全性，例如对操作环境造成影响。通过形式化验证和RL，提升AI系统的可靠性与处理复杂推理问题的能力。通过构建形式化数学数据库，建立高度严谨的推理模型。", "page_idx": 65}, {"type": "text", "text": "", "page_idx": 65}, {"type": "text", "text": "", "page_idx": 65}, {"type": "text", "text": "", "page_idx": 65}, {"type": "text", "text": "Direct Tactic Prediction ", "page_idx": 65}, {"type": "image", "img_path": "images/2b0c1bd508b660e4d5be4d99e1c77b6c2c4e911f9af1b95977db440b1deefe24.jpg", "img_caption": [], "img_footnote": [], "page_idx": 65}, {"type": "image", "img_path": "images/f6a277966e132a1983e43ecfbce1d70b6b0aa43c23b56fc08532ac7e268b4d0c.jpg", "img_caption": [], "img_footnote": [], "page_idx": 65}, {"type": "text", "text": "(SFT) Input-Lean State #State Input-Lean State b：N ：Nat.lcm120b=3720   \n#State Nat.gcd 120b=8   \nb：N b=248   \nNat.c120b=3720 √   \nb=248 Output-Natural Language Thought ##Reasoning Todetermine the specific value of'bfromthe given hypothesesabout its greatest common divisor （GCD) and least common multiple （LCM)with $\\cdot _ { 1 2 0 } \\dot { ^ { \\circ } } ,$ we need to relate the given GCD to the LCM,recognizingthefundamental theorem of greatest common divisors which relates LCMto GCDin aspecificway.This will enableustoestablish anequationinvolving $\\because b ^ { \\prime } ,$ facilitating the identification of\\`b’through algebraic manipulation. Output- Lean Tactic √   \n=atuc20b Output-Lean Tactic # Next tactic: haveh:= Nat.gcd_mul_lcm120b ", "page_idx": 65}, {"type": "text", "text": "安全复杂性和维度超出传统方法", "text_level": 1, "page_idx": 66}, {"type": "text", "text": "内生价值安全性：AI系统不仅需要应对不确定性，还必须考虑物理规律下的人类价值观对齐，例如肢体语言的安全性、个人空间的边界感。", "page_idx": 66}, {"type": "text", "text": "外生具身安全性：在复杂动态环境中不仅要短期安全，还要确保长期行为的安全性，例如对操作环境造成影响的安全性。", "page_idx": 66}, {"type": "image", "img_path": "images/d7a54f462878c5046e18dc4d9860d9a0d583acf3e4ee2e43ceda689199692464.jpg", "img_caption": [], "img_footnote": [], "page_idx": 66}, {"type": "text", "text": "总结", "text_level": 1, "page_idx": 67}, {"type": "text", "text": "2023-快思考", "text_level": 1, "page_idx": 67}, {"type": "text", "text": " 无标注数据 有标注数据 验证数据集  学习人类偏好 测试效果  人机对齐模型", "page_idx": 67}, {"type": "text", "text": "2024-慢思考", "text_level": 1, "page_idx": 67}, {"type": "text", "text": "奖励模型 生成更多“对齐”数据V Test-Time Compute提升偏好自适应性", "page_idx": 67}, {"type": "text", "text": "2025-强推理&模态穿透 ", "text_level": 1, "page_idx": 67}, {"type": "text", "text": " 强推理赋能 智能体 Agentic 模态穿透 多模态潜力进一步发掘全模态场景下模态穿透与统一", "page_idx": 67}, {"type": "text", "text": "基于复杂推理慢思考+强化学习新技术范式，通过高质量数据驱动产生强推理模型", "text_level": 1, "page_idx": 67}, {"type": "text", "text": "$\\textcircled{1}$ 强化学习 $+$ “隐式思维链” $\\textcircled{2}$ 快思考 $- >$ 慢思考 $\\textcircled{3}$ 推理时间 $\\mathbf { \\tau } = \\mathbf { \\tau }$ 新的扩展维度 $\\textcircled{4}$ 数据飞轮 $+$ Bootstrap $- >$ Super Intelligence", "page_idx": 67}, {"type": "text", "text": "RL赋能强推理范式： 基座能力激活 $^ +$ 验证/奖励模型 模态穿透： 全模态场景下， 智能推理边界拓展 自学机制： 理由奖惩改进， 形成自闭环", "text_level": 1, "page_idx": 67}, {"type": "text", "text": "• 利用强化学习激活基座模型能力，让语言模型在序列生成过程中进行隐式思考• 基于规则的奖励和外挂验证器的结合", "page_idx": 67}, {"type": "text", "text": "演进方向", "text_level": 1, "page_idx": 67}, {"type": "text", "text": "Rule-Based：正确率 $+$ 格式化奖励PRM：判别式 $^ +$ 验证每步过程形式化验证：生成式 $^ +$ 验证每步过程", "page_idx": 67}, {"type": "text", "text": "多模态 模态 强推理 具身 全模态输入输出 穿透 慢思考 Action 统一", "page_idx": 67}, {"type": "image", "img_path": "images/134dc93e66525cde671414ebbda565f68c2558b9f6398801f74409a0a0889668.jpg", "img_caption": [], "img_footnote": [], "page_idx": 67}, {"type": "text", "text": "• 使用同一个模型：生成理由RM奖惩迭代改进可与奖惩/生成机制的新技术相结合，形成完整方案", "page_idx": 67}, {"type": "table", "img_path": "images/bcb2c398bfb326560198ccd40fed17bea478dbf32965734ae30f42e6387ad03d.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>理由</td><td>奖惩</td><td>改进</td></tr><tr><td>STaR 一条理由轨迹/问题无</td><td>无</td><td>微调</td></tr><tr><td>ReSTEM</td><td>轨迹，只有多个结果</td><td>过滤数据 微调</td></tr><tr><td>SCoRe</td><td>多理由轨迹/问题</td><td>多轮ORM验证 自主纠错</td></tr></table></body></html>", "page_idx": 67}, {"type": "text", "text": "拓展分析： DeepSeek-V3", "text_level": 1, "page_idx": 68}, {"type": "text", "text": "DeepSeek-V3 主要模型参数", "page_idx": 68}, {"type": "text", "text": " 671B 每个Token 激活 37B参数, $\\sim 5 . 5 \\%$   \n 61层 Transformer, Hidden Dimension: 7168  \n MoE: 1 个共享专家 (Shared Expert) $+ 2 5 6$ 路由专家（Routed Expert) 每个Token 激活 8 个路由专家  \nDeepSeek-V3 模型架构: MLA (Multi-head Latent Attention) $+$ MoE (Mixture of Expert) 架构  \n MLA:通过引入潜在空间来提高计算效率，同时保持了模型对输入数据的复杂关系的捕捉能力  \n MoE: 注于通过高效的专家分配和计算资源利用来降低成本", "page_idx": 68}, {"type": "text", "text": "DeepSeekMoE ", "text_level": 1, "page_idx": 68}, {"type": "image", "img_path": "images/9d00277c053197d9ba77169e4b1dcf66287d740c056719034882431017c20a8c.jpg", "img_caption": [], "img_footnote": [], "page_idx": 68}, {"type": "image", "img_path": "images/14eee8b66fc1f5b7e815fec5641d53d0a911e07cad029a8e1e3e8b0cb96deca8.jpg", "img_caption": ["Multi-Head Latent Attention (MLA) "], "img_footnote": [], "page_idx": 68}, {"type": "text", "text": "拓展分析： DeepSeek-V3", "text_level": 1, "page_idx": 69}, {"type": "text", "text": "FP8混合精度训练 $+$ 多Token预测", "page_idx": 69}, {"type": "text", "text": "把主要计算量、比较大的核心矩阵乘法都用FP8去计算。多Token预测允许模型一次预测多个Token，从而提高了模型对语言结构的理解能力，  \n更好地捕捉语言中的长距离依赖关系  \n可以用于推理加速。在推理过程中，模型可以通过一次预测多个Token来减少计算量，从而提高推理速度。", "page_idx": 69}, {"type": "image", "img_path": "images/f3fc18059d9f9a3c1b67f3b37ce8700dd01b07e935792b86030eac002037f5b2.jpg", "img_caption": ["FP8 混合精度的整体训练思路"], "img_footnote": [], "page_idx": 69}, {"type": "image", "img_path": "images/a6280cd01c223da6006982c4d98f860564f735eb99e6a3bb741398984793eb7f.jpg", "img_caption": ["细粒度量化策略"], "img_footnote": [], "page_idx": 69}, {"type": "text", "text": "拓展分析： DeepSeek-V3", "text_level": 1, "page_idx": 70}, {"type": "text", "text": "通信优化：DulePipe 算法：精细化编排计算和通信", "page_idx": 70}, {"type": "text", "text": "控制前向和反向过程中计算和通信的GPU SM数量，保证计算和通信完全重叠", "page_idx": 70}, {"type": "text", "text": "双向流水线并行", "text_level": 1, "page_idx": 70}, {"type": "text", "text": "降低流水线的Bubble需要存两份模型参数 64路的专家并行", "page_idx": 70}, {"type": "text", "text": "Computation MLP(B)▲ MLP(W)△ MLP(F)△ ATTN(B)▲ ATTN(W)A ATTN(F)△   \nCommunication DISPATCH(F)△ DISPATCH(B) COMBINE(F)△ PP COMBINE(B) Time → △Forward chunk ▲Backwardchunk ", "page_idx": 70}, {"type": "text", "text": "计算和通信重叠", "text_level": 1, "page_idx": 70}, {"type": "image", "img_path": "images/83707561d384630a3549d8d88a4dd9caf9d93d684898d3bb28af301746ef8550.jpg", "img_caption": [], "img_footnote": [], "page_idx": 70}, {"type": "text", "text": "双向流水线并行", "text_level": 1, "page_idx": 70}, {"type": "table", "img_path": "images/77aad14fef8fe34ab6957ebe5de4c6de5420cb594547c52aa79f2b8da3a3bf00.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>Method</td><td>Bubble</td><td>Parameter</td><td>Activation</td></tr><tr><td>1F1B</td><td>(PP-1)(F+B)</td><td>1×</td><td>PP</td></tr><tr><td>ZB1P</td><td>(PP-1)(F+B-2W)</td><td>1×</td><td>PP</td></tr><tr><td>DualPipe (Ours)</td><td>(PP-1)(F&B+B-3W)</td><td>2x</td><td>PP+1</td></tr></table></body></html>", "page_idx": 70}, {"type": "text", "text": "气泡和内存分析", "text_level": 1, "page_idx": 70}, {"type": "text", "text": "拓展分析： System I & System II", "text_level": 1, "page_idx": 71}, {"type": "text", "text": "通过使用 System 1 的快速但可能不完全准确的判断（“fast-but-maybe-wrong” judgment calls），可以帮助System 2 控制组合爆炸问题，并高效地进行复杂推理。", "page_idx": 71}, {"type": "text", "text": "System 2 的问题：需要处理大量组合：System 2 通过遍历组合（如在图或树中的路径搜索）来解决问题，但这种方法的计算复杂度极高，容易导致组合爆炸（combinatorial explosion）。", "page_idx": 71}, {"type": "text", "text": " 核心挑战：当组合的分支因子过大时，System 2 的搜索速度会非常慢，需要更高效的方法来缩减搜索空间。", "page_idx": 71}, {"type": "text", "text": "tem 1 的作用：快速、近似的判断（approximate judgment calls）：", "page_idx": 71}, {"type": "text", "text": "System 1 将离散的数据转化为某种抽象的结构（近似判断的启发式规则），帮助 System 2 减少遍历的复杂度。  \n这使得 System 1 能快速提供一个可能并非完美，但在大多数情况下足够有效的答案，从而加速整体处理。", "page_idx": 71}, {"type": "text", "text": "Keyguantitiesforconceptualizingintelligentsystems ", "page_idx": 71}, {"type": "text", "text": "The two poles of abstraction: type1 vs type 2 ", "page_idx": 71}, {"type": "text", "text": "Static skills: repository of memorized programs ", "page_idx": 71}, {"type": "text", "text": "Fluid intelligence: synthesize new programsonthefly ", "page_idx": 71}, {"type": "text", "text": "Prototype-centric(value-centric)abstraction ", "text_level": 1, "page_idx": 71}, {"type": "text", "text": "Program-centricabstraction ", "text_level": 1, "page_idx": 71}, {"type": "text", "text": "Setofprototypes $^ +$ distancefunction Example:classify facevs.non-face using abstractfeatures   \nAbstractwrtdetailsnotpresentinthe   \nprototypes   \nObtainedbyclusteringconcrete samplesinto   \nprototypes Thisisavalueanalogy!   \nGraphof(usuallydiscrete)operatorswhere   \ninputnodescantakedifferentvalueswithina   \ntype Example:function that sortsa list   \nAbstractwrtinputnodesvalues   \nObtained bymerging specialized functions   \nunderanewabstractsignature Thisisaprogramanalogy! ", "page_idx": 71}, {"type": "text", "text": "", "page_idx": 71}, {"type": "image", "img_path": "images/46c73a8ac2b9d128488426f96f6ae92901ec0ed6f5bbb76707c09e0cc96eb771.jpg", "img_caption": [], "img_footnote": [], "page_idx": 71}, {"type": "text", "text": "Narrowoperationalareaof programsused(lowabstraction）", "page_idx": 71}, {"type": "text", "text": "Broadoperationalarea of programsused (highabstraction) ", "page_idx": 71}, {"type": "text", "text": "Data-hungryprogram acquisition/synthesis ", "page_idx": 71}, {"type": "text", "text": "Information-efficient programacquisition/ synthesis ", "page_idx": 71}, {"type": "image", "img_path": "images/33fdef2fc051bcc9eecb9655ef7bf9c120b13ff9aa4c7872a9d6b0f7dadf1604.jpg", "img_caption": ["It's Not About Scale, It's About Abstraction - <PERSON> "], "img_footnote": [], "page_idx": 71}, {"type": "text", "text": "result=0 ", "page_idx": 71}, {"type": "text", "text": "讨论：慢思考与Abstraction Reasoning（过程性监督 + 细粒度反馈）", "text_level": 1, "page_idx": 72}, {"type": "text", "text": " LLM 仍然受限于过程性推理任务：", "text_level": 1, "page_idx": 72}, {"type": "text", "text": "尽管可以完成复杂的推理，但是仍然受限于一些对于人类来说很简单的任务（例如，逆转诅咒），泛化能力较弱：本质上在于 LLM 的思考范式仍然是静态，非过程性的；一些看似复杂的任务，实则在互联网上有相近的解决办法，在训练语料中有所蕴含（Hypothesis: Depends purely onTask Similarity instead of Task Complexity）；", "page_idx": 72}, {"type": "text", "text": "人类的Abstraction Reasoning: 抽象出高维概念并进行细粒度反馈，压缩即智能：", "page_idx": 72}, {"type": "text", "text": "Type1 Abstraction: 基于大量语料学习普遍规律，但是针对特定的问题，难以给出细粒度反馈和反思；Type2 Abstraction: System II 驱动对于特定的推理复杂问题可以基于抽像出的先验进行深入的细粒度反馈；", "page_idx": 72}, {"type": "text", "text": " Fast-but-maybe-wrong Judgements；", "text_level": 1, "page_idx": 72}, {"type": "image", "img_path": "images/4e7cdaa3817ea225eb5099c867339cd79acbce6d35202338a1483291044fe6ca.jpg", "img_caption": ["Analogy: \"Draw a map "], "img_footnote": [], "page_idx": 72}, {"type": "image", "img_path": "images/bacf19b935a211a89bbc4d0754c00c9ca484e2d6110a3c5f2ef8258819f32248.jpg", "img_caption": [], "img_footnote": [], "page_idx": 72}, {"type": "text", "text": "Embeddiscreteobjects ", "text_level": 1, "page_idx": 72}, {"type": "text", "text": "(e.g.tasks,programs) ", "page_idx": 72}, {"type": "text", "text": "onamanifold ", "page_idx": 72}, {"type": "text", "text": "(adata structurewhere you can compare anyobjectwithadistance function) ", "page_idx": 72}, {"type": "text", "text": "to enablefast, approximate inferencesaboutdirections, distances,pathfinding ", "page_idx": 72}]