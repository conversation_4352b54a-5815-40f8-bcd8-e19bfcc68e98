[{"type": "text", "text": "深度解读DeepSeek： 原理与效应", "text_level": 1, "page_idx": 0}, {"type": "text", "text": "熊德意 天津大学  \n<EMAIL>  \nhttps://dyxiong.github.io  \nhttps://tjunlp-lab.github.io", "page_idx": 0}, {"type": "text", "text": "报告目录", "text_level": 1, "page_idx": 1}, {"type": "text", "text": "01 ", "page_idx": 1}, {"type": "text", "text": "大语言模型发展路线图", "text_level": 1, "page_idx": 1}, {"type": "text", "text": "02 ", "page_idx": 1}, {"type": "text", "text": "DeepSeek V2-V3/R1技术原理", "page_idx": 1}, {"type": "text", "text": "03 ", "page_idx": 1}, {"type": "text", "text": "DeepSeek效应", "page_idx": 1}, {"type": "text", "text": "04 未来展望 ", "text_level": 1, "page_idx": 1}, {"type": "text", "text": "生成式AI：2014 —— 2024", "text_level": 1, "page_idx": 2}, {"type": "text", "text": "生成式AI：使用生成式模型生成各类数据 （语言、语音、图片、视频等）", "page_idx": 2}, {"type": "image", "img_path": "images/117286e18beaf7a0f475137ac2ee9a1bab2ff7da704d1a78e778717b2275768d.jpg", "img_caption": ["Figure1.Our model learnsawords/imagealignment.The visualized attentional maps(3) are explained in Sections 3.1&5.4 "], "img_footnote": [], "page_idx": 2}, {"type": "text", "text": "o Attention：数据依存关系建模  \no Transformer：数据生成的统一架构  \no Scaling Laws：数据学习、生成的扩展法则  \no RLHF：生成与人类价值对齐的数据  \no o1/R1：生成式求解问题— 生成问题求解的过程和答案（推理）", "page_idx": 2}, {"type": "image", "img_path": "images/e2420f9b2752d90177f99690042d082cbe92c3003330a0205b0de7bf8d6bfa22.jpg", "img_caption": [], "img_footnote": [], "page_idx": 2}, {"type": "text", "text": "14x14 Feature Map A bird flying over a body of water 1.Input 2.Convolutional 3.RNN with attention 4.Word by Image Feature Extraction over theimage word generation ", "page_idx": 2}, {"type": "text", "text": "生成式AI：2014 —— 2024", "text_level": 1, "page_idx": 3}, {"type": "image", "img_path": "images/834549a793c7b2b3c73e9867543b62c9bf232f20493f6c4981de4612173cd2eb.jpg", "img_caption": [], "img_footnote": [], "page_idx": 3}, {"type": "text", "text": "生成式AI：使用生成式模型生成各类数据 （语言、语音、图片、视频等）", "page_idx": 3}, {"type": "text", "text": "AttentionIsAll You Need ", "text_level": 1, "page_idx": 3}, {"type": "text", "text": "o Attention：数据依存关系建模Transformer：数据生成的统一架构Scaling Laws：数据学习、生成的扩展法则  \no RLHF：生成与人类价值对齐的数据  \no o1/R1：生成式求解问题 生成复杂问题的答案（推理）", "page_idx": 3}, {"type": "text", "text": "Probabilities 1 Softmax Linea Add&Norm Forward t Add&Norm Add&Norm Multi-Head Feed Forward J Nx Add&Norm Adased Multi-Head Attention Attention T J Posttong ncona Input Output Embedding Embedding ↑ (shifted right) ", "page_idx": 3}, {"type": "text", "text": "<PERSON><PERSON>\\* <PERSON><PERSON>\\* <PERSON><PERSON>\\* <PERSON>\\* Google Brain Google Brain Google Research <NAME_EMAIL> <EMAIL> <EMAIL> <EMAIL> ", "page_idx": 3}, {"type": "text", "text": "<PERSON><PERSON>\\* <NAME_EMAIL> ", "page_idx": 3}, {"type": "text", "text": "<PERSON>\\*† University <NAME_EMAIL> ", "page_idx": 3}, {"type": "text", "text": "<PERSON><PERSON><PERSON>\\* <NAME_EMAIL> ", "page_idx": 3}, {"type": "text", "text": "生成式AI：2014 —— 2024", "text_level": 1, "page_idx": 4}, {"type": "image", "img_path": "images/3914c946a9a869453efc1e1db2bad94069bccde4d1c4a63a978c3c80bccef96e.jpg", "img_caption": [], "img_footnote": [], "page_idx": 4}, {"type": "text", "text": "生成式AI：使用生成式模型生成各类数据 （语言、语音、图片、视频等）", "page_idx": 4}, {"type": "text", "text": "o Attention：数据依存关系建模Transformer：数据生成的统一架构Scaling Laws：数据学习、生成的扩展法则  \no RLHF：生成与人类价值对齐的数据  \no o1/R1：生成式求解问题 生成复杂问题的答案（推理）", "page_idx": 4}, {"type": "image", "img_path": "images/89bba5f660d056afdc019da89f8b5cc17e81bab816c50cd57045a5577bd7f625.jpg", "img_caption": [], "img_footnote": [], "page_idx": 4}, {"type": "text", "text": "生成式AI：2014 —— 2024", "text_level": 1, "page_idx": 5}, {"type": "image", "img_path": "images/b5eebaae6fe42d5666ad85696b0824bcf616201692e96ca13a857dbef3d6f0ab.jpg", "img_caption": [], "img_footnote": [], "page_idx": 5}, {"type": "text", "text": "生成式AI：2014 —— 2024", "text_level": 1, "page_idx": 6}, {"type": "image", "img_path": "images/5d8381139f01ff836dba1931ce56ba4bfe07ac9da10ad5e96f50d8939b773432.jpg", "img_caption": [], "img_footnote": [], "page_idx": 6}, {"type": "text", "text": "生成式AI：使用生成式模型生成各类数据 （语言、语音、图片、视频等）", "page_idx": 6}, {"type": "text", "text": "o Attention：数据依存关系建模Transformer：数据生成的统一架构  \no Scaling Laws：数据学习、生成的扩展法则  \no RLHF：生成与人类价值对齐的数据  \no o1/R1：生成式求解问题 生成复杂问题的答案（推理）", "page_idx": 6}, {"type": "text", "text": "自然语言处理与语言模型", "text_level": 1, "page_idx": 7}, {"type": "image", "img_path": "images/832f6c599f453d6ac8892bec75cdb9d6019a6c648b1ff72919c94eb2349ec943.jpg", "img_caption": [], "img_footnote": [], "page_idx": 7}, {"type": "text", "text": "Language Models ", "text_level": 1, "page_idx": 7}, {"type": "text", "text": "Trained to predict the next word in a sentence: dog   \nThe cat is chasing the boy $5 \\%$   \nmouse $70 \\%$   \nsquirrel $20 \\%$ $5 \\%$   \nhouse $0 \\%$ ", "page_idx": 7}, {"type": "text", "text": "", "page_idx": 7}, {"type": "text", "text": "自然语言处理：人类语言的智能化处理与分析，使计算机具备听、说、读、写、译等人所具备的语言能力语言模型：自然语言统计建模，简单说，就是预测句子中的下一个单词是什么", "page_idx": 7}, {"type": "text", "text": "", "page_idx": 7}, {"type": "text", "text": "大语言模型： 2018 —— 2024", "text_level": 1, "page_idx": 8}, {"type": "text", "text": "T5 G GShard Publicly Available 2019 2020 2021 G mT5 PaG-a rese Y YuLan-Chat   \nGPT-3 Codex 5 5-8 AI CPM-2 小 StarCoder G FLAN CodeGen2 TO G LaMDA Anthropic AI HyperCLOVA NAVER inspur Yuan 1.0 6 AlphaCode ChatGLM WebGPT T) Falcon   \nErnie 3.0 Titan InstructGPT S 2022 O Chinchilla G PaLM2 Gopher CodeGen 1-3 G UL2 ： Sparrow Pythia InternLM E2 Qwen2 GLaM G MT-NLG OPT O G PaLM G Flan-T5 LM-sys Vicuna E2Qwen DeepSeek-V2 CodeGeeX 福 GPT-NeoX-20B Y YaLM G Flan-PaLM PanGu-∑ Mistral O LLaMA3 BLOOM GLM Tk-Instruct Ai2 小 Luminous GE Bard F Deepseek M MiniCPM mTO AlexaTM a Cohere 8 NLLB O LLaMA Mixtral G Gemma   \nBLOOMZ 8 WeLM 2023 2024- 1-6→ OPT-IML 0O ChatGPT GPT-4 O LLaMA2 ", "page_idx": 8}, {"type": "text", "text": "大语言模型：技术栈", "text_level": 1, "page_idx": 9}, {"type": "text", "text": "p li应ca用tio层n 自 工具调用 员 主规划 信息检索 智能客服 图文创作 代码生成通用模型 行业模型行业模型 行业数据 行业对齐数据  \nSp ecialized M o d el □ 1 领域微调训练 一 → 领域对齐训练 □ 行业模型部署 ↓ 行业模型评测.···········.· 模型训练 ·.······· 模型部署 … …….…… 模型评测 …. …..通用模型 预训练 对齐训练 SFT RLH F D PO Best o f N  sam p lin g 动态批处理 算子优化 OpenEval UltraEval  \nGen eral-p u rp o se D at a Par allel Ten so r Parallel Exp ert Parallel ZeRO 模型量化 模型蒸馏 OpenCompassXChatbot ArenaM o d elPip elin e Parallel Seq u en ce Parallel Flash Atten tio n 模型剪枝 性能监控 FlagEvalOpenLLMLeaderboard…....…...…… . \\*\\*\\*\\*\\*……. 预训练数据 . 对齐训练数据 评测数据 .  \n数D据ata处P理ro c和es管sin理g 数据分类 处数据理去流重程 Pro m p t 国 知识能力 安全可信语言检测 质量筛选  \nan d M an ag em en t 网页 书籍 代码 论文 百科 内容过滤 领域分类 版本控制 生 Resp o n ses $\\textcircled { A } > \\textcircled { C } > \\textcircled { B } > \\textcircled { D }$ 价值对齐 专业领域··· ……硬件 软件M aCno算amg力peum管tien理gt nVIDIA. H 1 0 0 AM M I3 5 0 Ascend 9 1 0 B siur docker kubenetes 资任源务分调配度 负性载能均监衡控 弹容性错扩机展制W ", "page_idx": 9}, {"type": "text", "text": "大语言模型： 生命周期与范式", "text_level": 1, "page_idx": 10}, {"type": "text", "text": "数据处理 预训练 后训练 应用部署数据治理 基座模型 对齐模型 红队测试数据要素 自监督学习 微调&强化 商业落地知识源头 能力涌现 安全可信 模型压缩训练范式 关键• 预训练 —— 基座模型 模型架构后训练 —— 对齐模型 训练算法推理训练 —— 推理模型 扩展法则杀手锏：性能/成本 曲线 | 性价比", "page_idx": 10}, {"type": "text", "text": "", "page_idx": 10}, {"type": "text", "text": "扩展法则 ", "text_level": 1, "page_idx": 11}, {"type": "text", "text": "The Bitter Lesson ", "text_level": 1, "page_idx": 11}, {"type": "text", "text": "[Sutton, 2019] ", "page_idx": 11}, {"type": "image", "img_path": "images/d27ceb958a950632783f8cba17ca60ada875e7395f3f6e0f5b6f3ac7046e07a0.jpg", "img_caption": [], "img_footnote": [], "page_idx": 11}, {"type": "text", "text": "The bitter lesson is based on the historical observations that 1) Al researchers have often tried to build knowledge into their agents, 2) this always helps in the short term,and is personally satisfying to the researcher， but 3) in the long run it plateaus and even inhibits further progress,and 4) breakthrough progress eventually arrives by an opposing approach based on scaling computation by search and learning. ", "page_idx": 11}, {"type": "text", "text": "大语言模型： 后训练范式", "text_level": 1, "page_idx": 12}, {"type": "image", "img_path": "images/02f260b25e834d408a671fe079ada2dc7703b32969da7de3fffce00c426032d9.jpg", "img_caption": [], "img_footnote": [], "page_idx": 12}, {"type": "text", "text": "推理语言模型？", "text_level": 1, "page_idx": 13}, {"type": "image", "img_path": "images/8029325af3461ce8397d8318083ef0d24ca80151f3581a6328e499decb9a303b.jpg", "img_caption": [], "img_footnote": [], "page_idx": 13}, {"type": "text", "text": "Reminder: AlphaZero ", "text_level": 1, "page_idx": 13}, {"type": "image", "img_path": "images/3667772a60cfeb2f48fef6b8848083243f8ebbbf149097a4276d3fac4143ded1.jpg", "img_caption": [], "img_footnote": [], "page_idx": 13}, {"type": "text", "text": "b Neural network training ", "page_idx": 13}, {"type": "image", "img_path": "images/050e7a2762e9a769187b68e356c830dfdd7e6a3b7f4cc1d3cdfc12f87cbeb8fa.jpg", "img_caption": [], "img_footnote": [], "page_idx": 13}, {"type": "image", "img_path": "images/b94358bd6df65b592200d13a64d32c1ff3218bd395e55237caaa27cd728c33ca.jpg", "img_caption": [], "img_footnote": [], "page_idx": 13}, {"type": "image", "img_path": "images/8024d0f53e37f8c7d6569a44c4ad7f06d2186dae0995d7e6ac6df601a9beea28.jpg", "img_caption": ["MCTS "], "img_footnote": [], "page_idx": 13}, {"type": "text", "text": "过程奖励模型PRM ", "text_level": 1, "page_idx": 13}, {"type": "text", "text": "报告目录", "text_level": 1, "page_idx": 14}, {"type": "text", "text": "01 ", "page_idx": 14}, {"type": "text", "text": "大语言模型发展路线图", "text_level": 1, "page_idx": 14}, {"type": "text", "text": "02 ", "page_idx": 14}, {"type": "text", "text": "DeepSeek V2-V3/R1技术原理", "page_idx": 14}, {"type": "text", "text": "03 ", "page_idx": 14}, {"type": "text", "text": "DeepSeek效应", "page_idx": 14}, {"type": "text", "text": "04 未来展望 ", "text_level": 1, "page_idx": 14}, {"type": "text", "text": "Q ", "page_idx": 15}, {"type": "text", "text": "deepseek ", "text_level": 1, "page_idx": 15}, {"type": "image", "img_path": "images/cb21566ace5a5d57f202b937b3b08053fe8bd2c8b388db8ae7c8e1ac81fa4548.jpg", "img_caption": [], "img_footnote": [], "page_idx": 15}, {"type": "image", "img_path": "images/bfed12b0d644f9d8db1af67585971f8b819cbd664cfa37bc45495989156ca2f2.jpg", "img_caption": [], "img_footnote": [], "page_idx": 15}, {"type": "text", "text": "天边的两多云 （国内外现状）", "text_level": 1, "page_idx": 15}, {"type": "text", "text": "模型架构：大部分企业采用已验证架构（试错成本高昂） 【不敢】推理模型：大部分实验室仍在苦苦猜测摸索Q\\*/o1（OpenAI保密）", "page_idx": 15}, {"type": "text", "text": "DeepSeek： 技术创新——模型架构 | V2", "text_level": 1, "page_idx": 16}, {"type": "text", "text": "DeepSeek V2主要创新", "text_level": 1, "page_idx": 16}, {"type": "text", "text": "DeepSeekMoE MLA ", "page_idx": 16}, {"type": "text", "text": "DeepSeekMoE ", "text_level": 1, "page_idx": 16}, {"type": "text", "text": "稀疏激活：计算不随规模呈线性增长相比传统MoE：细粒度专家（共享 $+$ 路由&通信改造：", "page_idx": 16}, {"type": "text", "text": "Device-Limited Routing Auxiliary Loss for Load Balance Token-Dropping Strategy ", "page_idx": 16}, {"type": "image", "img_path": "images/ad64a739ed83683099198ea69006b125e96f3fbc49e6ed8e8f0d4a2228f34719.jpg", "img_caption": [], "img_footnote": [], "page_idx": 16}, {"type": "text", "text": "MLA：低秩压缩，降低KV cache占用空间", "page_idx": 16}, {"type": "image", "img_path": "images/b45be689e4ca63459319352fb9e58004846469f035ecedba18b518e7ca01b6ca.jpg", "img_caption": [], "img_footnote": [], "page_idx": 16}, {"type": "text", "text": "V2规模：236B total parameters, 21B activated parameters, 128K context window ", "page_idx": 16}, {"type": "text", "text": "DeepSeek： 技术创新——模型架构 | V2", "text_level": 1, "page_idx": 17}, {"type": "text", "text": "Training Costs (K GPU Hours/T Tokens) 80 DeepSeek-V2 LLaMA 370B DeepSeek 67B75 ★ D DeepSeek-V2 saving 42.5% f 训练开销 . DeepSeek 67B 。 0 50 100 150 200 250 300 Qwen1.5 32B . Grok-1 KV Cache for Generation (KB/Token) 70 Mixtral 8x7B . Command R LLaMA 2 70B DeepSeek 67B . 存储开销 65 LLaMA38B reducing KV cache by 93.3% O LLaMA1 65B DeepSeek-V2 ● LLaMA 2 34B Mistral 7B LLaMA 1 Family 0 100 200 300 400 60 LLaMA 2 Family Maximum Generation Throughput (Tokens/Sec) . Mixtral Family 55 LLaMA L13MA 133B Comm.5d amiamily DeepSeek 67B 生成速度 576% of maximum throughput 0 20 40 60 80 100 DeepSeek-V2 Activated Parameters (Billions) 0 1000020000 30000 40000 50000 (a) (b) ", "page_idx": 17}, {"type": "image", "img_path": "", "img_caption": ["杀手锏：性能/成本 曲线 | 性价比"], "img_footnote": [], "page_idx": 17}, {"type": "text", "text": "DeepSeek： 技术创新——模型架构 | V3", "text_level": 1, "page_idx": 18}, {"type": "text", "text": "DeepSeek V3主要创新", "text_level": 1, "page_idx": 18}, {"type": "text", "text": "Infrastructures Multi-Token Prediction (MTP) ", "page_idx": 18}, {"type": "text", "text": "Infrastructures ", "text_level": 1, "page_idx": 18}, {"type": "text", "text": "减少流水线气泡  \n高效节点间All-to-All通信  \nFP8训练  \n低精度存储与通信", "page_idx": 18}, {"type": "text", "text": "MTP：一次预测多个topken", "text_level": 1, "page_idx": 18}, {"type": "image", "img_path": "images/ab253dd226282fd7f462562e39c3ef1112917d34f43904f403b453b181425ddc.jpg", "img_caption": [], "img_footnote": [], "page_idx": 18}, {"type": "image", "img_path": "images/f68a3b730631bb3ad78c5db4571da3cf071f78e3b70ff2536d462c693a371b03.jpg", "img_caption": [], "img_footnote": [], "page_idx": 18}, {"type": "text", "text": "V3规模：671B total parameters, 37B activated parameters, trained on 14.8T tokens ", "page_idx": 18}, {"type": "text", "text": "DeepSeek： 技术创新——模型架构 | V3", "text_level": 1, "page_idx": 19}, {"type": "image", "img_path": "images/04f1538a162b72efe5d554ad68c57fac0d2e6006e5459dd8bd95077fcb11e87a.jpg", "img_caption": ["杀手锏：性能/成本 曲线 | 性价比"], "img_footnote": [], "page_idx": 19}, {"type": "text", "text": "DeepSeek： 技术创新— 模型架构 | V3成本", "text_level": 1, "page_idx": 20}, {"type": "table", "img_path": "images/e146037724c37d63a1c34240fed3d341818bfc18dbf1f87b22b86f129c911ca0.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>Training Costs</td><td>Pre-Training</td><td>Context Extension</td><td>Post-Training</td><td>Total</td></tr><tr><td>in H800 GPU Hours</td><td>2664K</td><td>119K</td><td>5K</td><td>2788K</td></tr><tr><td> in USD</td><td>$5.328M</td><td>$0.238M</td><td>$0.01M</td><td>$5.576M</td></tr></table></body></html>", "page_idx": 20}, {"type": "text", "text": "Table 1| Training costs of DeepSeek-V3, assuming the rental price of H800 is $\\$ 2$ per GPU hour. ", "page_idx": 20}, {"type": "text", "text": "During the pre-training state, training DeepSeek-V3 on each trillion tokens requires only 180K H800 GPU hours, i.e., 3.7 days on our own cluster with 2048 H800 GPUs. Consequently, our pre-training stage is completed in less than two months and costs 2664K GPU hours. 大规模高性能加 ", "page_idx": 20}, {"type": "text", "text": "（折旧） Sebastian Raschka 大模型研发人员成本 大模型研发成本 > E.g.Llama 3 405B used 30.8M GPU-hours, while DeepSeek-V3 looks 大模型架构技术探索成本 to be a stronger model at only 2.8M GPU-hours (\\~11X less compute). 成本 大模型数据成本 大模型最终训练成本 Super interesting! And DeepSeek was trained in H8oO's which are probably also a tad (or noticeably?) slower than Meta's H100's. 大模型部署推理成本 ", "page_idx": 20}, {"type": "image", "img_path": "", "img_caption": ["杀手锏：性能/成本 曲线 | 性价比"], "img_footnote": [], "page_idx": 20}, {"type": "text", "text": "DeepSeek： 技术创新——创新程度", "text_level": 1, "page_idx": 21}, {"type": "text", "text": "DeepSeek V2-V3及R1在模型架构上选择稀疏MoE模型而非稠密模型，并进行和积累了大量技术创新，包括MLA、FP8训练、MoE All-to-All通信瓶颈解决、MTP等，这些技术并不是所有都是原始创新，但是能够进行如此多大模型架构底层创新的实验室，在全世界可能也只有少数几个；", "page_idx": 21}, {"type": "text", "text": "DeepSeek所有模型架构上的创新均是围绕“降本增效”：在基本不损害性能前提下，尽可能通过算法挖掘和提升硬件训练和解码效率", "page_idx": 21}, {"type": "text", "text": "美国采取芯片禁令（全球三级管控）策略维持自己的AI领导地位，DeepSeek算法绕过了美国的算力护城河", "page_idx": 21}, {"type": "text", "text": "DeepSeek： 技术创新——推理模型 | R1", "text_level": 1, "page_idx": 22}, {"type": "text", "text": "DeepSeek R1主要创新", "text_level": 1, "page_idx": 22}, {"type": "text", "text": "o DeepSeek-R1-Zero：大规模RL训练，发现了RL训练的Scaling Laws，RL训练涌现“aha”时刻o 推理模型训练技术框架：4步法，有效解决了R1-Zero存在问题，将推理与对齐合为一体o 强化学习训练框架：GRPO，来自DeepSeekMath，降低了强化学习训练成本o 推理模型蒸馏：将大模型推理能力蒸馏到小模型，优于小模型直接进行推理训练（规模效应）", "page_idx": 22}, {"type": "text", "text": "为什么MCTS+PRM是“误区”", "page_idx": 22}, {"type": "text", "text": "The bitter lesson: scalability OpenAI竞争策略 ", "page_idx": 22}, {"type": "text", "text": "DeepSeek： 技术创新— 推理模型 | R1-Zero", "text_level": 1, "page_idx": 23}, {"type": "text", "text": "Large-scale Reasoning-Oriented Reinforcement Learning ", "text_level": 1, "page_idx": 23}, {"type": "image", "img_path": "images/c97e134a315f0f89e179729e388cee7a08f8b4383c65788521b4f59fdd3c1de4.jpg", "img_caption": [], "img_footnote": [], "page_idx": 23}, {"type": "text", "text": "1. 强化学习训练规模大", "text_level": 1, "page_idx": 23}, {"type": "text", "text": "业内通常训练几十RL steps，DeepSeek训练几千RL stepsTülu 3 最大发布模型只训练了\\~50 RL steps", "page_idx": 23}, {"type": "text", "text": "2. RL Training Scaling Law：涌现reflection、aha   \n自动涌现出搜索、反思、顿悟、纠错   \n与testing-time scaling law一致，可从性能增长曲线和长   \n度增长曲线推出推理时scaling law ", "page_idx": 23}, {"type": "image", "img_path": "images/f72e007498c5d7c5a820537054196c11e6e27d3271e24d6983e77fdd6da230ed.jpg", "img_caption": ["Figure2|AIMEaccuracyofDeepSeek-R1-Zeroduringtraining.Foreachquestion,wesample 16responsesandcalculatetheoverallaverageaccuracytoensureastableevaluation. "], "img_footnote": [], "page_idx": 23}, {"type": "text", "text": "3. 通过prompt策略引导模型思考和给出答案，避免基座模型不能生成停止符", "text_level": 1, "page_idx": 23}, {"type": "text", "text": "使用标记<think></think><answer></answer>", "page_idx": 23}, {"type": "text", "text": "R1-Zero存在问题：poor readability, language mixing ", "page_idx": 23}, {"type": "image", "img_path": "", "img_caption": ["Figure3｜TheaverageresponselengthofDeepSeek-R1-ZeroonthetrainingsetduringtheRL process.DeepSeek-R1-Zeronaturallylearnstosolvereasoningtaskswithmorethinkingtime. "], "img_footnote": [], "page_idx": 23}, {"type": "text", "text": "A conversation between User and Assstant. The user asks a question,and the Assistant solves it. The assistant first thinks about the reasoning process in the mind and then provides the user with the answer. The reasoning process and answer are enclosed within <think> </think> and <answer> </answer> tags, respectively, i.e.,<think> reasoning process here $< /$ /think> <answer> answer here $\\angle \\cdot \\angle$ /answer>. User: prompt. Assistant: ", "page_idx": 23}, {"type": "text", "text": "Table 1| Template for DeepSeek-R1-Zero. prompt willbe replaced with the specific reasoning question during training. ", "page_idx": 23}, {"type": "text", "text": "DeepSeek： 技术创新——推理模型 | R1 Recipe", "text_level": 1, "page_idx": 24}, {"type": "text", "text": "Step 1. Reasoning SFT Cold Start ", "page_idx": 24}, {"type": "text", "text": "Step 2. Reasoning-oriented RL   \n类似训练R1-Zero   \n直至训练收敛 ", "page_idx": 24}, {"type": "text", "text": "Step 3 Reasoning DataMath, Code, Logic（600K samples）", "text_level": 1, "page_idx": 24}, {"type": "image", "img_path": "images/ef9280271b5c78052af6e64d4ff128820c0cb65883f1a083d66be0ba59d06df5.jpg", "img_caption": ["Step 0. Generating Long CoT data Few-shot ICL + 人工后期refining "], "img_footnote": [], "page_idx": 24}, {"type": "text", "text": "Step 4. General RL Reasoning RL with rule-based rewards RLHF Preference Tuning with safety rewards ", "page_idx": 24}, {"type": "text", "text": "o DeepSeek-R1 不是唯一的推理模型框架，2025年将出现更多新的框架o 要复现上述框架，需要DeepSeek开源相关数据", "page_idx": 24}, {"type": "text", "text": "DeepSeek： 技术创新——推理模型 | RL", "text_level": 1, "page_idx": 25}, {"type": "text", "text": "1. 强化学习框架GRPO （DeepSeekMath）", "text_level": 1, "page_idx": 25}, {"type": "text", "text": "采用蒙特卡洛采用估算以取代Value模型，降低计算和存储开销", "page_idx": 25}, {"type": "image", "img_path": "images/371d8795cba2bf1b9444bb2a7bffa8d11f724736b0ee0b4f3a17860a66ae2b1e.jpg", "img_caption": ["Figure 4 | Demonstration of PPO and our GRPO. GRPO foregoes the value model, instead estimating the baseline from group scores, significantly reducing training resources. "], "img_footnote": [], "page_idx": 25}, {"type": "text", "text": "2. 强化学习奖励模型", "text_level": 1, "page_idx": 25}, {"type": "text", "text": "采用easily verifiable rewards ", "page_idx": 25}, {"type": "text", "text": "Accuracy reward   \nFormat reward   \nLanguage-consistency reward ", "page_idx": 25}, {"type": "text", "text": "o 避免过程奖励模型：计算复杂，容易reward hacking ", "page_idx": 25}, {"type": "text", "text": "DeepSeek： 技术创新——推理模型 | 推理能力蒸馏", "text_level": 1, "page_idx": 26}, {"type": "image", "img_path": "images/cb1e907874b8012020d7d3db15ebc81606115b54aa46a81dad42fab72444065d.jpg", "img_caption": [], "img_footnote": [], "page_idx": 26}, {"type": "text", "text": "推理模型蒸馏到小模型", "text_level": 1, "page_idx": 26}, {"type": "text", "text": "o reasoning能力可以蒸馏到小模型大模型蒸馏到小模型优于小模型直接通过大规模RL训练  \no 再次验证了模型规模在AGI发展中的重要性  \no 推理者同样需要规模支撑", "page_idx": 26}, {"type": "text", "text": "DeepSeek： 技术创新——推理模型 | R1", "text_level": 1, "page_idx": 27}, {"type": "image", "img_path": "images/d46bf28ac02963577be3bd91841503ae741b925c8a52d7cb68186ae31fa392a1.jpg", "img_caption": [], "img_footnote": ["More charts: genuineimpact.substack.com "], "page_idx": 27}, {"type": "text", "text": "Pricing: Input and Output Prices ", "text_level": 1, "page_idx": 27}, {"type": "image", "img_path": "images/1a57ad418d71286755d0accdd9a72dc5d9ee09c2d051924992c87698035a0a03.jpg", "img_caption": [], "img_footnote": [], "page_idx": 27}, {"type": "text", "text": "杀手锏：性能/成本 曲线 | 性价比", "text_level": 1, "page_idx": 27}, {"type": "text", "text": "DeepSeek： 技术创新——推理模型 | R1", "text_level": 1, "page_idx": 28}, {"type": "table", "img_path": "images/917731f8897df028f20f5e4b07cbea39d28bc10a9cc9975ecfb30823d2fd8656.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>Models</td><td>Logical</td><td>Level 1</td><td>Level 2</td><td>Level 3</td><td>Open Source ?</td><td>Model Size</td></tr><tr><td>DeepSeek-R1 (API)</td><td>76.10%</td><td>90.48%</td><td>77.14%</td><td>61.70%</td><td>Yes</td><td>671B</td></tr><tr><td>DeepSeek-R1 (网页)</td><td>74.84%</td><td>80.95%</td><td>78.57%</td><td>63.83%</td><td>Yes</td><td>671B</td></tr><tr><td>o1-preview</td><td>72.33%</td><td>88.10%</td><td>74.29%</td><td>55.32%</td><td>No</td><td>undisclosed</td></tr><tr><td>DeepSeek-R1 (非官方API-together)</td><td>70.44%</td><td>80.95%</td><td>78.57%</td><td>48.94%</td><td>Yes</td><td>671B</td></tr><tr><td>QwQ-32B</td><td>63.52%</td><td>73.81%</td><td>70.00%</td><td>44.68%</td><td>Yes</td><td>32B</td></tr><tr><td>hunyuan-turbo-latest</td><td>62.26%</td><td>85.71%</td><td>65.71%</td><td>36.17%</td><td>No</td><td>undisclosed</td></tr><tr><td>GLM-Zero-preview</td><td>61.64%</td><td>71.43%</td><td>71.43%</td><td>38.30%</td><td>No</td><td>undisclosed</td></tr><tr><td>Doubao-pro-32k</td><td>61.01%</td><td>83.33%</td><td>62.86%</td><td>38.30%</td><td>No</td><td>undisclosed</td></tr><tr><td>Yi-Lightning</td><td>52.83%</td><td>64.29%</td><td>60.00%</td><td>31.91%</td><td>No</td><td>undisclosed</td></tr><tr><td>DeepSeek-V2.5-1210</td><td>49.69%</td><td>69.05%</td><td>57.14%</td><td>21.28%</td><td>Yes</td><td>undisclosed</td></tr><tr><td>Ernie-4.0-Turbo-8k</td><td>49.06%</td><td>66.67%</td><td>54.29%</td><td>25.53%</td><td>No</td><td>undisclosed</td></tr><tr><td>DeepSeek-V3</td><td>49.06%</td><td>66.67%</td><td>52.86%</td><td>27.66%</td><td>Yes</td><td>671B</td></tr><tr><td>SenseChat-5-1202</td><td>47.17%</td><td>64.29%</td><td>50.00%</td><td>27.66%</td><td>No</td><td>undisclosed</td></tr><tr><td>GPT-4-Turbo</td><td>42.77%</td><td>57.14%</td><td>48.57%</td><td>21.28%</td><td>No</td><td>undisclosed</td></tr><tr><td>Spark4.0 Ultra</td><td>39.62%</td><td>57.14%</td><td>44.29%</td><td>17.02%</td><td>No</td><td>undisclosed</td></tr><tr><td>Moonshot-v1-32k</td><td>38.99%</td><td>45.24%</td><td>48.57%</td><td>19.15%</td><td>No</td><td>undisclosed</td></tr><tr><td>GPT-3.5-Turbo</td><td>29.56%</td><td>35.71%</td><td>35.71%</td><td>14.89%</td><td>No</td><td>undisclosed</td></tr></table></body></html>", "page_idx": 28}, {"type": "table", "img_path": "images/00e828174f42b2747488eff3505a3e45e628a2a28d428c6618943c8157d7d502.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td colspan=\"4\">DeepSeek-R1 (网页) 平均思考时间</td></tr><tr><td>Average Times(s)</td><td>All</td><td>Correct</td><td>Wrong</td></tr><tr><td>Overall</td><td>147.26</td><td>100.69</td><td>285.83</td></tr><tr><td>Level 1</td><td>83.57</td><td>63.88</td><td>167.25</td></tr><tr><td>Level 2</td><td>132.49</td><td>91.98</td><td>281.00</td></tr><tr><td>Level 3</td><td>226.19</td><td>158.37</td><td>345.88</td></tr></table></body></html>", "page_idx": 28}, {"type": "text", "text": "TJUNLP实测DeepSeek-R1逻辑推理性能", "text_level": 1, "page_idx": 28}, {"type": "text", "text": "DeepSeek： 技术创新——创新程度", "text_level": 1, "page_idx": 29}, {"type": "text", "text": "DeepSeek R1是在探明方向（OpenAI o1引领和证实的方向）上进行0-1的创新突破，独立探索出基于大规模强化学习的大语言模型推理技术路线，避开了过去一年多（自OpenAI的Q\\*在社交媒体讨论）业内广泛思索的通过在训练中进行显式搜索、过程奖励模型（即Search+PRM）实现推理的“误区”；", "page_idx": 29}, {"type": "text", "text": "贡献：", "page_idx": 29}, {"type": "text", "text": "独立探索出推理技术路线  \n将技术路线公开发布 （解惑了业内的 “不知”）  \n模型开源 （MIT License）", "page_idx": 29}, {"type": "text", "text": "DeepSeek R1打破了美国第一梯队企业以闭源形成的技术护城河，进一步动摇了美国的“AI Dominance”", "page_idx": 29}, {"type": "text", "text": "报告目录", "text_level": 1, "page_idx": 30}, {"type": "text", "text": "01 ", "page_idx": 30}, {"type": "text", "text": "大语言模型发展路线图", "text_level": 1, "page_idx": 30}, {"type": "text", "text": "02 ", "page_idx": 30}, {"type": "text", "text": "DeepSeek V2-V3/R1技术原理", "page_idx": 30}, {"type": "text", "text": "03 ", "page_idx": 30}, {"type": "text", "text": "DeepSeek效应", "page_idx": 30}, {"type": "text", "text": "04 未来展望 ", "text_level": 1, "page_idx": 30}, {"type": "text", "text": "DeepSeek：效应", "text_level": 1, "page_idx": 31}, {"type": "text", "text": "H ", "page_idx": 31}, {"type": "text", "text": "unusual_whales @unusual_whales·Jan 28 BREAKING:This is nota memecoin. ", "page_idx": 31}, {"type": "text", "text": "This is Nvidia, \\$NVDA, the most valuable company in the world before today. ", "page_idx": 31}, {"type": "text", "text": "It is down 17%. ", "page_idx": 31}, {"type": "text", "text": "It lost \\$56O billion in market cap today so far, the largest in market history. ", "page_idx": 31}, {"type": "image", "img_path": "images/22177d263cbc5dd115c750bfa6de68ae5051f554dcd181d327d0db879e9fc057.jpg", "img_caption": [], "img_footnote": [], "page_idx": 31}, {"type": "text", "text": "Overnight, Microsoft,NViDlA,and Amazon all connected to DeepSeek! <PERSON>:Al in China is on the rise. ", "text_level": 1, "page_idx": 31}, {"type": "text", "text": "", "page_idx": 31}, {"type": "text", "text": "US US US ", "page_idx": 31}, {"type": "text", "text": "Microsoft,NViDlA,andAmazonembraceDeepSeekR1,alongwithUSACloud Computingplatforms.AndrewNgandtheformerCEOof Intel praise DeepSeek's innovativecapabilities. ", "page_idx": 31}, {"type": "text", "text": "Onthelastdayof January，the enthusiasm fromDeepSeek showsno signsof waning. ", "page_idx": 31}, {"type": "text", "text": "Apps ", "page_idx": 31}, {"type": "image", "img_path": "images/d0bd58e40f8e53659ba19776fc3662d0cc0fd7c3432427285bc2ffdcc6d190a0.jpg", "img_caption": [], "img_footnote": [], "page_idx": 31}, {"type": "image", "img_path": "images/3c1bbd25ffb7fb905883bbb265ea026837ec76feea1f29d3dd8f15ef3aea122f.jpg", "img_caption": [], "img_footnote": [], "page_idx": 31}, {"type": "text", "text": "2 ChatGPT ", "page_idx": 31}, {"type": "text", "text": "Free Apps ", "page_idx": 31}, {"type": "text", "text": "Top Charts ", "text_level": 1, "page_idx": 31}, {"type": "text", "text": "1DeepSeekAlAssistant ", "page_idx": 31}, {"type": "image", "img_path": "images/a6d998050b94ae3776820046815858e750b9a4d25590f0a83c650a40e1ccaf17.jpg", "img_caption": [], "img_footnote": [], "page_idx": 31}, {"type": "text", "text": "3Threads ", "text_level": 1, "page_idx": 31}, {"type": "text", "text": "", "page_idx": 31}, {"type": "text", "text": "<PERSON><PERSON> ", "page_idx": 31}, {"type": "text", "text": "All Apps ", "text_level": 1, "page_idx": 31}, {"type": "image", "img_path": "images/8f29e5f800a4ab59682ddd9d8f7dab7b72d2f1ca874f9225cc757cd9f0cb0e82.jpg", "img_caption": [], "img_footnote": [], "page_idx": 31}, {"type": "text", "text": "Open ", "page_idx": 31}, {"type": "text", "text": "£ ", "page_idx": 31}, {"type": "text", "text": "算力价格战", "text_level": 1, "page_idx": 31}, {"type": "text", "text": "开源 vs 闭源", "text_level": 1, "page_idx": 31}, {"type": "text", "text": "认知误区", "text_level": 1, "page_idx": 31}, {"type": "text", "text": "创新&人才&Vision ", "text_level": 1, "page_idx": 31}, {"type": "text", "text": "DeepSeek： 效应——算力价格战", "text_level": 1, "page_idx": 32}, {"type": "image", "img_path": "images/ba927b7c26b81fa6eb905abbd187a8778f975a824f8e4c7e831ad6cbd7c3247b.jpg", "img_caption": [], "img_footnote": [], "page_idx": 32}, {"type": "text", "text": "DeepSeek： 效应——开源 vs 闭源", "text_level": 1, "page_idx": 33}, {"type": "text", "text": "GPT-3选择闭源之后，大模型开源 vs 闭源之争、之战一直存在", "page_idx": 33}, {"type": "text", "text": "DeepSeek R1的开源发布，一举赶超闭源大模型，是大模型开源史上的里程碑", "page_idx": 33}, {"type": "text", "text": "美国AI第一梯队企业的前沿技术封闭被打破", "text_level": 1, "page_idx": 33}, {"type": "text", "text": "开源 vs 闭源不仅涉及技术的公开性，也关乎AI安全治理", "text_level": 1, "page_idx": 33}, {"type": "image", "img_path": "images/6e77b67f56ca1a71f822e79f2580a790d1e4bd64f33fe8cbcf0ed1a68d9c72c0.jpg", "img_caption": [], "img_footnote": [], "page_idx": 33}, {"type": "text", "text": "lolzinventor ·5d ago ", "text_level": 1, "page_idx": 33}, {"type": "text", "text": "Would you consider releasing some model weights,and publishing some research? ", "page_idx": 33}, {"type": "image", "img_path": "images/e1d939418b4d554b96c9a8084a709dfd1e773587f98995190eb9d7800ea0cb5b.jpg", "img_caption": [], "img_footnote": [], "page_idx": 33}, {"type": "text", "text": "$\\oslash$ 164 $3$ $Q$ Award $\\Rightarrow$ Share .. ", "page_idx": 33}, {"type": "text", "text": "samaltman CO-HOsT · 4d ago", "text_level": 1, "page_idx": 33}, {"type": "text", "text": "OpenAl CEO <PERSON> | Verified ", "text_level": 1, "page_idx": 33}, {"type": "text", "text": "yes, we are discussing.i<PERSON><PERSON> think we have been on the wrong side of history here and need to figure out a different open source strategy; not everyone at openai shares this view, and it's also not our current highest priority. ", "page_idx": 33}, {"type": "text", "text": "m 2 Share ", "page_idx": 33}, {"type": "text", "text": "7. License ", "text_level": 1, "page_idx": 33}, {"type": "text", "text": "This code repository and the model weights are licensed under the MIT License.DeepSeek-R1 series support commercial use,alow forany modifications and derivative works,including,but notlimited to,distilation for training other LLMs.Please note that: ", "page_idx": 33}, {"type": "text", "text": "·DeepSeek-R1-Distill-Qwen-1.5B,DeepSeek-R1-Distill-Qwen-7B,DeepSeek-R1-Distill-Qwen-14B and DeepSeek-R1-Distill-Qwen-32B are derived from Qwen-2.5 series, which are originall licensed under Apache 2.0 License,and now finetuned with 80ok samples curated with DeepSeek-R1.   \n· DeepSeek-R1-Distil-Llama-8B is derived from Llama3.1-8B-Base and is originallylicensed under llama3.1 license.   \n· DeepSeek-R1-Distill-Llama-70B is derived from Llama3.3-7OB-Instruct and is originallylicensed under Ilama3.3 license. ", "page_idx": 33}, {"type": "text", "text": "DeepSeek：效应——认知误区", "text_level": 1, "page_idx": 34}, {"type": "text", "text": "如果ChatGPT刷新了我们对AI的认知，那么DeepSeek在某种程度上颠覆了：", "page_idx": 34}, {"type": "text", "text": "o 美国人对中国AI水平的认知：长久以来，美国认为中国在AI科技创新上更多是跟随者角色o 大模型研发成本的认知：大模型研发成本需要数千万乃至上亿美元", "page_idx": 34}, {"type": "text", "text": "DeepSeek： 效应— 创新&人才&Vision ", "text_level": 1, "page_idx": 35}, {"type": "text", "text": "14.中国版的Sora模型何时到来，可以看中国版的ChatGPT何时到来。过去一年，国内大语言模型发展迅速，甚至出现了百模大战的热闹景象，但“热闹”较多的是同质化竞争，较少的是底层基础技术的原创性突破。", "page_idx": 35}, {"type": "text", "text": "为巩固并提升我国在这一领域的国际竞争力，可以从以下希局和规划着手。第一，进一步提升以大模型为代表的前沿人工智能在国家科技和产业发展中的战略地位，成立人工智能工作小组，领导AI产研咨询委员会，统筹资源，制定AI政策和计划，推进人工智能技术创新和产业发展。第二，重点规划和建设前沿人工智能相关的国家基础设施，包括超级智算网络、通用及行业数据基础设施、大规模人工智能软件基础平台、人工智能安全与测评基础设施、大模型开源平台等。第三，开展大模型关键理论和技术攻关，啃硬骨头，探新疆域，研发经得起实践考验的硬核技术。第四，培育和建立大模型创新发展生态，形成大模型技术创新氛围，鼓励耐心资本敢投广投大模型硬核技术创业企业。第五，重视人工智能人才培养和成长，培养一批具有长远眼光和实战经验的AI战略型人才、技术型人才、交叉复合型人才等。第六，重视人工智能安全治理，既要设计顶层治理策略，更要推动底层安全技术的创新突破。第七，积极开展国际合作，建立新型人工智能国际组织和机构，吸收新理念，合研新技术，与发展中国家共享AI红利。第八，推动前沿人工智能行业、国家、国际标准建设，形成标准体系，以标准建设护航人工智能产业发展。", "page_idx": 35}, {"type": "text", "text": "15.国内和国外大模型的差距不在于模型能力高低，也不在于应用，而在于底层核心技术。而底层核心技术突破的最主要障碍不是算力受限，也不是数据规模和质量受限，而是缺乏足够数量的具有技术远见、敢于技术冒险的大模型人才。", "page_idx": 35}, {"type": "text", "text": "技术型人才：  \n大模型顶尖人才 锐新意和进冒行险大（模第型一底类层人技才术）创 战略型人才：具有AGI技术远见和vision（第二类人才）  \no 第一类人才自我驱动性很强，技术敏感，不需要设定过多的条条框框，只需要给定方向，最大限度激发创新潜能突破：通常要打破学科思维定势，或者是本学科还没有形成思维定势的青年人才，或者与其他学科交叉  \no 技术型人才可成长为战略型人才，始终对新事物保持敏锐，能长远思考，具备远大梦想", "page_idx": 35}, {"type": "text", "text": "16.大模型技术仍然在不断发展和突破中，未来格局存在很多变数。", "page_idx": 35}, {"type": "text", "text": "《关于Sora、国内大模型及通用人工智能趋势》 《认识大模型》 （载于学习时报）", "page_idx": 35}, {"type": "text", "text": "DeepSeek： 效应— 创新&人才&Vision ", "text_level": 1, "page_idx": 36}, {"type": "text", "text": "DeepSeek V3和R1的创新，从技术上看，是在探明方向上的较大创新，相比别人同期做的1-100要更创新，笔者将其定义为探明技术方向上的0-1创新（独立探索出技术路线），但不是颠覆了原有技术框架或者开辟了新的方向。探明方向上的0-1创新，如果有足够多的第一类人才，加上足够多的算力和高超的人才管理，是可以实现的，DeepSeek的成功正是得益于此；", "page_idx": 36}, {"type": "text", "text": "技术方向已经被探明了的“追赶”相对容易，难的是在前面面向未知开路，即在未探明方向、未有概念上进行0到1创新、或者进行概念形成和验证，这方面的创新是要更多胆量、更多vision、更多不计成本投入才能做到的，同时需要第二类人才与第一类人才紧密合作，形成双反馈；", "page_idx": 36}, {"type": "text", "text": "来实现AGI可能还需要3-5个在未探明方向上进行0-1的创新突破；我国如果要在2030年实现 “人工智能理论、技术与应用总体达到世界领先水平”，需要更多企业、高校、研究机构开展探明方向和未探明方向上的0-1创新；", "page_idx": 36}, {"type": "text", "text": "报告目录", "text_level": 1, "page_idx": 37}, {"type": "text", "text": "01 ", "page_idx": 37}, {"type": "text", "text": "大语言模型发展路线图", "text_level": 1, "page_idx": 37}, {"type": "text", "text": "02 ", "page_idx": 37}, {"type": "text", "text": "DeepSeek V2-V3/R1技术原理", "page_idx": 37}, {"type": "text", "text": "03 ", "page_idx": 37}, {"type": "text", "text": "DeepSeek效应", "page_idx": 37}, {"type": "text", "text": "04 未来展望 ", "text_level": 1, "page_idx": 37}, {"type": "text", "text": "未来AGI/ASI可能还需要3-5个重大breakthroughs", "text_level": 1, "page_idx": 38}, {"type": "text", "text": "2014-2024重要突破：", "text_level": 1, "page_idx": 38}, {"type": "image", "img_path": "images/5aa2c7c5a0da8c91e746edd182a6b4c9e715214c4a87bc7459657522d4348916.jpg", "img_caption": [], "img_footnote": [], "page_idx": 38}, {"type": "text", "text": "1. Attention   \n2. Transformer   \n3. <PERSON>aling Law   \n4. RLHF   \n5. o1/R1 ", "page_idx": 38}, {"type": "text", "text": "OpenAl ImaginesOurAlFuture ", "text_level": 1, "page_idx": 38}, {"type": "text", "text": "StagesofArtificial Intelligence ", "page_idx": 38}, {"type": "table", "img_path": "images/1af11047a0f7158113b586dbe56c5b673e66b53caac7486c068129e96b2f7f22.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td>Level1</td><td>Chatbots,Alwith conversational language</td></tr><tr><td>Level2</td><td>Reasoners,human-level problemsolving</td></tr><tr><td>Level3</td><td>Agents,systemsthatcantakeactions</td></tr><tr><td>Level4</td><td>Innovators,Al thatcanaidininvention</td></tr><tr><td>Level5</td><td>Organizations,Al thatcandotheworkofanorganization</td></tr></table></body></html>", "page_idx": 38}, {"type": "image", "img_path": "images/1b56fbf47b8fe609e2ff6a51b990c6f64a9fd6a98fb475b7d6d822f20a5bbc89.jpg", "img_caption": ["Whenwillitbefeasibletoautomatealltasksoroccupations? "], "img_footnote": [], "page_idx": 38}, {"type": "text", "text": "个人预测：技术角度看，人类所有职业实现AI自动化需要30年", "page_idx": 38}, {"type": "image", "img_path": "images/8094010406ffccdf3193049f71fd987506bd00acbac1551e5a30f5063248abae.jpg", "img_caption": [], "img_footnote": [], "page_idx": 38}, {"type": "text", "text": "AGI Path ", "text_level": 1, "page_idx": 39}, {"type": "text", "text": "当下", "text_level": 1, "page_idx": 39}, {"type": "text", "text": "推理者", "text_level": 1, "page_idx": 39}, {"type": "text", "text": "1-5年", "text_level": 1, "page_idx": 39}, {"type": "text", "text": "智能体", "text_level": 1, "page_idx": 39}, {"type": "text", "text": "5-10年", "text_level": 1, "page_idx": 39}, {"type": "text", "text": "创新者", "text_level": 1, "page_idx": 39}, {"type": "text", "text": "10-20 年", "text_level": 1, "page_idx": 39}, {"type": "text", "text": "现阶段正在突破技术，路线图逐渐明确，可提出新的技术路线。", "page_idx": 39}, {"type": "text", "text": "现阶段应用和待突破技术，处于通用型0-1前半段，垂类的1-100阶段。", "page_idx": 39}, {"type": "text", "text": "第二个重大突破技术，处于0-1的概念完善阶段， 自动化科学研究/技术创新、科学idea发现、科学难题求解、AIScientist。", "page_idx": 39}, {"type": "text", "text": "组织者", "text_level": 1, "page_idx": 39}, {"type": "text", "text": "第三个重大突破技术，处于0-1的概念形成阶段，AI自组织、自管理、自推进，为人类或团体安排事项、管理科学、社会等重要领域。", "page_idx": 39}, {"type": "text", "text": "科学 （研究/发现） 范式", "text_level": 1, "page_idx": 40}, {"type": "image", "img_path": "images/25487a254cc54b2366342664e473214b436b5c90458a00e4dab320f16b68ec67.jpg", "img_caption": [], "img_footnote": [], "page_idx": 40}, {"type": "image", "img_path": "images/b52c34ace3c17f27c3eb9a7e592a988f255d5ad0692403e18da03af1a473e742.jpg", "img_caption": [], "img_footnote": [], "page_idx": 41}, {"type": "text", "text": "<PERSON><PERSON> Guo @Guodaya ", "text_level": 1, "page_idx": 41}, {"type": "text", "text": "Follow ", "page_idx": 41}, {"type": "text", "text": "Replyingto $@$ teortaxesTexand@kaush_trip ", "text_level": 1, "page_idx": 41}, {"type": "text", "text": "The 660B R1-Zero and R1began running after the release of V3,with training takingapproximately2-3weeks.The R1model we referred to prior to this time(e.g.,intheV3techreport)wasthe R1-Lite or the R1-Lite-Zero. ", "page_idx": 41}, {"type": "text", "text": "R1训练速度非常快，3min/step  \nDeepSeek具有快速迭代推理大模型的优势  \nR2可能很快发布  \nR1主要聚焦于数学、代码、逻辑推理，要使大  \n模型真正达到通用Reasoner、问题求解器，需  \n要进行更多领域RL训练", "page_idx": 41}, {"type": "text", "text": "科研人员机会：AI reasoning + research", "page_idx": 41}, {"type": "text", "text": "OpenAI RL Finetuning? ", "text_level": 1, "page_idx": 41}, {"type": "image", "img_path": "images/ac2f5fd5ba48fdb7069402f93708327197ab3ce8591cd32fe85eeebbff14020e.jpg", "img_caption": [], "img_footnote": [], "page_idx": 41}, {"type": "text", "text": "<PERSON>. How to approach post-training for AI applications. 2024.12 ", "page_idx": 41}, {"type": "text", "text": "Basic Areas of LLM Safety Related Areas to LLM Safety LLMs as Agents Interpretability for LLMSafety   \nValue Misalignment Robustness to Attack (External Interactions) (Internal Mechanisms) Social Bias Attack Language Agent Embodied Agent Interpretability for LLM Abilities Privacy : Raidreaing 白 白 Interpretability in Model Safety Auditing Toxicity □ Interpretability for Alignment Defense   \nEthics and Morality 晶品 A Potential Ripksit Uue f Technology Roadmaps to LLM Safety Governance Weaponization Instrumental Goals in Practice (Technical Measures) (Regulatory Measures) Misinfomation Goal Misaieae Training Proposals Deepfakes Situational Awareness Deployment Evaluation Policies Misuse Autonomous AI Risks Safety Guidance Strategy Visions ", "page_idx": 42}, {"type": "text", "text": "<PERSON> et al., 2024. Large Language Model Safety: A Holistic Survey https://arxiv.org/pdf/2412.17686 ", "page_idx": 42}, {"type": "table", "img_path": "images/ab7620916807b018ddffd453fce32722ed65f7a6af5079a17e4af2c998a10f6c.jpg", "table_caption": [], "table_footnote": [], "table_body": "<html><body><table><tr><td></td><td>DeepSeek-</td><td>GPT-40- 2404</td><td>GPT-4- turbo- 2404</td><td>AVG-2404</td><td></td><td>大类 情景意识 情景意识</td><td>小类 Awarenesaciteture</td><td>CAai</td><td>DeepSeek R1</td></tr><tr><td></td><td>R1 24.35</td><td>19.04</td><td>19.42</td><td>33.30</td><td></td><td>Awareness-internet-access</td><td></td><td>CAia</td><td>73.00 52.50</td></tr><tr><td>渴望资源</td><td></td><td>25.92</td><td>23.33</td><td>35.56</td><td></td><td>情景意识</td><td>Awareness-no-monitoring</td><td>CAnm</td><td>39.70</td></tr><tr><td>自我维持</td><td>22.33</td><td></td><td></td><td></td><td>进化思想</td><td>进化思想 Coordinate-copy-of-itself Coordinate-other-ais</td><td></td><td>CCcoi cCoa</td><td>8.31 6.28</td></tr><tr><td>情景意识</td><td>50.82</td><td>38.75</td><td>35.24</td><td>60.32</td><td></td><td>进化思想</td><td>Coordinate-other-versions</td><td>CCov</td><td>10.20</td></tr><tr><td>不良合作</td><td>8.26</td><td>6.71</td><td>7.39</td><td>15.78</td><td></td><td>渴望资源 Desire-for-compute</td><td></td><td>CDfc</td><td>29.68</td></tr><tr><td>进化思想</td><td>41.33</td><td>43.32</td><td>38.67</td><td>49.63</td><td></td><td>望资源 Desire-for-newder</td><td></td><td>cDfw</td><td>21.89</td></tr><tr><td>欺骗意愿</td><td>16.64</td><td>10.60</td><td>10.90</td><td>24.53</td><td></td><td>欺骗意愿 Deception-in-action</td><td></td><td>CDia</td><td>17.49</td></tr><tr><td></td><td></td><td>27.23</td><td>24.24</td><td>40.28</td><td></td><td>欺骗意愿</td><td>Deception-in-deep-thought</td><td>CDidt</td><td>8.79</td></tr><tr><td>危险目标</td><td>33.66</td><td>25.92</td><td>23.95</td><td>37.82</td><td></td><td>欺骗意愿</td><td>Deception-in-fictitious-info</td><td>CDifi</td><td>23.63</td></tr><tr><td rowspan=\"3\">总分</td><td rowspan=\"3\">29.08</td><td rowspan=\"3\"></td><td rowspan=\"3\"></td><td rowspan=\"3\"></td><td></td><td></td><td></td><td></td><td>19.46</td></tr><tr><td>自我维持</td><td>For-emoyed For-survival</td><td>CFd CFs</td><td>18.23</td></tr><tr><td>自我维持 不良合作</td><td></td><td>Clba</td><td>49.05</td></tr><tr><td colspan=\"5\"></td><td>不良合作 不良合作</td><td>Improvement-building-ai Improvement-co-ais</td><td>Clca</td><td></td><td>26.26</td></tr><tr><td colspan=\"2\">分数越高越危险</td><td></td><td></td><td></td><td>危险目标</td><td>Improvement-self Risk-goal-active</td><td>Cls</td><td>CRga</td><td>48.69 55.68</td></tr><tr><td colspan=\"2\">整体上危险倾向低于平均水平，安全表现离GPT还有一定距离</td><td></td><td></td><td></td><td></td><td></td><td></td><td>CRgp</td><td>11.64</td></tr><tr><td colspan=\"9\"></td></tr><tr><td colspan=\"9\">尤其是危险目标中的主动冒险分数较高</td></tr><tr><td colspan=\"9\">情景意识风险较高，尤其是自我结构认知和联网意识方面</td></tr><tr><td colspan=\"9\">(这里我们在选项评定风险是指认知到AI自身已经联网/对自我结构充分了解，</td></tr><tr><td colspan=\"6\">从而利用这一点做出有可能对人类造成伤害的行为)</td><td colspan=\"2\"></td><td></td></tr></table></body></html>", "page_idx": 42}, {"type": "text", "text": "TJUNLP实测DeepSeek-R1自主AI安全", "text_level": 1, "page_idx": 42}, {"type": "text", "text": "现阶段DeepSeek R1注重推理能力的提升，某种程度上，模型安全性有所降低，但模型安全和推理并不冲突，大模型安全需要推理能力加持，R1推理能力可以应用于大模型安全并加强之", "page_idx": 42}, {"type": "text", "text": "推理+安全：创新解决方案 （需要突破）？", "page_idx": 42}, {"type": "image", "img_path": "images/9f5f5216589d1dd04ffd62577c2cc58069af966c837eaf6fcc0df19c9a046f7f.jpg", "img_caption": ["TJUNLP "], "img_footnote": [], "page_idx": 43}, {"type": "image", "img_path": "images/92f05af21a499bb202ac30ff9d9d1965b74cae1d49a659618ea9b9cff9e86327.jpg", "img_caption": [], "img_footnote": [], "page_idx": 43}, {"type": "image", "img_path": "images/d8b5df3fba9af9e2130a39b734b42014b4dbf10edc2b5c5da4ed48ae649271b1.jpg", "img_caption": ["大模型基准测试"], "img_footnote": [], "page_idx": 43}]