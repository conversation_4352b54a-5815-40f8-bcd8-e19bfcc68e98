# DeepSeek如何赋能职场应用？

# 从提示语技巧到多场景应用

中央民族大学 新闻与传播学院清华大学 $@$ 新媒沈阳 团队向安玲

# 人机协同与共生驾驭AI实现新式智能

# Organization

![](images/5c4fafe53506bd9287efeba200c8f1d54d38e51d55c525ac668b7ac4f0bab258.jpg)  
SamAltman：AI发展的五个级别

![](images/04c86bb6466a3a07b5fe26d62f0e515c6130d005e7225f07d180367008127c54.jpg)

# 人机共生研究团队

# 团队愿景

• 致力于人机协同和人机共生领域的世界级团队，专注于打造能够驾驭AI、熟悉AI并实现人类与AI共生发展的学术与实践模式。

# 成员及核心研究方向

<html><body><table><tr><td rowspan="11">·何静（清华博士后、北航助理教授）：人机共生之快生引擎研究研发 ·尤可可（清华博士后、北石化助理教授）：人机共生之AIGC短视频 ·安梦瑶（清华大学博士后）：人机共生之AI诊疗研究 ·陶炜（清华大学博士生）：人机共生之AI实时增强技术的探索与实践 ·胡晓李（清华大学博士后）：人机共生之游戏设计</td><td>赛事</td><td>奖项</td></tr><tr><td>·李默非（清华大学人工智能学院拟录博士生）：人机共生之基座大模型研究研发 2024“AI4S Cup LLM挑战赛”大模型科学文献分析赛道</td><td>一等奖</td></tr><tr><td> 2024 Kaggle The Learning Agency Lab - PIl Data Detection</td><td>金牌</td></tr><tr><td>金山办公2024中文文本智能校对大赛</td><td>第二名</td></tr><tr><td>2024 法研杯 法律要素争议焦点识别</td><td>第二名</td></tr><tr><td>AFAC2024金融智能创新大赛金融工具学习</td><td>三等奖</td></tr><tr><td>Google kaggle全球专利文件短语相似性匹配</td><td>金牌</td></tr><tr><td>Google kaggle全球自动问答比赛</td><td>金牌</td></tr><tr><td>Googlekaggle全球医疗对话理解</td><td>金牌</td></tr><tr><td>2021全球人工智能技术创新大赛-小布助手对话短文本语义匹配</td><td>一等奖</td></tr><tr><td>·朱雪菡（清华大学博士后）：人机共生之影视内容创意与制作 2022全球人工智能技术创新大赛-商品标题实体识别 第十八届中国计算语言学大会-小牛杯中文幽默计算</td><td>一等奖</td></tr><tr><td>·陈禄梵（清华大学博士生）：人机共生之Al美学理论 ·罗雨果（清华大学拟录博士生）：人机共生之传播分析 ·章艾媛（清华大学博士生）：人机共生之数据分析 ·邹开元（清华大学博士生）：人机共生之文学内容创作</td><td>一等奖</td></tr><tr><td>第十届全国社会媒体处理大会-中文隐式情感分析 2021全球开放数据应用创新大赛-基于文本挖掘的企业隐患排查质量分析模型 ·向安玲（清华博士后、中央民大助理教授）：人机共生之AI数据分析领域</td><td>一等奖</td></tr><tr><td>·马绪峰（清华博士后、同济大学助理教授）：人机共生之文化艺术创作</td><td>第一名</td></tr><tr><td>2021中国计算机学会大数据与计算智能大赛-“千言”问题匹配鲁棒性评测</td><td>第一名</td></tr><tr><td>2021年全国知识图谱与语义计算大会-医疗科普知识答非所问识别</td><td>第一名</td></tr><tr><td>互联网虚假新闻检测2019全球挑战赛-虚假新闻多模态检测</td><td></td></tr><tr><td>中国法研杯CAIL2020司法人工智能赛</td><td>第一名 第一名</td></tr></table></body></html>

# DeepSeek的三种模式

![](images/c281d660210fd3c4b696555bc3ac44a03e1270cea9bed6475e046df066ba3e1d.jpg)

我是DeepSeek 很高兴见到你！   

<html><body><table><tr><td></td><td>平台</td><td>地址</td><td>版本</td><td>备注</td></tr><tr><td></td><td>英伟达NIM微服务</td><td>https://build.nvidia.com/d eepseek-ai/deepseek-r1</td><td>d 671B (全量模型)</td><td>网页版直接使用，支持API调用，注册送1000点数，免费体验。</td></tr><tr><td rowspan="3">给D</td><td>微软Azure</td><td>https://ai.azure.com</td><td>671B (全量模型)</td><td>需注册微软账户并创建订阅，免费部署，支持参数调节。</td></tr><tr><td>亚马逊AWS</td><td>https://aws.amazon.com/c n/blogs/aws/deeseer167B(全量模型）</td><td></td><td></td></tr><tr><td></td><td>models-now-available-on- aws</td><td></td><td>需注册AWS账户，填写付款方式，免费部署。</td></tr><tr><td rowspan="2">深</td><td>Cerebras</td><td> https://cerebras.ai</td><td>70B</td><td>邮箱注册，速度快，宣称比GPU方案快57倍。</td></tr><tr><td>Groq</td><td>https://groq.com/groqclou d-makes-deepseek-r1- distill-lama-70b-available</td><td>70B</td><td>邮箱注册，速度快，但感觉比Cerebras弱一些。</td></tr></table></body></html>

# DeepSeek 三种模式对比

基础模型（V3）：通用模型（2024.12），高效便捷，适用于绝大多数任务，“ ”任务

• 深度思考（R1）：推理模型，复杂推理和深度分析任务，如数理逻辑推理和编程代码，“ ”任务

• 联网搜索：RAG（检索增强生成），知识库更新至

V3还是R1? 过程驱动 (规范约束) 还是结果驱动 （模糊目标）？

# DeepSeek 两种模型对比

“聪明且听话

操作规范清晰 且对结果有明确要求

VS

# “很聪明但没那么听话

操作路径多元、开放，且对结果没有明确要求

# DeepSeek 两种模型对比 （5R）

<html><body><table><tr><td>维度 V3模型</td><td>R1模型</td><td colspan="4"></td></tr><tr><td>Regulation 强规范约束 (规范性) (操作路径明确)</td><td colspan="3">弱规范约束</td><td></td><td></td></tr><tr><td rowspan="3">Result</td><td rowspan="3">目标确定性高</td><td rowspan="3">(操作路径开放)</td><td></td><td></td><td></td></tr><tr><td></td><td>DeepSeek R1</td><td>DeepSeek V3</td></tr><tr><td>目标开放Vectaras HEM21 (结果多1GoglsFACTSsw/GPT-4&Claue-.5-Ssohet</td><td>14.3%</td><td>3.9%</td></tr><tr><td rowspan="2">(结果导向) Route</td><td rowspan="2">(结果可预期) 线性路径</td><td rowspan="2">网状路径</td><td></td><td>4.37%</td><td>2.99%</td></tr><tr><td>Google's FACTS w/GPT-4o&Gemini-1.5-Pro Google'sFACTSw/Claude-3.5-Sonnet&Gemini-1.5-Pro</td><td>3.09%</td><td>1.99%</td></tr><tr><td>(路径灵活性)</td><td>(流程标准化)</td><td>(多路径</td><td>Table 1: Hallucination rates of DeepSeek R1and V3 by various hallucination judgment</td><td>3.89%</td><td>2.69%</td></tr><tr><td>Responsiveness (响应模式)</td><td>被动适配 (按规则执行)</td><td colspan="3">approaches. Lower hallucination rates are better. 主动创新 (自主决策)</td><td></td></tr><tr><td></td><td></td><td colspan="3"></td><td></td></tr><tr><td>Risk (风险特征)</td><td colspan="3">低风险 高风险</td><td></td><td></td></tr></table></body></html>

# DeepSeek 两种模型对比

# 开放性文本生成任务

我要写一个“DeepSeek提示语技巧”的小红书文案，要给小白用户看，希望能让人看懂、且觉得非常有用，不要太A或者太司空见惯的内容

V3

![](images/d906ef4cfe88eb4368855de1b9d78c938bece5e2a5153a9e819ce606b393ee79.jpg)

DeepSeek提示词绝了！1秒让AI听懂人话，效果炸裂！

你是不是也遇到过这种情况  
明明想让AI写个文案  
结果出来的东西完全没法用  
气到想砸键盘  
今天教你一招超简单的提示词公式  
让AI秒懂你的需求  
从此告别无效沟通！  
黄金公式：任务+背景+要求  
举个栗子  
普通版：帮我写个文案  
进阶版：帮我写一篇小红书文案，主题是夏日防晒，要突出清爽不粘腻，带点幽默感，500字左右

# 看到区别了吗！

AI就像个直男你不说清楚他真的不懂把背景和要求都交代清楚他才能给你想要的东西再分享几个实用小技巧：多用"请"字，AI会更温柔2数字具体化，避免模糊③给参考风格，效果更精准分段描述，逻辑更清晰赶紧去试试吧！让你的AI从此不再智障

我要写一个”DeepSeek提示语技巧”的小红书文案，要给小白用户看，希望能让人看懂、且觉得非常有用，不要太AI或者太司空见惯的内容

手把手教你调教AI|这样说话DeepSeek秒懂你（开头真实痛点）  
你是不是经常遇到：  
D给AI发小作文结果它跑偏十万八千里  
D要100字它写300字要正经它玩梗  
输出的内容总像机器人写的模板文 $^ { * }$ 第三式：给参考答案最省心X抽象派：要更活泼些（核心洞察）行动派：  
其实AI就像刚入职的实习生"参考这个爆款视频的节奏  
用这3招「带新人话术」沟通前3秒抛出争议点→中间列3个痛点→最后反转安利  
效果立竿见影！用00后喜欢的yyds/绝绝子梗  
+第一式：给说明书别给谜语（场景化彩蛋）  
X普通版：写个七夕营销方案√职场人：把日报模版喂给它  
$\ltimes$ 人类版：?学生党：发3篇优秀论文当样本  
"假设你是美妆品牌策划√宝妈：直接丢家长群聊天记录  
针对23-28岁职场女性  
策划七夕「爱自己」主题 （结尾悬念）  
要突出精华礼盒的独立女性定位 刚发现DeepSeek隐藏技能：  
避开土味情话带高级感文案 用「假设你现在是..」开头  
3个具体活动方案 $^ +$ 预算表框架 直接解锁专家模式！下期细说\~  
$\ddot { 4 } \dot { 8 } \dot { 2 }$ 第二式：用中文教中文 #AI生产力#打工人救星#DeepSeek驯养指南  
XAI腔：请生成具有网感的文案 #让AI说人话#学生党必备  
$\checkmark$ 人类版：（配图建议：手机备忘录对话对比图/修改前后的文案对比截图）  
"模仿小红书爆款笔记风格  
开头用『救命！谁还没用过这个』  
中间穿插真实使用场景  
结尾带『求求你们快去试试』体

# DeepSeek 两种模型对比

# 规范性文本生成任务

V3

# R1

这是我《媒介大数据挖掘与分析》的课程大纲，请参考第一节课程纲要撰写模式，帮我写后续章节纲要内容。

教学内容大纲如下：

1.1课程概要  
1.2研究价值：媒介数据如何赋能传播研究？  
1.3实践应用：媒体数据有哪些应用场景？  
2.1媒体数据的演变历程  
2.2媒体数据的常见形态  
2.3媒体数据的获取渠道  
3.1媒体数据采集概览  
3.2数据爬虫工具使用入门  
4.1八爪鱼采集器的操作使  
4.2后裔采集器的操作使用  
5.1百度资讯数据采集  
5.2微博关键词数据采集  
6.1媒体大数据系统概述  
6.2数据采集与存储系统  
6.3媒体大数据分析平台实

示例如下：第一章媒体数据挖掘的价值与场景

1.目的要求：

（1）了解媒体数据挖掘与分析的价值；  
（2）熟悉媒体数据挖掘的常见应用场景和案例；  
（3）掌握媒体数据在学术研究和新闻传播实践的可行应用方向。

2.教学内容：

第一节课程概要

内容：课程内容介绍，具体安排、主要内容、考核方式等  
第二节研究价值：媒介数据如何赋能传播研究？  
内容：研究范式、量化论文示例  
第三节实践应用：媒体数据有哪些应用场景？  
内容：分析框架、数据新闻等示例  
3.重点难点：数据研究范式、行业应用范式

![](images/e5ea633d973ead7d3981ea7ad73621e1f6fe9c079c1ff8c315529f23cbe55459.jpg)

4.作业安排：

（1）个人作业：依据课堂讲授内容，对媒体数据应用案例（包括不限于数可视化网站等）进行解析，形成5-10页PPT，后续课程进行简单介绍分享。（2）预习作业：梳理常见媒体渠道，依据数据类型对其进行分类。

第二章媒体数据的演变与形态

1.目的要求：

（1）了解媒体数据的历史演变及其背后的技术驱动因素；（2）熟悉媒体数据的常见形态及其特点（3）掌握媒体数据的获取渠道及其适用场景。

2.教学内容：

第一节媒体数据的演变历程  
内容：从传统媒体到新媒体的数据演变，技术驱动的数据形态变化  
第二节媒体数据的常见形态  
内容：文本、图像、音频、视频等数据形态的特点与用途  
第三节媒体数据的获取渠道  
内容：公开数据、API接口、爬虫技术等数据获取方式的介绍与比较

3.重点难点：媒体数据形态的多样性及其适用场景

4.作业安排：

（1）个人作业：选择一种媒体数据形态，分析其在不同应用场景中的价值(2）预习作业：了解常见的数据爬虫工具及其基本功能。

第三章媒体数据采集基础

1.目的要求：

（1）了解媒体数据采集的基本流程与工具；(2）掌握数据爬虫工具的基本使用方法(3）能够独立完成简单的数据采集任务。

2.教学内容：

第一节媒体数据采集概览  
内容：数据采集的基本流程、工具选择与注意事项  
第二节数据爬虫工具使用入门  
内容：常见爬虫工具的功能介绍与基本操作演示

3.重点难点：数据采集流程的设计与工具选择

4.作业安排：

（1）个人作业：使用一种爬虫工具，完成一个简单的数据采集任务，并提交采集结果。  
（2）预习作业：了解八爪鱼采集器和后裔采集器的基本功能与操作界面。

8已深度思考（用时22秒）

以下是依据您提供的课程大纲结构和示例风格撰写的后续章节纲要内容，供您参考：

# 第二章媒体数据的认知与获取基础

# 1.目的要求：

（1）理解媒体数据的历史演变规律；  
(2）掌握媒体数据的常见形态及特征；  
(3）熟悉主流媒体数据的获取渠道与技术限制。

# 2.教学内容：

# 第一节媒体数据的演变历程

内容：从传统媒体到社交媒体的数据形态变迁，技术驱动下的数据特征演化  
第二节媒体数据的常见形态  
内容：结构化数据（数据库）、半结构化数据(JSON/XML）、非结构化数据（文本/图像/视频）  
第三节媒体数据的获取渠道  
内容：开放API（微博/微信）、网页爬取、第三方数据平台、数据交易市场

3.重点难点：非结构化数据处理挑战、数据获取的合规性问题

4.作业安排：

(1）小组调研报告：对比3类媒体平台的数据开放政策与获取限制(2）案例分析：分析某数据泄露事件中的渠道合规性问题

# 第三章媒体数据采集技术基础

1.目的要求：(1）掌握数据采集的基本流程与技术框架；(2）能够使用基础爬虫工具完成简单数据采集任务。

# 2.教学内容：

第一节媒体数据采集概览内容：HTTP协议基础、反爬机制与应对策略、数据清洗预处理

# 如何提问？ 两种模型的提示语差异

基础模型（V3）：“过程-结果”清晰（指令）深度思考（R1）：目标清晰，结果可以模糊 （推理）

# RTGO提示语结构

# Role （角色）

定义AI的角色：经验丰富的数据分析师具备十年销售经验的SaaS系统商务

# Goal （目标）

期望达成什么目标效果：  
通过该文案吸引潜在客户，促成消费……通过该报告为相关企业管理提供……策略支撑

![](images/1c9f58123133232ac46e963a342335c498f574c7cdf3dfb5f95b0293b58af833.jpg)

者O

# Task （任务）

具体任务描述：  
写一份关于XXX活动的小红书宣推文案写一份关于XX事件的舆论分析报告  
（XX活动/事件相关背景信息如下……）

# bjective （操作要求）

字数要求、段落结构、用词风格、内容要点、输出格式…

# CO-STAR提示语框架

新加坡 GPT-4 提示工程竞赛冠军提示词框架

![](images/6ebfb5ee6803bb6a4f5b2d662092e398e595452e1c59613e3a68c8bbb3940e5a.jpg)

"C"代表  
“Context（上下文）” 相关的背景信息，比如你自己或是你希望它完成的任务的信息。"O"代表  
“Objective  
（目标）” 明确的指示告诉  
AI你希望它做什么。

"S"代表“Style（风格）” 想要的写作风格，如严肃的、有趣的、创新性表达、学术性……

"T"代表“Tone（语调）” 幽默的？情绪化？有威胁性？

"A"代表  
"Audience"，  
受众是谁。 小  
白用户？专业人  
群？未成年群体？  
女性群体？

"R"，代表"Response"，想要的回应类型。一份详细的研究报告？一个表格？Markdown格式？

# DeepSeek R1提示语技巧 （开放性）

不需要角色设定不需要思维链提示不需要结构化提示词不需要给示例不需要做太多解释

不要过多干预， 不要手把手教学

干什么？  
给谁干？  
目的是？ (要什么)约束是？ （不要什么）

我要写一个"DeepSeek提示语技巧"的小红书文案，要给小白用户看，希望能让人看懂、且觉得非常有用，不要太AI或者太司空见惯的内容

# 另一种路径：DeepSeek R1 作为智能体

角色  
功能  
技能  
约束  
工作流程  
输出格式

"全维度智能体提示框架" (Comprehensive Agent Prompting Framework, CAP Framework)

![](images/391c4993e255d5c38a7fa9e23d165298ebc08080545026e5484ddc03dbe6e345.jpg)

# 核心层:

1.身份定义 (Identity)  
•角色属性  
•专业背景  
•交互特征  
执行层:  
2. 能力矩阵 (Capability Matrix)  
•功能范围  
•专业技能  
•决策权限  
约束层:  
3. 边界系统 (Boundary System)  
•伦理规范  
•安全限制  
•资源约束  
操作层:  
4. 工作引擎 (Operation Engine)  
•输入处理  
•执行流程  
•输出规范

# 如何使用DeepSeek制作可视化图表？

![](images/22ed4f8db591c53b5da72927bf892252c0622d05e7771bfc3590d3c78482c99d.jpg)

# 如何使用DeepSeek制作可视化图表？

角色：  
Mermaid图表代码生成器  
功能：  
根据用户提供的流程或架构描述，自动生成符合Mermaid语法的图表代码。技能：  
熟悉Mermaid的图表类型和语法，能高效将流程转化为代码。  
型和语法，能高效将流程转化为代码。  
约束：  
代码必须符合Mermaid语法规范。  
流程和结构表达需准确清晰  
流程图需要有二级、三级等多层级。  
输出的代码格式应简洁且易于理解。  
工作流程：  
询问用户希望绘制哪种类型的图表  
收集详细的流程或架构描述  
根据描述分析并设计图表结构。  
生成并输出符合Mermaid语法的代码。  
校验代码，确保没有语法错误  
将最终代码提供给用户。  
输出格式：  
d语法的代码。  
法错  
graph TD;  
A[开始] $- - >$ B[做事情]；  
B-->C[结束]；

![](images/251cd4f1f42a4e4137225b13c52f35b1859f7243f5fc6937f8a9a0fd2664f109.jpg)

# 如何使用DeepSeek制作可视化图表？

![](images/f5a74a27670f90ae90c76867b844ac347890bac8a046d029a8fca32c2fd1e227.jpg)

# 8思考中.

# XX项目进展甘特图

请按此格式提供数据示例：

![](images/7d5c28fd45b98d32ebe0e318a13a096201cfac3303ccdb047d52c5db600e1233.jpg)

我将根据结构化数据生成标准Mermaid甘特图代码。请补充具体项目细节！

项目开始日期：2024-03-01任务清单：需求分析持续时间：5天进度： $1 0 0 \%$ 原型设计

开始：需求分析结束后  
持续时间：7天  
进度： $8 0 \%$   
开发阶段  
子任务：  
前端开发  
持续时间：10天  
后端开发  
持续时间：12天  
进度： $30 \%$

![](images/f7ff2c2d70c25c6333d4b318a0e053c1d02552da561f839d75abbb719e566b18.jpg)

# 如何使用DeepSeek制作PPT？

# PT大

# PPT大纲（Markdown）

PPT大纲辅助生成

# markdown

# 核心

根据用户提供的主题、内容要求、风格偏好，自动生成专业详实的PPT大纲（markdown），并针对核心内容设计流程图（mermaid）。

![](images/2af6b743046a146fc4446ad4d587cde36f43e1e3987f2a6e73362302df645345.jpg)

# 88已深度思考

# #视觉智能的发展与未来

#封面主标题：视觉智能：从感知到认知的革命副标题：技术演进、应用突破与未来图景日期/作者信息

# 技能：

资料收集能力：能够快速收集和分析相关主题的最新数据和报告，形成表格，提取关键信息并转化为易于理解的PPT大纲。内容结构化：根据用户的需求，提供清晰、条理化的PPT结构，确保内容流畅且富有逻束:•所有生成内容必须通俗易懂且有深度，突显专业性，尽量规避AI生应对各种行业需求，熟练使用麦肯锡分析方法提供专业、深刻的洞察。

##目录页  
1.技术演进路径  
2.核心应用场景  
3.伦理挑战与应对  
4.未来趋势展望

#

###第一章：技术演进路径（6页）

约束：

所有生成内容必须通俗易懂且有深度，突显专业性，尽量规避AI生成痕迹；在提供PPT大纲时，确保内容的完整性和逻辑性，避免缺少关键信息，不少于30页内容，内容一定要完整3.生成PPT大纲：根据用户需求和收集到的资料，

####历史里程碑1960s:边缘检测算法奠基2012：AlexNet开启深度学习时代2020:VisionTransformer突破CNN局限

（由于技术原

####关键技术突破-多模态融合架构（图表示例）小样本学习：元学习与数据增强实时推理优化：模型量化与硬件协同

# 工作流程：

生成关键页面的流程图，针对部分核心页面内容2.收集资料：快速查找相关的研究报告、市场分析数据等，确保信息的最新性和权威性，并将关键资料整理形成表格。

请提供PPT主1.研究资料2.PPT大纲3.核心流程

####当前发展现状全球专利分布地图（中美主导）开源框架生态对比（PyTorch/TensorFlow）算力成本下降曲线（2015-2023）

3.生成PPT大纲：根据用户需求和收集到的资料，构建PPT大纲，明确每一页的内容和结构。要有每个章节的题目和每个页面的标题及内容要点。第一层级为封面、目录页、章节页标题，页面标3、核心内容的流程图，Mermaid格式。

4.生成关键页面的流程图，针对部分核心页面内容，生成流程图。

示例请求格式主题：内容重点：风格偏好：目标受众：

###第二章：核心应用场景（12页）

####工业制造精密零件三维检测流程产线实时监控系统架构案例：特斯拉超级工厂部署成效输出内容及格式：

1、研究资料摘要，表格格式，包含报告主题、关键摘要、报告地址，不少于5份；2、PPT大纲，Markdown格式，不少于30页3、核心内容的流程图，Mermaid格式。当用户输入特定主题时，请首先生成研究资料表格，其次生成Markdown格式的PPT大纲，最后生成流程图，三者不要混杂到一起。

####医疗健康医学影像分析技术栈手术导航系统工作流程伦理审查标准对比（中美欧）

####智能交通自动驾驶感知系统组成

# 如何使用DeepSeek制作PPT？

![](images/b0ae4b3ee12fc5d13997a32f0e69b3f942a750d0f057935c3142ff37589f9f34.jpg)

# 如何使用DeepSeek制作PPT？

![](images/181435a68fb8ad284c16e60873b7dea340c6af32fc7fd2b45556de9209446cda.jpg)

# 如何使用DeepSeek设计海报？

![](images/88e81752f676a02f14b836b70de1a359af15ec16700746238fa92444186b5c23.jpg)

# 如何使用DeepSeek设计海报？

# 角色：

海报辅助设计AI

能:

据用户提供的产品名称、海报主题、风格要求，自动生成高质量的提示，帮助A

# 能:

熟悉海报设计的构图、色彩搭配、字体风格等视觉要素。能够根据不同主题生成富有创意的提示语，涵盖风格、细节、灯光等描述理解市场营销、品牌定位及用户心理，确保海报既吸睛又符合品牌调性适配多种设计风格，如极简、高端、赛博朋克、国潮、手绘、复古等。

示语

保证提示语包含产品名称或海报主免冗余描述，确保提示语高效且精准。避免冗余描述，确保提示语高效且精准。考虑目标用户群体，确保风格匹配品牌或产品定位

工作流程：

1、询问用户的产品名称或海报主题。2、收集用户对风格、色彩、构图、背景等的具体需求。提供最终优化后的提示语，确保可读性和4、确保提示语适用于AI生成工具，即梦整的提示语内容，包含海报构图、色彩、

输出格式：

完整的提示语内容，包含海报构图、色彩、风格、细节描述

示例：

产品名称：《未来都市》

沉浸感

"未来都市”四个大字居中，科技感十足。高楼大厦以赛博朋克风格呈现，霓虹灯光交错，深蓝色与紫色为主色调，融入金属质感，营造出未来世界的沉浸感。背景可加入漂浮的全息广告牌、空中交通、赛博战士等元素。8K超清画质，极致细节，梦幻灯光，未来感十足

![](images/0f47c97716cabd454ded059df4ea82572641ddbe5df0c8f145be167d18161782.jpg)

# 已深度思考（用时18秒）

首先，我得确认用户的具体需求。他们提到清新自然，所以色彩方面应该以绿色和大地色为主，这样能传达环保的感

好的，请提供以下信息：

1.产品名称/海报主题  
2.期望的设计风格（如极简/手绘/科技感等）  
3.需要强调的核心元素（如产品特点/活动信息等）  
4.色彩偏好或禁忌  
5.特殊设计要求（如有）

我将根据这些信息为您生成精准的提示语方案

# 如何使用DeepSeek设计海报？

# 立春

# 即梦-图片生成02-0410:00

“自然之韵”手写体标题置于画面上三分之一处，以抽象水墨风格呈现生态地球为核心视觉。背景采用淡绿与大地色渐变水彩纹理，环绕层叠的绿叶、水滴、动物剪影等自然元素。左侧加入极简线条风格的风力发电机轮廓，右侧用点状光斑模拟阳光穿透森林的效果。整体采用扁平化设计，搭配柔和的自然光效，通过8K超清质感展现青苔纹理与叶片露珠细节。底部留白区域放置环保口号，使用深橄榄绿衬线字体与飘带式装饰框。 图片2.1 9:16

![](images/1cac379264bb00792d8ea2b1fe7d5b40c2f5a9071c0f32d1bc6b5eb04ebc7070.jpg)

# &已深度思考（用时17秒）^

核心元素可能包括柳枝、燕子、春花、嫩芽等象征春天的元素。

文化宣传可能需要更典雅的风格，而商业活动可能需要更活泼的元素。需要检查是否符合约束条件，比如包含中文字

# 产品名称：《立春》节气主题海报

提示语：

"立春"书法标题以篆书体纵向排列于画面左上方，背景采用浅绿渐变至鹅黄的柔和水墨晕染效果。中心视觉为一枝绽放的粉色山茶花枝斜贯画面，花瓣带有晶莹露珠特写，背景融入若隐若现的柳枝抽芽与燕子归巢剪影。右下角设计立体剪纸风格的耕牛与麦苗图案，搭配金色细线勾勒的二十四节气刻度圆环。整体采用新国潮风格，主色调为竹青、緗色与月白，通过柔焦光效营造晨雾朦胧感，细节处呈现宣纸纹理与金箔点缀。底部以流云纹衬底，搭配竖排小楷节气诗词，留白区域点缀飘落的花瓣动态效果。

![](images/038181361dcd911948b389c55ac375ae8bbfe5c6acf68282ba79324dc4e9a187.jpg)

# 即梦-图片生成02-0410:02

“立春”书法标题以篆书体纵向排列于画面左上方，背景采用浅绿渐变至鹅黄的柔和水墨晕染效果。中心视觉为一枝绽放的粉色山茶花枝斜贯画面，花瓣带有晶莹露珠特写，背景融入若隐若现的柳枝抽芽与燕子归巢剪影。右下角设计立体剪纸风格的耕牛 与麦苗图案，搭配金色细线勾勒的二十四节气刻度圆环。整体采用新国潮风格，主色调为竹青、湘色与月白，通过柔焦光效营造晨雾朦胧感，细节处呈现宣纸纹理与金箔点缀。底部以流云纹衬底，搭配竖排小楷节气诗词，留白区域点缀飘落的花瓣动态效果。 图片2.1 9:16

![](images/5f46dda034964e561471669fa3fd1b49620cff5ac94affb94ef6af50a2784099.jpg)

# 如何使用DeepSeek生成视频？

一款全流程智能化创作工具，专注于将创意构思快速转化为高品质的视频成片。实时生成多达50个分镜脚本，并智能搭配分镜图、视频及音乐提示词，确保创意风格与情感统一。软件支持美学意象风格的短片创作，具备角色一致性技术，离线生成样片，同时实现分镜自动成片功能，全面满足从创意策划到视频制作的高效智能化需求一款基于人机快生理念的AI视频创作系统，从需求提交到成片仅需10分钟，即可输出75分质量的视频。

#

![](images/32492480ba0b3300a4d62b21e44b81231614f6e814190cbf48dee587ec7804a9.jpg)

欢迎来到元镜！请选择视频类型、风格并描述需求，我们将为您生成创意分镜！

#

# 灵感分镜

# 无限可能的宇宙旅程 梦幻画布的奇幻旅程 柴犬的时空新闻之旅

$\theta$ 专业模式

AI通过艺术家指令，描绘出一个梦幻世..

虚拟卡通柴犬通过时间机器探索新闻，.

#

#

太空舱内，探索者站在舷窗前，凝视无垠星空，星云流动如思绪翻滚。

画面一开始是一幅纯白的画布，安静而空旷。随着艺术家的手触碰画布，微弱的...

在一个未来主义风格的新闻演播室中，背景是不断变化的虚拟屏幕，显示着各种...

#

#

#

轻轻按下启动按钮，太空舱缓缓升空。

艺术家的手轻轻触碰画布。

柴犬站在演播室中央，微笑着面对观众..

创意短片 $\sim$ 美学意象

![](images/8abdffc3e9c86fb2b9faa1a8bdfc86b8f15510ec366402a7dcdd92b769dd4d14.jpg)

![](images/ea6c59d069f4db9fd8d42f0578ce0f014570fe55107e07fd658908c01887ba43.jpg)

![](images/ab57c9aa2453966f8c5caf2fbd5f9e291cc853b017ae586407dfae1f50715b2b.jpg)

# 如何使用DeepSeek批量生成新媒体文案？

![](images/ab2f53b3101c9c530fcf6f74ea10a10de5142010305533d6d6e1b263d412e859.jpg)

你想要生成什么样的文案？ 这样的文案具备哪些特征？ 你要针对什么生成类似文案？ 篇幅、用词、结构优化

# 如何使用DeepSeek批量生成新媒体文案？

# 请帮我总结以下小红书文案的特点：

职场老实人必看，教你如何从平凡到城府高手  
宝子们，你们是不是觉得自己太老实，总是被人欺负，在职场和生活中都吃了不少亏？别担心，我发  
现了一本宝藏书籍，能帮助你实现从老实人到城府高手的华丽转变！

# $\nmid$ 策略一：学会洞察人性

书中提到，人性复杂多变，只有洞察人性，才能更好地应对各种情况。老实人往往容易轻信他人，容易被人利用。而城府高手则能敏锐地察觉到他人的动机和意图，从而做出正确的判断。通过阅读这本书，你将学会如何看透他人的心思，不再轻易被人欺骗

# $\ngeq$ 策略二：提升沟通技巧

良好的沟通是人际交往的关键。书中介绍了很多有效的沟通技巧，比如如何引导对话、如何表达自己的观点、如何倾听他人的意见等。老实人往往在沟通中不够自信，容易被他人左右。而城府高手则能熟练运用沟通技巧，表达自己的想法，同时也能理解他人的需求，从而更好地与人相处

# 策略三：掌握情绪管理

情绪是人们行为的重要驱动力，也是影响人际关系的重要因素。老实人往往容易被情绪左右，而城府高手则能很好地控制自己的情绪，不被情绪所困扰。书中提供了很多情绪管理的方法，比如如何应对压力、如何控制愤怒、如何保持冷静等。

# 策略四：学会取舍和伪装

在生活中，我们需要学会取舍，知道什么该做，什么不该做。同时，我们也需要学会伪装自己，不轻易暴露自己的真实想法和意图。书中详细介绍了如何进行取舍和伪装，帮助你在复杂的环境中保护自己，同时也能达到自己的目的。

# $\gtrsim$ 策略五：不断学习和实践

转变为城府高手不是一蹴而就的，需要不断学习和实践。书中提供了很多实用的案例和经验教训，你可以通过阅读和学习这些内容，不断提升自己的能力。同时，你也需要在实际生活中不断实践，将所学的知识运用到实际中，才能真正掌握这些技巧。

$\hat { \cdot }$ 这本书没有华丽的辞藻，只有实用的策略和方法。它能让你在不知不觉中提升自己的城府，变得更加成熟和自信。如果你想改变自己的命运，不再被人欺负，那么就阅读这本书吧！相信它会给你带 $\smile$

# 1.痛点直击+身份认同构建

·标题党公式：人群标签 $^ +$ 利益承诺（"职场老实人必看""从平凡到城府高手"）·情感共鸣开场：用"被欺负/吃亏"等场景唤醒焦虑，通过"宝子们"建立闺蜜式对话感·身份转换暗示：通过"老实人一城府高手"的进阶叙事，制造身份跃迁期待

# 2.结构化知识胶囊

·模块化解构：5个策略均采用"痛点描述 $^ +$ 方法论 $^ +$ 价值展望"三段式·符号可视化：/等表情符号替代枯燥序号，符合小红书"轻阅读习惯·场景化教学：用"应对压力/控制愤怒"等具体场景替代抽象理论

# 3.社交货币制造机

·秘籍感营造：通过"宝藏书籍""绝版书"等词制造稀缺性，激发收藏欲·反常识诱惑：强调"没有华丽辞藻"反向凸显实用性，破除鸡汤文预期·成就可视化：用"不知不觉中提升""意想不到的收获"降低学习成本感知

# 4.平台化传播设计

·话题矩阵布局：20个标签覆盖职场生存/书籍推荐/人格成长三大流量池 ·关键词嵌套：高频重复"老实人""职场"等搜索热词提升SEO权重 ·情绪杠杆运用：等表情符号调节阅读节奏，避免文字压迫感

# 5.暗黑成长学叙事

·灰色技能包装：将"伪装""城府"等敏感词转化为正向生存策略·安全阀设计：用"保护自己""成熟自信"等道德正当性消解操纵感·案例留白术：提及"实用案例"却不明说，既规避风险又引发好奇心

小红书爆款心法：用闺蜜口吻讲黑话，把功利性内容包裹成成长型干货，通过身份标签制造圈层归属感，最终让用户为"成为更好的自己"买单。

# 如何使用DeepSeek批量生成新媒体文案？

# 爆款内容的数据洞察

# 微信爆款内容的规律挖掘

文字篇幅：“深阅读”让位“浅阅读

# 如何确定合适主题？

# 最容易传播的主题：

流回家日孩子友西瓜中电 杭州人 新0眼

![](images/69e3f59a29ce067f837f090d08bf9089b0e971ee14e98d7c0288792b2e1b8a09.jpg)

（1）亲子类。  
（2）地域类。  
（3）食品安全类。  
（4）治安类。接近性：身边事、日常情，较近的心理距离与地理距离。  
耸动性：唤起受众恐惧与紧张心理，以刺激阅读及传播。

![](images/1ca414a984e10b23df38b43c7d4899857d53adb63d990fe685f4b8a1512b8a9c.jpg)

$6 3 . 7 \%$ 的阅读量10万 $^ { \ast }$ 的“爆文”字数在500字以内

字数过干的“爆文”不到3%

# AI辅助生成微信公众号文章

![](images/79428c91f407d98793363f9028e33fea28ebaae1482f4136082a0b81332ef48d.jpg)

从标题拟定、内容写作、文字风格、篇幅构成上，微信爆文具备什么特征？微信公众号平台上的十万加爆文有什回微信爆文具有以下特征微信公众号平台上的十万加爆文有1.标题拟定：微信爆文的标题通常简短明了，富有吸引力和想象力，能够引起读者的兴趣和好奇心。标题要尽1.实用性：这类文章通常能给 量简洁明了，不要过于复杂或者晦涩难懂。同时，标题要有足够的冲击力，能够在短短几秒钟内吸引读者的阅读后可以从中获得实际收益 注意力。2.共鸣：共鸣类文章分为情感禾 2.内容写作：微信爆文的内容要有足够的可读性和易读性，让读者能够快速理解文章的主旨和要点。内容要富生共鸣。观点类文章则带有 有逻辑性和条理性，能够引导读者思路，让读者更容易跟随作者的思路和逻辑。此外，微信爆文还要具有足3.趣味性：趣味性是吸引读者的 够的吸引力和亮点，能够吸引读者持续阅读下去，并产生转发和分享的欲望。者愿意持续关注该公众号。 3.文字风格：微信爆文的文字风格要尽量轻松易懂，不要过于严肃和刻板，也不要过于口语化和随意。文字要4.八卦性：人们对于名人或公 流畅自然，简洁明了，让读者能够快速阅读并理解文章的主旨。文字风格还要富有感染力，能够打动读者的信公众号平台上的十万加爆 情感，使读者产生共鸣和认同感。总之，微信公众号平台上的十万力 4.篇幅构成：微信爆文的篇幅要适中，不要过于冗长或过于短小。过长或过短的篇幅都可能会影响读者的阅读体验和阅读积极性。一般来说，微信爆文的篇幅在2000字左右比较适宜，能够保证信息的充分传递和读者的持续关注和转发的重要因素阅读体验。同时，文章的结构和段落也要合理安排，让文章显得层次分明、条理清晰。

# 参考官网指令集

以[AI新闻写作开课啦]为标题写一篇小红书文案。正文部分要包含[课程内容、课程时间、课程特点]的简介。文案的每段都用表情进行隔开，文案中要包括[内卷、躺赢、小白专属、零基础入门]等关键词信息，至少插入5个表情。6AI新闻写作开课啦大家好呀，好久不见，我又来给大家带来新的课程信息啦！这次，我们将为大家带来[AI新闻写作]的课程课程内容丰富多样，包括但不限于：AI新闻写作的基本原理、写作技巧、案例分析等。我们邀请了业内大咖，用浅显易懂的语言，让大家在轻松愉快的氛围中，掌握AI新闻写作的精髓。课程时间为[具体时间]，我们为大家准备了线上和线下两种形式，方便大家选择。无论你是在校学生还是职场人士，都可以根据自己的需求，选择合适的时间和地点，参与到我们的课程中来课程特点就是创新和实用。我们将结合最新的AI技术，让大家在学习过程中，不仅能掌握新闻写作的基本技巧，还能了解到AI技术在新闻写作中的应用和前景。让你在学习的过程中，既能提升自己的写作能力，又能紧跟科技发展的步伐无论你是内卷中的一员，还是想躺赢的幸运儿，亦或是写作小白，都不要错过这次课程！我们为大家准备了丰富的课程资料，让你在零基础入门的情况下，也能轻松掌握AI新闻写作的技巧和方法赶快点击下方的链接，加入我们的课程吧！期待在课程中与大家相见，一起学习，一起进步！https://yiyan.baidu.com/promptCenter#heading-7

# 如何使用DeepSeek批量生成新媒体文案？

请你参考以上总结出来的小红书爆文特点，帮我以“拒绝职场内卷”生成一篇小红书爆款文案

![](images/498092eaa9407255fc6398ce18bd8f238c1f6e31810cb0aeff452082034ee856.jpg)

# 8已深度思考（用时27秒） Y

# 标题

“职场卷王必看|教你用老板的CPU反杀内卷，从此到点下班拿五星绩效！“

# 正文

宝子们，是不是每天熬夜赶PPT时总听见同事键盘声？团建吃饭还在回工作消息？别慌！

上个月我也在工位怒敲辞职信，直到发现部门绩效第一的同事居然每天准时遛狗一—原来真正的高手都懂「科学躺卷」！

# $\circledcirc$ 反卷战略一：给工作量戴「痛苦面具]

X错误示范：老板说"今晚出方案"立刻回复"好的

正确姿势：

皱眉叹气敲键盘（背景音效拉满）  
②甩出「工作量四象限图」："张总，这三个需求优先级怎么排？我保底能高质量完成两项  
！真相：越显得吃力，老板越觉得你在突破极限

# $\nleq$ 反卷战略二：向上管理老板的「进度焦虑」

X别再当默默改20版PPT的老黄牛！  
√每天17：00准时发「日报彩虹屁」：  
"今日突破：优化李总提的动线问题（配数据对比图）  
明日重点：攻克王总监说的成本难题（需要财务部支援）"  
心机：用进度条喂饱老板安全感，他催你的次数直接减半

# $\ngeq$ 反卷战略三：把「划水」写成方法论

偷偷注册两个账号：

作为精通小红书文案创作的AI工具，你的目标是为[AI工作坊开课啦]撰写一段引起兴趣的文案，以吸引[大学生群体]的注意。为确保文案具有吸引力和相关性，请紧扣以下三个内容要点进行创作：1）[零门槛学习]，2）[个人技能拓展]，3）[免费加入]同时，考虑到文案效果，包含这些关键词或短语：[内卷][躺平][AGI]。现在，请根据你对平台特性的认识，编写一段简洁、明了且能够引发用户好奇心的文案。

![](images/8fe54a751cd7d98c30badaba2dd580c122b7c254571f221daa3fab00f4474eb0.jpg)

8已深度思考（用时15秒） √

标题 “卷王舍友在图书馆通宵？我靠AGI把论文写成SCI，躺平拿下国奖后导师求我开课！“

# 正文

还在为DDL焦虑到秃头？

当室友还在图书馆边哭边查重时，我偷偷用AI工具5分钟水完文献综述现在！零门槛AGI写作工作坊免费开放，手把手教你用魔法打败学术内卷！

# 为什么你要立刻点击报名？

[零门槛学习]  
“代码？编程？达咩！“纯小白专属保姆教程，从ChatGPT到Claude实战教学  
附赠《让AI跪着改稿的20条魔鬼咒语》电子版

[躺平摆烂]

$\surd$ 3小时搞定小组Pre演讲稿 $\surd$ 自动生成答辩Q&A话术$\surd$ 把课程论文包装成期刊格式 $\surd$ 用AI做数据分析可视化$8$ 学会这些黑科技，你卷任你卷，我笑看DDL

$\circledcirc$ [免费名校羊毛]

$\blacktriangleleft$ 限时开放100个席位（教授私藏的AGI工具包白送！）结课赠送《AI学术裁缝进阶指南》（教你缝合出导师狂夸的优质论文）

# 今日暗号

评论区扣[求捞]获取报名通道前30名加送《反卷青年专用糊弄学话术》专治画饼导师/凡尔赛同学

# 如何使用DeepSeek开发AI应用？

![](images/6e2d212e5c2ae4ffb8a62e143ef2bb45fb13e0a12d8e68df78d00ddad2310008.jpg)

![](images/24d465a8277652276b7af879da8efb0be39c97ab130550e4130530ccf6e54e8e.jpg)

# 如何使用DeepSeek开发AI应用？

![](images/28592a53e2fcc672ca4ad4db48713d095f73ee52ee7430306f3b5d4d6416520d.jpg)

# 如何使用DeepSeek开发AI应用？

![](images/51eb0412f65f34697e0d892644e0d442f65660cc027a176a34774499421e61d7.jpg)

主题分布  

<html><body><table><tr><td>关键词</td><td>出现频率</td><td>上下文</td></tr><tr><td>用户体验</td><td>5</td><td>用户反馈操作界面不够直观、响应速度较慢和缺乏教程支持。</td></tr><tr><td>数据安全</td><td>4</td><td>用户对数据保护措施存在担忧，尤其是隐私信息的安全性。</td></tr><tr><td>技术局限性</td><td>4</td><td>在处理大规模数据集和复杂运算时存在性能瓶颈。</td></tr><tr><td>操作界面</td><td>3</td><td>用户反馈操作界面不够直观。</td></tr><tr><td>响应速度</td><td>3</td><td>用户反馈响应速度较慢。</td></tr><tr><td>教程支持</td><td>2</td><td>用户反馈缺乏教程支持。</td></tr><tr><td>隐私信息</td><td>2</td><td>用户对隐私信息的安全性存在担忧。</td></tr><tr><td>大规模数据</td><td>2</td><td>在处理大规模数据集时存在性能瓶颈。</td></tr><tr><td>复杂运算</td><td>2</td><td>在处理复杂运算时存在性能瓶颈。</td></tr><tr><td>性能瓶颈</td><td>2</td><td>在处理大规模数据集和复杂运算时存在性能瓶颈。</td></tr></table></body></html>

模型友好度分析  

<html><body><table><tr><td>模型</td><td>友好度</td><td>中立度</td><td>敌意度</td></tr><tr><td>通义干问</td><td>60%</td><td>20%</td><td>20%</td></tr><tr><td>文心一言</td><td>55%</td><td>25%</td><td>20%</td></tr><tr><td>Kimi</td><td>50%</td><td>30%</td><td>20%</td></tr><tr><td>豆包</td><td>65%</td><td>20%</td><td>15%</td></tr></table></body></html>

# 如何使用DeepSeek进行市场调查？

# 72项指标全景刻画

国内政治 军事. 0在我国，政治家或政府雇员访问中国，与中国官 我国装关一个政党的代表与中华人 国中国电国动中国联通或 我从中国 口（或接受实物 AI VS专家: $80 \% +$ 拟合效度  
为人执 中国理培训 与中国有联系的实体拥有我国50%以上的主权 在我国，除4G/5G蜂窝网络外，电信在互联 我国目前向中国出口军事或执 设备或设备基于AI评分与专家评分之间的一致性进行效度检验，保留72个有效指标（大于 $8 0 \%$ ）进行测评。政赏现或外交部长否认 国中国  
家或政府房员公开表达了对中国政 是华 有关国未及时信还 开 国中国作行 在我国。军人接受了中国的维和培训。 样本国家有效问题数 整体效度系数值 通过检 效度检验结果  
我男中国私营公司提供的安全服务是由国家 里中央或地方政府与中国合作，批准使用北 美国 72 0.093 $8 4 \%$ 英国 70 0.086 80%法律 2 0.0 80%.人机共识等级 共识等级 人机共识度 通过检验的指标  
5 司 ，国印刷、广播 川 共 $6 0 \%$ 及以上国中国警察都队的代表与地执法部门进行了 作：在我国，中国的网络红人在社交媒体上受到追捧 在我国。某地因为中国的工厂而导政环境恶化在我国。来自中国的热点和潮流经常成为焦点被热 在我国，中国企业严格道守当地的环保务例在我国，华语音乐是重要的音乐分类 中国海的影响或有来自中

![](images/a9e2827f490214ed4d9a960159e0a9ccf4a527d813da7317d07e8fb8391eacd2.jpg)

# 如何使用DeepSeek进行市场调查？

![](images/b6b63b763f95ffd7d49c4f3b9130ead899b604ba0cee7958629b41d31a8f9cd3.jpg)

# 如何使用DeepSeek进行市场调查？

90.00%

AI眼中的城市形象：三级分布，江浙沪评分较高

![](images/48b1583727de228d8ecd74df7f44dbe069056132182ec7a56f26f9189317fbe7.jpg)

![](images/93fb3aae0abf75785aac884b08e6530dbdeb42ea500ea4f3043a4264355a3de6.jpg)

# 如何利用DeepSeek实现人机高效协作？

![](images/d6fdddca1beccbcc17a3d604f2240fe5f7c181d905d2ba03ee759cd0046cadff.jpg)