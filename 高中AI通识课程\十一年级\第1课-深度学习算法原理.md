# 第1课：深度学习算法原理

## 🎯 课程基本信息

- **课程名称**：深度学习算法原理
- **适用年级**：高中十一年级
- **课时安排**：90分钟（2课时）
- **课程类型**：算法深化课
- **核心主题**：深度学习算法的数学原理与优化方法

## 📚 教学目标

### 认知目标
- 深入理解深度学习的数学基础和理论原理
- 掌握反向传播算法的推导过程和计算细节
- 认识各种优化算法的设计思想和适用场景
- 了解深度学习中的正则化和泛化理论

### 技能目标
- 能够从数学角度分析和设计深度学习算法
- 掌握手工实现反向传播和优化算法的方法
- 学会调试和优化深度学习模型的训练过程
- 能够根据问题特点选择合适的算法和参数

### 思维目标
- 培养严谨的数学思维和逻辑推理能力
- 发展从理论到实践的抽象思维
- 建立算法设计和优化的系统性思维
- 培养科学研究和创新探索精神

### 价值观目标
- 树立追求真理和严谨治学的态度
- 培养对数学之美的欣赏和敬畏
- 增强攻克技术难题的信心和毅力
- 建立理论联系实际的学习观念

## 🎯 教学流程

### 第一课时（45分钟）

#### 导入环节（10分钟）
**数学之美展示**：
- 展示神经网络训练过程的损失函数下降曲线
- 可视化梯度下降在高维空间中的优化路径
- 演示不同优化算法的收敛行为差异

**核心问题**：
- "深度学习的'学习'本质上是什么数学过程？"
- "为什么梯度下降能够找到最优解？"
- "如何从数学角度理解深度网络的表达能力？"

#### 新课讲授（25分钟）

##### 1. 深度学习的数学基础（15分钟）
**函数逼近理论**：
```python
import numpy as np
import matplotlib.pyplot as plt

class UniversalApproximation:
    """万能逼近定理演示"""
    
    def __init__(self):
        self.target_function = lambda x: np.sin(2*np.pi*x) + 0.5*np.sin(4*np.pi*x)
    
    def sigmoid(self, x):
        """Sigmoid激活函数"""
        return 1 / (1 + np.exp(-np.clip(x, -500, 500)))
    
    def single_hidden_layer_network(self, x, weights, biases, output_weights):
        """单隐藏层神经网络"""
        # 隐藏层计算
        hidden = self.sigmoid(np.dot(x.reshape(-1, 1), weights.reshape(1, -1)) + biases)
        # 输出层计算
        output = np.dot(hidden, output_weights)
        return output
    
    def demonstrate_approximation(self, num_neurons_list=[5, 10, 20, 50]):
        """演示不同神经元数量的逼近效果"""
        x = np.linspace(0, 1, 1000)
        target_y = self.target_function(x)
        
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))
        axes = axes.flatten()
        
        for i, num_neurons in enumerate(num_neurons_list):
            # 随机初始化网络参数
            np.random.seed(42)
            weights = np.random.randn(num_neurons) * 2
            biases = np.random.randn(num_neurons) * 2
            output_weights = np.random.randn(num_neurons) * 0.1
            
            # 计算网络输出
            network_y = self.single_hidden_layer_network(x, weights, biases, output_weights)
            
            # 绘制结果
            axes[i].plot(x, target_y, 'b-', label='目标函数', linewidth=2)
            axes[i].plot(x, network_y, 'r--', label=f'神经网络({num_neurons}个神经元)', linewidth=2)
            axes[i].set_title(f'神经元数量: {num_neurons}')
            axes[i].legend()
            axes[i].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.show()
        
        return fig

# 演示万能逼近定理
approximator = UniversalApproximation()
approximator.demonstrate_approximation()
```

**损失函数和优化目标**：
```python
class LossFunctions:
    """损失函数的数学原理"""
    
    @staticmethod
    def mean_squared_error(y_true, y_pred):
        """均方误差损失"""
        return np.mean((y_true - y_pred) ** 2)
    
    @staticmethod
    def cross_entropy_loss(y_true, y_pred):
        """交叉熵损失"""
        # 避免log(0)
        y_pred = np.clip(y_pred, 1e-15, 1 - 1e-15)
        return -np.mean(y_true * np.log(y_pred) + (1 - y_true) * np.log(1 - y_pred))
    
    @staticmethod
    def huber_loss(y_true, y_pred, delta=1.0):
        """Huber损失（对异常值鲁棒）"""
        error = y_true - y_pred
        is_small_error = np.abs(error) <= delta
        squared_loss = 0.5 * error ** 2
        linear_loss = delta * np.abs(error) - 0.5 * delta ** 2
        return np.where(is_small_error, squared_loss, linear_loss)
    
    def visualize_loss_functions(self):
        """可视化不同损失函数的特性"""
        errors = np.linspace(-3, 3, 1000)
        
        mse_loss = 0.5 * errors ** 2
        mae_loss = np.abs(errors)
        huber_loss = self.huber_loss(np.zeros_like(errors), errors, delta=1.0)
        
        plt.figure(figsize=(10, 6))
        plt.plot(errors, mse_loss, label='MSE Loss', linewidth=2)
        plt.plot(errors, mae_loss, label='MAE Loss', linewidth=2)
        plt.plot(errors, huber_loss, label='Huber Loss (δ=1)', linewidth=2)
        
        plt.xlabel('预测误差')
        plt.ylabel('损失值')
        plt.title('不同损失函数的比较')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.show()

# 可视化损失函数
loss_viz = LossFunctions()
loss_viz.visualize_loss_functions()
```

##### 2. 反向传播算法详解（10分钟）
**链式法则的应用**：
```python
class BackpropagationDemo:
    """反向传播算法详细实现"""
    
    def __init__(self, input_size, hidden_size, output_size):
        # 初始化权重和偏置
        self.W1 = np.random.randn(input_size, hidden_size) * 0.1
        self.b1 = np.zeros((1, hidden_size))
        self.W2 = np.random.randn(hidden_size, output_size) * 0.1
        self.b2 = np.zeros((1, output_size))
        
        # 存储前向传播的中间结果
        self.z1 = None
        self.a1 = None
        self.z2 = None
        self.a2 = None
    
    def sigmoid(self, x):
        """Sigmoid激活函数"""
        return 1 / (1 + np.exp(-np.clip(x, -500, 500)))
    
    def sigmoid_derivative(self, x):
        """Sigmoid函数的导数"""
        s = self.sigmoid(x)
        return s * (1 - s)
    
    def forward_pass(self, X):
        """前向传播"""
        # 第一层
        self.z1 = np.dot(X, self.W1) + self.b1
        self.a1 = self.sigmoid(self.z1)
        
        # 第二层
        self.z2 = np.dot(self.a1, self.W2) + self.b2
        self.a2 = self.sigmoid(self.z2)
        
        return self.a2
    
    def backward_pass(self, X, y, learning_rate=0.01):
        """反向传播"""
        m = X.shape[0]  # 样本数量
        
        # 输出层梯度
        dz2 = self.a2 - y
        dW2 = (1/m) * np.dot(self.a1.T, dz2)
        db2 = (1/m) * np.sum(dz2, axis=0, keepdims=True)
        
        # 隐藏层梯度
        da1 = np.dot(dz2, self.W2.T)
        dz1 = da1 * self.sigmoid_derivative(self.z1)
        dW1 = (1/m) * np.dot(X.T, dz1)
        db1 = (1/m) * np.sum(dz1, axis=0, keepdims=True)
        
        # 参数更新
        self.W2 -= learning_rate * dW2
        self.b2 -= learning_rate * db2
        self.W1 -= learning_rate * dW1
        self.b1 -= learning_rate * db1
        
        return {
            'dW2': dW2, 'db2': db2,
            'dW1': dW1, 'db1': db1
        }
    
    def compute_loss(self, y_true, y_pred):
        """计算损失"""
        return np.mean((y_true - y_pred) ** 2)
    
    def train_step_by_step(self, X, y, num_epochs=100):
        """逐步训练并记录梯度变化"""
        loss_history = []
        gradient_norms = []
        
        for epoch in range(num_epochs):
            # 前向传播
            y_pred = self.forward_pass(X)
            
            # 计算损失
            loss = self.compute_loss(y, y_pred)
            loss_history.append(loss)
            
            # 反向传播
            gradients = self.backward_pass(X, y)
            
            # 记录梯度范数
            grad_norm = np.sqrt(np.sum(gradients['dW1']**2) + np.sum(gradients['dW2']**2))
            gradient_norms.append(grad_norm)
            
            if epoch % 20 == 0:
                print(f"Epoch {epoch}: Loss = {loss:.6f}, Grad Norm = {grad_norm:.6f}")
        
        return loss_history, gradient_norms

# 演示反向传播
np.random.seed(42)
X = np.random.randn(100, 2)
y = (X[:, 0] + X[:, 1] > 0).astype(float).reshape(-1, 1)

network = BackpropagationDemo(input_size=2, hidden_size=5, output_size=1)
loss_history, grad_norms = network.train_step_by_step(X, y, num_epochs=200)

# 可视化训练过程
fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))

ax1.plot(loss_history)
ax1.set_title('损失函数变化')
ax1.set_xlabel('训练轮次')
ax1.set_ylabel('损失值')
ax1.grid(True, alpha=0.3)

ax2.plot(grad_norms)
ax2.set_title('梯度范数变化')
ax2.set_xlabel('训练轮次')
ax2.set_ylabel('梯度范数')
ax2.grid(True, alpha=0.3)

plt.tight_layout()
plt.show()
```

#### 实践体验（10分钟）
**手工计算反向传播**：
学生分组进行简单神经网络的手工反向传播计算

### 第二课时（45分钟）

#### 深入分析（20分钟）

##### 1. 优化算法原理（12分钟）
**梯度下降算法族**：
```python
class OptimizationAlgorithms:
    """优化算法的实现和比较"""
    
    def __init__(self):
        self.optimizers = {}
    
    def sgd(self, params, grads, learning_rate=0.01):
        """随机梯度下降"""
        for param, grad in zip(params, grads):
            param -= learning_rate * grad
        return params
    
    def momentum(self, params, grads, velocities, learning_rate=0.01, momentum=0.9):
        """动量法"""
        for i, (param, grad) in enumerate(zip(params, grads)):
            velocities[i] = momentum * velocities[i] - learning_rate * grad
            param += velocities[i]
        return params, velocities
    
    def adam(self, params, grads, m, v, t, learning_rate=0.001, beta1=0.9, beta2=0.999, epsilon=1e-8):
        """Adam优化器"""
        t += 1
        
        for i, (param, grad) in enumerate(zip(params, grads)):
            # 更新一阶矩估计
            m[i] = beta1 * m[i] + (1 - beta1) * grad
            # 更新二阶矩估计
            v[i] = beta2 * v[i] + (1 - beta2) * (grad ** 2)
            
            # 偏差修正
            m_hat = m[i] / (1 - beta1 ** t)
            v_hat = v[i] / (1 - beta2 ** t)
            
            # 参数更新
            param -= learning_rate * m_hat / (np.sqrt(v_hat) + epsilon)
        
        return params, m, v, t
    
    def compare_optimizers(self, objective_function, initial_params, num_iterations=1000):
        """比较不同优化器的性能"""
        
        # 定义测试函数：Rosenbrock函数
        def rosenbrock(x, y):
            return (1 - x)**2 + 100 * (y - x**2)**2
        
        def rosenbrock_grad(x, y):
            dx = -2 * (1 - x) - 400 * x * (y - x**2)
            dy = 200 * (y - x**2)
            return np.array([dx, dy])
        
        # 初始化参数
        x_sgd = np.array([-1.0, 1.0])
        x_momentum = np.array([-1.0, 1.0])
        x_adam = np.array([-1.0, 1.0])
        
        # 动量法状态
        velocity = np.zeros_like(x_momentum)
        
        # Adam状态
        m_adam = np.zeros_like(x_adam)
        v_adam = np.zeros_like(x_adam)
        t_adam = 0
        
        # 记录优化路径
        path_sgd = [x_sgd.copy()]
        path_momentum = [x_momentum.copy()]
        path_adam = [x_adam.copy()]
        
        for i in range(num_iterations):
            # SGD
            grad_sgd = rosenbrock_grad(x_sgd[0], x_sgd[1])
            x_sgd = self.sgd([x_sgd], [grad_sgd], learning_rate=0.001)[0]
            path_sgd.append(x_sgd.copy())
            
            # Momentum
            grad_momentum = rosenbrock_grad(x_momentum[0], x_momentum[1])
            x_momentum, velocity = self.momentum([x_momentum], [grad_momentum], [velocity], 
                                               learning_rate=0.001, momentum=0.9)
            x_momentum = x_momentum[0]
            velocity = velocity[0]
            path_momentum.append(x_momentum.copy())
            
            # Adam
            grad_adam = rosenbrock_grad(x_adam[0], x_adam[1])
            x_adam, m_adam, v_adam, t_adam = self.adam([x_adam], [grad_adam], [m_adam], [v_adam], t_adam)
            x_adam = x_adam[0]
            m_adam = m_adam[0]
            v_adam = v_adam[0]
            path_adam.append(x_adam.copy())
        
        return {
            'sgd': np.array(path_sgd),
            'momentum': np.array(path_momentum),
            'adam': np.array(path_adam)
        }
    
    def visualize_optimization_paths(self, paths):
        """可视化优化路径"""
        # 创建Rosenbrock函数的等高线图
        x = np.linspace(-2, 2, 100)
        y = np.linspace(-1, 3, 100)
        X, Y = np.meshgrid(x, y)
        Z = (1 - X)**2 + 100 * (Y - X**2)**2
        
        plt.figure(figsize=(12, 8))
        
        # 绘制等高线
        contour = plt.contour(X, Y, Z, levels=np.logspace(0, 3, 20), alpha=0.6)
        plt.colorbar(contour)
        
        # 绘制优化路径
        colors = ['red', 'blue', 'green']
        labels = ['SGD', 'Momentum', 'Adam']
        
        for (name, path), color, label in zip(paths.items(), colors, labels):
            plt.plot(path[:, 0], path[:, 1], color=color, label=label, linewidth=2, alpha=0.8)
            plt.scatter(path[0, 0], path[0, 1], color=color, s=100, marker='o')  # 起点
            plt.scatter(path[-1, 0], path[-1, 1], color=color, s=100, marker='*')  # 终点
        
        plt.scatter(1, 1, color='black', s=200, marker='x', label='全局最优解')
        plt.xlabel('x')
        plt.ylabel('y')
        plt.title('不同优化算法的收敛路径比较')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.show()

# 比较优化算法
optimizer_demo = OptimizationAlgorithms()
paths = optimizer_demo.compare_optimizers(None, None, num_iterations=500)
optimizer_demo.visualize_optimization_paths(paths)
```

##### 2. 正则化和泛化理论（8分钟）
**偏差-方差权衡**：
```python
class BiasVarianceAnalysis:
    """偏差-方差分解分析"""
    
    def __init__(self):
        self.true_function = lambda x: 0.5 * x + 0.3 * np.sin(2 * np.pi * x)
        self.noise_std = 0.1
    
    def generate_dataset(self, n_samples=50, noise_std=None):
        """生成数据集"""
        if noise_std is None:
            noise_std = self.noise_std
            
        x = np.random.uniform(0, 1, n_samples)
        y_true = self.true_function(x)
        y_noisy = y_true + np.random.normal(0, noise_std, n_samples)
        
        return x, y_noisy, y_true
    
    def polynomial_fit(self, x_train, y_train, degree):
        """多项式拟合"""
        coeffs = np.polyfit(x_train, y_train, degree)
        return lambda x: np.polyval(coeffs, x)
    
    def bias_variance_decomposition(self, n_experiments=100, n_samples=50):
        """偏差-方差分解"""
        x_test = np.linspace(0, 1, 100)
        y_true_test = self.true_function(x_test)
        
        degrees = [1, 3, 9, 15]
        results = {}
        
        for degree in degrees:
            predictions = []
            
            # 多次实验
            for _ in range(n_experiments):
                x_train, y_train, _ = self.generate_dataset(n_samples)
                model = self.polynomial_fit(x_train, y_train, degree)
                y_pred = model(x_test)
                predictions.append(y_pred)
            
            predictions = np.array(predictions)
            
            # 计算偏差和方差
            mean_prediction = np.mean(predictions, axis=0)
            bias_squared = np.mean((mean_prediction - y_true_test) ** 2)
            variance = np.mean(np.var(predictions, axis=0))
            noise = self.noise_std ** 2
            
            results[degree] = {
                'bias_squared': bias_squared,
                'variance': variance,
                'noise': noise,
                'total_error': bias_squared + variance + noise,
                'predictions': predictions,
                'mean_prediction': mean_prediction
            }
        
        return results, x_test, y_true_test
    
    def visualize_bias_variance(self, results, x_test, y_true_test):
        """可视化偏差-方差权衡"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        axes = axes.flatten()
        
        degrees = list(results.keys())
        
        for i, degree in enumerate(degrees):
            ax = axes[i]
            
            # 绘制真实函数
            ax.plot(x_test, y_true_test, 'k-', linewidth=3, label='真实函数')
            
            # 绘制多次预测结果
            predictions = results[degree]['predictions']
            for j in range(min(20, predictions.shape[0])):
                ax.plot(x_test, predictions[j], 'b-', alpha=0.1, linewidth=1)
            
            # 绘制平均预测
            ax.plot(x_test, results[degree]['mean_prediction'], 'r-', 
                   linewidth=2, label='平均预测')
            
            # 设置标题和标签
            bias_sq = results[degree]['bias_squared']
            variance = results[degree]['variance']
            total_error = results[degree]['total_error']
            
            ax.set_title(f'多项式度数: {degree}\n'
                        f'偏差²: {bias_sq:.4f}, 方差: {variance:.4f}\n'
                        f'总误差: {total_error:.4f}')
            ax.set_xlabel('x')
            ax.set_ylabel('y')
            ax.legend()
            ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.show()
        
        # 绘制偏差-方差权衡图
        plt.figure(figsize=(10, 6))
        
        degrees = list(results.keys())
        bias_squared = [results[d]['bias_squared'] for d in degrees]
        variance = [results[d]['variance'] for d in degrees]
        total_error = [results[d]['total_error'] for d in degrees]
        
        plt.plot(degrees, bias_squared, 'o-', label='偏差²', linewidth=2, markersize=8)
        plt.plot(degrees, variance, 's-', label='方差', linewidth=2, markersize=8)
        plt.plot(degrees, total_error, '^-', label='总误差', linewidth=2, markersize=8)
        
        plt.xlabel('模型复杂度（多项式度数）')
        plt.ylabel('误差')
        plt.title('偏差-方差权衡')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.show()

# 演示偏差-方差分解
bias_var_demo = BiasVarianceAnalysis()
results, x_test, y_true_test = bias_var_demo.bias_variance_decomposition()
bias_var_demo.visualize_bias_variance(results, x_test, y_true_test)
```

#### 算法实现（15分钟）

##### 从零实现深度学习框架
**微型深度学习框架**：
```python
class MiniDeepLearning:
    """微型深度学习框架"""
    
    class Tensor:
        """张量类，支持自动微分"""
        
        def __init__(self, data, requires_grad=False):
            self.data = np.array(data, dtype=np.float32)
            self.requires_grad = requires_grad
            self.grad = None
            self.grad_fn = None
            
        def backward(self, grad=None):
            """反向传播"""
            if grad is None:
                grad = np.ones_like(self.data)
                
            if self.grad is None:
                self.grad = grad
            else:
                self.grad += grad
                
            if self.grad_fn is not None:
                self.grad_fn(grad)
        
        def __add__(self, other):
            """加法运算"""
            if isinstance(other, MiniDeepLearning.Tensor):
                result = MiniDeepLearning.Tensor(self.data + other.data, 
                                               requires_grad=self.requires_grad or other.requires_grad)
            else:
                result = MiniDeepLearning.Tensor(self.data + other, requires_grad=self.requires_grad)
            
            def grad_fn(grad):
                if self.requires_grad:
                    self.backward(grad)
                if isinstance(other, MiniDeepLearning.Tensor) and other.requires_grad:
                    other.backward(grad)
            
            result.grad_fn = grad_fn
            return result
        
        def __mul__(self, other):
            """乘法运算"""
            if isinstance(other, MiniDeepLearning.Tensor):
                result = MiniDeepLearning.Tensor(self.data * other.data,
                                               requires_grad=self.requires_grad or other.requires_grad)
                
                def grad_fn(grad):
                    if self.requires_grad:
                        self.backward(grad * other.data)
                    if other.requires_grad:
                        other.backward(grad * self.data)
            else:
                result = MiniDeepLearning.Tensor(self.data * other, requires_grad=self.requires_grad)
                
                def grad_fn(grad):
                    if self.requires_grad:
                        self.backward(grad * other)
            
            result.grad_fn = grad_fn
            return result
        
        def matmul(self, other):
            """矩阵乘法"""
            result = MiniDeepLearning.Tensor(np.dot(self.data, other.data),
                                           requires_grad=self.requires_grad or other.requires_grad)
            
            def grad_fn(grad):
                if self.requires_grad:
                    self.backward(np.dot(grad, other.data.T))
                if other.requires_grad:
                    other.backward(np.dot(self.data.T, grad))
            
            result.grad_fn = grad_fn
            return result
        
        def sigmoid(self):
            """Sigmoid激活函数"""
            sigmoid_data = 1 / (1 + np.exp(-np.clip(self.data, -500, 500)))
            result = MiniDeepLearning.Tensor(sigmoid_data, requires_grad=self.requires_grad)
            
            def grad_fn(grad):
                if self.requires_grad:
                    sigmoid_grad = sigmoid_data * (1 - sigmoid_data)
                    self.backward(grad * sigmoid_grad)
            
            result.grad_fn = grad_fn
            return result
    
    class Linear:
        """线性层"""
        
        def __init__(self, in_features, out_features):
            self.weight = MiniDeepLearning.Tensor(
                np.random.randn(in_features, out_features) * 0.1, 
                requires_grad=True
            )
            self.bias = MiniDeepLearning.Tensor(
                np.zeros((1, out_features)), 
                requires_grad=True
            )
        
        def forward(self, x):
            return x.matmul(self.weight) + self.bias
        
        def parameters(self):
            return [self.weight, self.bias]
    
    class MLP:
        """多层感知机"""
        
        def __init__(self, input_size, hidden_size, output_size):
            self.layer1 = MiniDeepLearning.Linear(input_size, hidden_size)
            self.layer2 = MiniDeepLearning.Linear(hidden_size, output_size)
        
        def forward(self, x):
            x = self.layer1.forward(x)
            x = x.sigmoid()
            x = self.layer2.forward(x)
            return x.sigmoid()
        
        def parameters(self):
            return self.layer1.parameters() + self.layer2.parameters()

# 使用微型框架训练网络
def train_mini_network():
    # 创建数据
    X = MiniDeepLearning.Tensor(np.random.randn(100, 2))
    y = MiniDeepLearning.Tensor((X.data[:, 0] + X.data[:, 1] > 0).astype(float).reshape(-1, 1))
    
    # 创建网络
    model = MiniDeepLearning.MLP(2, 5, 1)
    
    # 训练
    learning_rate = 0.1
    losses = []
    
    for epoch in range(100):
        # 前向传播
        y_pred = model.forward(X)
        
        # 计算损失
        loss_data = np.mean((y_pred.data - y.data) ** 2)
        losses.append(loss_data)
        
        # 清零梯度
        for param in model.parameters():
            param.grad = None
        
        # 反向传播
        loss_tensor = MiniDeepLearning.Tensor(loss_data)
        grad = 2 * (y_pred.data - y.data) / len(y.data)
        y_pred.backward(grad)
        
        # 更新参数
        for param in model.parameters():
            if param.grad is not None:
                param.data -= learning_rate * param.grad
        
        if epoch % 20 == 0:
            print(f"Epoch {epoch}: Loss = {loss_data:.6f}")
    
    return losses

# 训练并可视化
losses = train_mini_network()
plt.figure(figsize=(10, 6))
plt.plot(losses)
plt.title('微型深度学习框架训练过程')
plt.xlabel('训练轮次')
plt.ylabel('损失值')
plt.grid(True, alpha=0.3)
plt.show()
```

#### 总结反思（10分钟）
**核心要点回顾**：
- 深度学习本质上是高维空间中的函数优化问题
- 反向传播是链式法则在计算图上的应用
- 不同优化算法有各自的适用场景和特点
- 偏差-方差权衡是理解模型泛化能力的关键

## 📊 评估方式

### 过程性评价
- **数学理解**：对算法数学原理的掌握程度
- **推导能力**：独立推导算法公式的能力
- **实现能力**：从零实现算法的编程能力
- **分析能力**：分析算法性能和适用性的能力

### 结果性评价
- **算法实现**：完成深度学习算法的完整实现
- **数学推导**：完成反向传播算法的数学推导
- **性能分析**：分析不同优化算法的性能特点
- **创新改进**：提出算法改进的想法和实现

## 🏠 课后延伸

### 基础任务
1. **算法推导**：完成多层神经网络反向传播的完整数学推导
2. **代码实现**：从零实现一个支持自动微分的深度学习框架
3. **性能对比**：比较不同优化算法在具体问题上的性能

### 拓展任务
1. **算法改进**：设计并实现一个新的优化算法
2. **理论分析**：分析深度网络的表达能力和泛化性能
3. **应用研究**：将算法应用到具体的机器学习问题

### 预习任务
了解多模态AI系统的基本概念，思考如何融合不同类型的数据。

---

*本课程旨在帮助十一年级学生深入理解深度学习的数学原理，培养严谨的算法思维和实现能力。*
