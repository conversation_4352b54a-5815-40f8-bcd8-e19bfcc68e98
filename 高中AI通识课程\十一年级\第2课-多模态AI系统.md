# 第2课：多模态AI系统

## 🎯 课程基本信息

- **课程名称**：多模态AI系统
- **适用年级**：高中十一年级
- **课时安排**：90分钟（2课时）
- **课程类型**：算法深化课
- **核心主题**：多模态数据融合与跨模态学习技术

## 📚 教学目标

### 认知目标
- 理解多模态AI系统的基本概念和技术架构
- 掌握不同模态数据的特征提取和表示方法
- 认识跨模态学习的核心算法和实现原理
- 了解多模态融合的策略和评估方法

### 技能目标
- 能够设计和实现多模态数据处理管道
- 掌握视觉-语言模型的构建和训练方法
- 学会使用注意力机制进行模态间信息融合
- 能够评估和优化多模态系统的性能

### 思维目标
- 培养跨模态和跨领域的融合思维
- 发展系统性的多模态架构设计思维
- 建立数据驱动的模态选择和融合策略
- 培养创新性的多模态应用设计能力

### 价值观目标
- 认识多模态AI在促进人机交互中的价值
- 培养对技术包容性和可访问性的关注
- 增强跨文化和跨语言理解的意识
- 建立负责任的AI开发理念

## 🎯 教学流程

### 第一课时（45分钟）

#### 导入环节（10分钟）
**多模态体验**：
- 展示GPT-4V、DALL-E等多模态AI系统的应用案例
- 演示图像描述生成、视觉问答等任务
- 体验语音识别+自然语言处理的组合应用

**核心问题**：
- "人类是如何同时处理视觉、听觉、触觉等多种信息的？"
- "AI系统如何模拟人类的多感官信息处理能力？"
- "不同模态的信息如何有效融合？"

#### 新课讲授（25分钟）

##### 1. 多模态AI基础概念（12分钟）
**模态定义与分类**：
```python
import numpy as np
import matplotlib.pyplot as plt
from sklearn.decomposition import PCA
from sklearn.manifold import TSNE
import torch
import torch.nn as nn
import torch.nn.functional as F

class ModalityAnalysis:
    """多模态数据分析和可视化"""
    
    def __init__(self):
        self.modalities = {
            'text': {'dim': 768, 'type': 'discrete'},
            'image': {'dim': 2048, 'type': 'continuous'},
            'audio': {'dim': 1024, 'type': 'temporal'},
            'video': {'dim': 4096, 'type': 'spatiotemporal'}
        }
    
    def simulate_modality_features(self, modality, n_samples=1000):
        """模拟不同模态的特征表示"""
        dim = self.modalities[modality]['dim']
        
        if modality == 'text':
            # 文本特征：稀疏、离散
            features = np.random.exponential(0.1, (n_samples, dim))
            features = np.where(np.random.random((n_samples, dim)) < 0.9, 0, features)
        
        elif modality == 'image':
            # 图像特征：连续、空间相关
            features = np.random.multivariate_normal(
                mean=np.zeros(dim),
                cov=np.eye(dim) * 0.5 + np.ones((dim, dim)) * 0.1,
                size=n_samples
            )
        
        elif modality == 'audio':
            # 音频特征：时序、频域
            t = np.linspace(0, 1, dim)
            features = []
            for _ in range(n_samples):
                freq = np.random.uniform(1, 10)
                phase = np.random.uniform(0, 2*np.pi)
                signal = np.sin(2*np.pi*freq*t + phase) + np.random.normal(0, 0.1, dim)
                features.append(signal)
            features = np.array(features)
        
        elif modality == 'video':
            # 视频特征：时空、高维
            features = np.random.normal(0, 1, (n_samples, dim))
            # 添加时序相关性
            for i in range(1, n_samples):
                features[i] = 0.7 * features[i-1] + 0.3 * features[i]
        
        return features
    
    def visualize_modality_distributions(self):
        """可视化不同模态的特征分布"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        axes = axes.flatten()
        
        for i, modality in enumerate(self.modalities.keys()):
            features = self.simulate_modality_features(modality, n_samples=500)
            
            # 使用PCA降维到2D进行可视化
            if features.shape[1] > 2:
                pca = PCA(n_components=2)
                features_2d = pca.fit_transform(features)
            else:
                features_2d = features
            
            axes[i].scatter(features_2d[:, 0], features_2d[:, 1], 
                          alpha=0.6, s=20, label=modality)
            axes[i].set_title(f'{modality.capitalize()} 模态特征分布')
            axes[i].set_xlabel('主成分1')
            axes[i].set_ylabel('主成分2')
            axes[i].grid(True, alpha=0.3)
            axes[i].legend()
        
        plt.tight_layout()
        plt.show()

# 演示多模态特征分布
modality_analyzer = ModalityAnalysis()
modality_analyzer.visualize_modality_distributions()
```

**跨模态对齐问题**：
```python
class CrossModalAlignment:
    """跨模态对齐演示"""
    
    def __init__(self, text_dim=512, image_dim=2048, hidden_dim=256):
        self.text_dim = text_dim
        self.image_dim = image_dim
        self.hidden_dim = hidden_dim
        
        # 模态特定的编码器
        self.text_encoder = nn.Sequential(
            nn.Linear(text_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim)
        )
        
        self.image_encoder = nn.Sequential(
            nn.Linear(image_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim)
        )
    
    def contrastive_loss(self, text_features, image_features, temperature=0.07):
        """对比学习损失函数"""
        # 归一化特征
        text_features = F.normalize(text_features, dim=1)
        image_features = F.normalize(image_features, dim=1)
        
        # 计算相似度矩阵
        similarity_matrix = torch.matmul(text_features, image_features.T) / temperature
        
        # 标签：对角线为正样本
        batch_size = text_features.shape[0]
        labels = torch.arange(batch_size)
        
        # 计算交叉熵损失
        loss_text_to_image = F.cross_entropy(similarity_matrix, labels)
        loss_image_to_text = F.cross_entropy(similarity_matrix.T, labels)
        
        return (loss_text_to_image + loss_image_to_text) / 2
    
    def demonstrate_alignment(self):
        """演示跨模态对齐过程"""
        # 模拟数据
        batch_size = 64
        text_data = torch.randn(batch_size, self.text_dim)
        image_data = torch.randn(batch_size, self.image_dim)
        
        # 编码到共同空间
        text_features = self.text_encoder(text_data)
        image_features = self.image_encoder(image_data)
        
        # 计算对比损失
        loss = self.contrastive_loss(text_features, image_features)
        
        print(f"跨模态对齐损失: {loss.item():.4f}")
        
        # 可视化对齐效果
        with torch.no_grad():
            text_features_np = text_features.numpy()
            image_features_np = image_features.numpy()
            
            # 使用t-SNE降维可视化
            combined_features = np.vstack([text_features_np, image_features_np])
            tsne = TSNE(n_components=2, random_state=42)
            embedded = tsne.fit_transform(combined_features)
            
            plt.figure(figsize=(10, 8))
            plt.scatter(embedded[:batch_size, 0], embedded[:batch_size, 1], 
                       c='red', label='文本特征', alpha=0.6)
            plt.scatter(embedded[batch_size:, 0], embedded[batch_size:, 1], 
                       c='blue', label='图像特征', alpha=0.6)
            
            # 连接对应的文本-图像对
            for i in range(batch_size):
                plt.plot([embedded[i, 0], embedded[i+batch_size, 0]], 
                        [embedded[i, 1], embedded[i+batch_size, 1]], 
                        'gray', alpha=0.3, linewidth=0.5)
            
            plt.title('跨模态特征对齐可视化')
            plt.xlabel('t-SNE 维度 1')
            plt.ylabel('t-SNE 维度 2')
            plt.legend()
            plt.grid(True, alpha=0.3)
            plt.show()

# 演示跨模态对齐
alignment_demo = CrossModalAlignment()
alignment_demo.demonstrate_alignment()
```

##### 2. 注意力机制与模态融合（13分钟）
**多头注意力机制**：
```python
class MultiModalAttention:
    """多模态注意力机制"""
    
    def __init__(self, d_model=512, num_heads=8):
        self.d_model = d_model
        self.num_heads = num_heads
        self.d_k = d_model // num_heads
        
        # 注意力权重矩阵
        self.W_q = nn.Linear(d_model, d_model)
        self.W_k = nn.Linear(d_model, d_model)
        self.W_v = nn.Linear(d_model, d_model)
        self.W_o = nn.Linear(d_model, d_model)
    
    def scaled_dot_product_attention(self, Q, K, V, mask=None):
        """缩放点积注意力"""
        # 计算注意力分数
        scores = torch.matmul(Q, K.transpose(-2, -1)) / np.sqrt(self.d_k)
        
        # 应用掩码
        if mask is not None:
            scores = scores.masked_fill(mask == 0, -1e9)
        
        # Softmax归一化
        attention_weights = F.softmax(scores, dim=-1)
        
        # 加权求和
        output = torch.matmul(attention_weights, V)
        
        return output, attention_weights
    
    def multi_head_attention(self, query, key, value, mask=None):
        """多头注意力"""
        batch_size = query.size(0)
        
        # 线性变换并重塑为多头
        Q = self.W_q(query).view(batch_size, -1, self.num_heads, self.d_k).transpose(1, 2)
        K = self.W_k(key).view(batch_size, -1, self.num_heads, self.d_k).transpose(1, 2)
        V = self.W_v(value).view(batch_size, -1, self.num_heads, self.d_k).transpose(1, 2)
        
        # 应用注意力
        attention_output, attention_weights = self.scaled_dot_product_attention(Q, K, V, mask)
        
        # 重塑并连接多头
        attention_output = attention_output.transpose(1, 2).contiguous().view(
            batch_size, -1, self.d_model
        )
        
        # 最终线性变换
        output = self.W_o(attention_output)
        
        return output, attention_weights
    
    def cross_modal_attention(self, text_features, image_features):
        """跨模态注意力"""
        # 文本作为查询，图像作为键值
        text_to_image, text_to_image_weights = self.multi_head_attention(
            text_features, image_features, image_features
        )
        
        # 图像作为查询，文本作为键值
        image_to_text, image_to_text_weights = self.multi_head_attention(
            image_features, text_features, text_features
        )
        
        return {
            'text_to_image': text_to_image,
            'image_to_text': image_to_text,
            'text_to_image_weights': text_to_image_weights,
            'image_to_text_weights': image_to_text_weights
        }
    
    def visualize_attention_weights(self, attention_weights, modality_names):
        """可视化注意力权重"""
        # 取第一个头的注意力权重进行可视化
        weights = attention_weights[0, 0].detach().numpy()
        
        plt.figure(figsize=(10, 8))
        plt.imshow(weights, cmap='Blues', aspect='auto')
        plt.colorbar(label='注意力权重')
        plt.xlabel(f'{modality_names[1]} 位置')
        plt.ylabel(f'{modality_names[0]} 位置')
        plt.title(f'{modality_names[0]} 到 {modality_names[1]} 的注意力权重')
        plt.show()

# 演示多模态注意力
attention_demo = MultiModalAttention()

# 模拟文本和图像特征
batch_size, seq_len, d_model = 2, 10, 512
text_features = torch.randn(batch_size, seq_len, d_model)
image_features = torch.randn(batch_size, seq_len, d_model)

# 计算跨模态注意力
results = attention_demo.cross_modal_attention(text_features, image_features)

# 可视化注意力权重
attention_demo.visualize_attention_weights(
    results['text_to_image_weights'], 
    ['文本', '图像']
)
```

#### 实践体验（10分钟）
**多模态数据探索**：
学生分组体验不同类型的多模态数据，分析其特征和挑战

### 第二课时（45分钟）

#### 深入分析（20分钟）

##### 1. 视觉-语言模型架构（12分钟）
**CLIP模型原理**：
```python
class CLIPModel:
    """CLIP模型简化实现"""

    def __init__(self, text_dim=512, image_dim=2048, embed_dim=512):
        # 文本编码器
        self.text_encoder = nn.Sequential(
            nn.Linear(text_dim, embed_dim),
            nn.ReLU(),
            nn.Linear(embed_dim, embed_dim),
            nn.LayerNorm(embed_dim)
        )

        # 图像编码器
        self.image_encoder = nn.Sequential(
            nn.Linear(image_dim, embed_dim),
            nn.ReLU(),
            nn.Linear(embed_dim, embed_dim),
            nn.LayerNorm(embed_dim)
        )

        # 温度参数
        self.temperature = nn.Parameter(torch.ones([]) * np.log(1 / 0.07))

    def encode_text(self, text):
        """编码文本"""
        return F.normalize(self.text_encoder(text), dim=-1)

    def encode_image(self, image):
        """编码图像"""
        return F.normalize(self.image_encoder(image), dim=-1)

    def forward(self, text, image):
        """前向传播"""
        text_features = self.encode_text(text)
        image_features = self.encode_image(image)

        # 计算相似度矩阵
        logit_scale = self.temperature.exp()
        logits_per_text = logit_scale * text_features @ image_features.t()
        logits_per_image = logits_per_text.t()

        return logits_per_text, logits_per_image

    def compute_clip_loss(self, logits_per_text, logits_per_image):
        """计算CLIP损失"""
        batch_size = logits_per_text.shape[0]
        labels = torch.arange(batch_size)

        loss_text = F.cross_entropy(logits_per_text, labels)
        loss_image = F.cross_entropy(logits_per_image, labels)

        return (loss_text + loss_image) / 2

# 演示CLIP模型
clip_model = CLIPModel()

# 模拟数据
batch_size = 32
text_data = torch.randn(batch_size, 512)
image_data = torch.randn(batch_size, 2048)

# 前向传播
logits_text, logits_image = clip_model(text_data, image_data)
loss = clip_model.compute_clip_loss(logits_text, logits_image)

print(f"CLIP损失: {loss.item():.4f}")
print(f"文本-图像相似度矩阵形状: {logits_text.shape}")
```

**视觉问答系统**：
```python
class VisualQuestionAnswering:
    """视觉问答系统"""

    def __init__(self, image_dim=2048, text_dim=768, hidden_dim=512, vocab_size=10000):
        # 图像特征提取器
        self.image_encoder = nn.Sequential(
            nn.Linear(image_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.1)
        )

        # 问题编码器
        self.question_encoder = nn.LSTM(text_dim, hidden_dim, batch_first=True)

        # 注意力机制
        self.attention = nn.MultiheadAttention(hidden_dim, num_heads=8)

        # 答案分类器
        self.answer_classifier = nn.Sequential(
            nn.Linear(hidden_dim * 2, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(hidden_dim, vocab_size)
        )

    def forward(self, image_features, question_features):
        """前向传播"""
        # 编码图像特征
        image_encoded = self.image_encoder(image_features)  # [batch, hidden_dim]

        # 编码问题
        question_encoded, (h_n, c_n) = self.question_encoder(question_features)
        question_final = h_n[-1]  # 取最后一层的隐状态

        # 注意力机制：问题关注图像
        image_attended, attention_weights = self.attention(
            question_final.unsqueeze(1),  # query
            image_encoded.unsqueeze(1),   # key
            image_encoded.unsqueeze(1)    # value
        )

        # 融合特征
        fused_features = torch.cat([
            question_final,
            image_attended.squeeze(1)
        ], dim=1)

        # 预测答案
        answer_logits = self.answer_classifier(fused_features)

        return answer_logits, attention_weights

    def demonstrate_vqa(self):
        """演示视觉问答"""
        batch_size, seq_len = 16, 10

        # 模拟数据
        image_features = torch.randn(batch_size, 2048)
        question_features = torch.randn(batch_size, seq_len, 768)

        # 前向传播
        answer_logits, attention_weights = self.forward(image_features, question_features)

        print(f"答案预测形状: {answer_logits.shape}")
        print(f"注意力权重形状: {attention_weights.shape}")

        # 可视化注意力权重
        plt.figure(figsize=(8, 6))
        weights = attention_weights[0].detach().numpy().flatten()
        plt.bar(range(len(weights)), weights)
        plt.title('视觉问答中的注意力权重')
        plt.xlabel('图像区域')
        plt.ylabel('注意力权重')
        plt.show()

# 演示视觉问答系统
vqa_model = VisualQuestionAnswering()
vqa_model.demonstrate_vqa()
```

##### 2. 多模态融合策略（8分钟）
**早期融合vs晚期融合**：
```python
class MultiModalFusion:
    """多模态融合策略比较"""

    def __init__(self, text_dim=768, image_dim=2048, audio_dim=1024, hidden_dim=512, num_classes=10):
        self.text_dim = text_dim
        self.image_dim = image_dim
        self.audio_dim = audio_dim
        self.hidden_dim = hidden_dim
        self.num_classes = num_classes

        # 早期融合
        self.early_fusion = self._build_early_fusion()

        # 晚期融合
        self.late_fusion = self._build_late_fusion()

        # 中期融合（注意力）
        self.mid_fusion = self._build_mid_fusion()

    def _build_early_fusion(self):
        """早期融合：特征级融合"""
        total_dim = self.text_dim + self.image_dim + self.audio_dim
        return nn.Sequential(
            nn.Linear(total_dim, self.hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(self.hidden_dim, self.hidden_dim),
            nn.ReLU(),
            nn.Linear(self.hidden_dim, self.num_classes)
        )

    def _build_late_fusion(self):
        """晚期融合：决策级融合"""
        return nn.ModuleDict({
            'text_classifier': nn.Sequential(
                nn.Linear(self.text_dim, self.hidden_dim),
                nn.ReLU(),
                nn.Linear(self.hidden_dim, self.num_classes)
            ),
            'image_classifier': nn.Sequential(
                nn.Linear(self.image_dim, self.hidden_dim),
                nn.ReLU(),
                nn.Linear(self.hidden_dim, self.num_classes)
            ),
            'audio_classifier': nn.Sequential(
                nn.Linear(self.audio_dim, self.hidden_dim),
                nn.ReLU(),
                nn.Linear(self.hidden_dim, self.num_classes)
            ),
            'fusion_weights': nn.Linear(3, 3)
        })

    def _build_mid_fusion(self):
        """中期融合：注意力机制"""
        return nn.ModuleDict({
            'text_encoder': nn.Linear(self.text_dim, self.hidden_dim),
            'image_encoder': nn.Linear(self.image_dim, self.hidden_dim),
            'audio_encoder': nn.Linear(self.audio_dim, self.hidden_dim),
            'attention': nn.MultiheadAttention(self.hidden_dim, num_heads=8),
            'classifier': nn.Sequential(
                nn.Linear(self.hidden_dim, self.hidden_dim),
                nn.ReLU(),
                nn.Linear(self.hidden_dim, self.num_classes)
            )
        })

    def early_fusion_forward(self, text, image, audio):
        """早期融合前向传播"""
        # 连接所有特征
        fused_features = torch.cat([text, image, audio], dim=1)
        return self.early_fusion(fused_features)

    def late_fusion_forward(self, text, image, audio):
        """晚期融合前向传播"""
        # 分别预测
        text_logits = self.late_fusion['text_classifier'](text)
        image_logits = self.late_fusion['image_classifier'](image)
        audio_logits = self.late_fusion['audio_classifier'](audio)

        # 学习融合权重
        logits_stack = torch.stack([text_logits, image_logits, audio_logits], dim=2)
        weights = F.softmax(self.late_fusion['fusion_weights'](
            torch.ones(text.size(0), 3).to(text.device)
        ), dim=1)

        # 加权融合
        fused_logits = torch.sum(logits_stack * weights.unsqueeze(1), dim=2)
        return fused_logits

    def mid_fusion_forward(self, text, image, audio):
        """中期融合前向传播"""
        # 编码各模态
        text_encoded = self.mid_fusion['text_encoder'](text).unsqueeze(1)
        image_encoded = self.mid_fusion['image_encoder'](image).unsqueeze(1)
        audio_encoded = self.mid_fusion['audio_encoder'](audio).unsqueeze(1)

        # 堆叠特征
        all_features = torch.cat([text_encoded, image_encoded, audio_encoded], dim=1)

        # 自注意力
        attended_features, _ = self.mid_fusion['attention'](
            all_features, all_features, all_features
        )

        # 平均池化并分类
        pooled_features = attended_features.mean(dim=1)
        return self.mid_fusion['classifier'](pooled_features)

    def compare_fusion_strategies(self):
        """比较不同融合策略"""
        batch_size = 32

        # 模拟数据
        text_data = torch.randn(batch_size, self.text_dim)
        image_data = torch.randn(batch_size, self.image_dim)
        audio_data = torch.randn(batch_size, self.audio_dim)

        # 测试不同融合策略
        early_output = self.early_fusion_forward(text_data, image_data, audio_data)
        late_output = self.late_fusion_forward(text_data, image_data, audio_data)
        mid_output = self.mid_fusion_forward(text_data, image_data, audio_data)

        print("融合策略比较:")
        print(f"早期融合输出形状: {early_output.shape}")
        print(f"晚期融合输出形状: {late_output.shape}")
        print(f"中期融合输出形状: {mid_output.shape}")

        # 可视化输出分布
        fig, axes = plt.subplots(1, 3, figsize=(15, 5))

        outputs = [early_output, late_output, mid_output]
        titles = ['早期融合', '晚期融合', '中期融合']

        for i, (output, title) in enumerate(zip(outputs, titles)):
            output_np = output.detach().numpy()
            axes[i].hist(output_np.flatten(), bins=30, alpha=0.7)
            axes[i].set_title(f'{title}输出分布')
            axes[i].set_xlabel('输出值')
            axes[i].set_ylabel('频次')
            axes[i].grid(True, alpha=0.3)

        plt.tight_layout()
        plt.show()

# 演示多模态融合策略
fusion_demo = MultiModalFusion()
fusion_demo.compare_fusion_strategies()
```

#### 系统实现（15分钟）

##### 完整多模态系统构建
**端到端多模态分类系统**：
```python
class EndToEndMultiModalSystem:
    """端到端多模态系统"""

    def __init__(self):
        # 预处理器
        self.text_preprocessor = self._build_text_preprocessor()
        self.image_preprocessor = self._build_image_preprocessor()

        # 特征提取器
        self.feature_extractors = self._build_feature_extractors()

        # 融合网络
        self.fusion_network = self._build_fusion_network()

        # 分类器
        self.classifier = self._build_classifier()

    def _build_text_preprocessor(self):
        """文本预处理器"""
        return {
            'tokenizer': lambda x: x.split(),  # 简化的分词器
            'vocab_size': 10000,
            'max_length': 128
        }

    def _build_image_preprocessor(self):
        """图像预处理器"""
        return {
            'resize': (224, 224),
            'normalize': {'mean': [0.485, 0.456, 0.406], 'std': [0.229, 0.224, 0.225]}
        }

    def _build_feature_extractors(self):
        """特征提取器"""
        return nn.ModuleDict({
            'text_extractor': nn.Sequential(
                nn.Embedding(10000, 768),
                nn.LSTM(768, 512, batch_first=True),
                nn.AdaptiveAvgPool1d(1)
            ),
            'image_extractor': nn.Sequential(
                nn.Conv2d(3, 64, 3, padding=1),
                nn.ReLU(),
                nn.AdaptiveAvgPool2d((7, 7)),
                nn.Flatten(),
                nn.Linear(64 * 7 * 7, 2048)
            )
        })

    def _build_fusion_network(self):
        """融合网络"""
        return nn.Sequential(
            nn.Linear(512 + 2048, 1024),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(1024, 512)
        )

    def _build_classifier(self):
        """分类器"""
        return nn.Sequential(
            nn.Linear(512, 256),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(256, 10)  # 10个类别
        )

    def preprocess_text(self, text_batch):
        """预处理文本"""
        # 简化实现：随机生成token ids
        batch_size = len(text_batch)
        max_length = self.text_preprocessor['max_length']
        return torch.randint(0, self.text_preprocessor['vocab_size'],
                           (batch_size, max_length))

    def preprocess_image(self, image_batch):
        """预处理图像"""
        # 简化实现：随机生成图像数据
        batch_size = len(image_batch)
        return torch.randn(batch_size, 3, 224, 224)

    def extract_features(self, text_tokens, image_tensors):
        """提取特征"""
        # 文本特征提取
        text_embedded = self.feature_extractors['text_extractor'][0](text_tokens)
        text_lstm_out, _ = self.feature_extractors['text_extractor'][1](text_embedded)
        text_features = text_lstm_out.mean(dim=1)  # 平均池化

        # 图像特征提取
        image_features = self.feature_extractors['image_extractor'](image_tensors)

        return text_features, image_features

    def forward(self, text_batch, image_batch):
        """完整前向传播"""
        # 预处理
        text_tokens = self.preprocess_text(text_batch)
        image_tensors = self.preprocess_image(image_batch)

        # 特征提取
        text_features, image_features = self.extract_features(text_tokens, image_tensors)

        # 特征融合
        fused_features = torch.cat([text_features, image_features], dim=1)
        fusion_output = self.fusion_network(fused_features)

        # 分类
        logits = self.classifier(fusion_output)

        return logits

    def train_step(self, text_batch, image_batch, labels, optimizer):
        """训练步骤"""
        optimizer.zero_grad()

        # 前向传播
        logits = self.forward(text_batch, image_batch)

        # 计算损失
        loss = F.cross_entropy(logits, labels)

        # 反向传播
        loss.backward()
        optimizer.step()

        return loss.item()

    def evaluate(self, text_batch, image_batch, labels):
        """评估"""
        with torch.no_grad():
            logits = self.forward(text_batch, image_batch)
            predictions = torch.argmax(logits, dim=1)
            accuracy = (predictions == labels).float().mean()

        return accuracy.item()

# 演示完整系统
system = EndToEndMultiModalSystem()

# 模拟训练数据
batch_size = 16
text_data = [f"sample text {i}" for i in range(batch_size)]
image_data = [f"image_{i}.jpg" for i in range(batch_size)]
labels = torch.randint(0, 10, (batch_size,))

# 前向传播测试
logits = system.forward(text_data, image_data)
print(f"系统输出形状: {logits.shape}")

# 模拟训练
optimizer = torch.optim.Adam(system.parameters(), lr=0.001)
loss = system.train_step(text_data, image_data, labels, optimizer)
print(f"训练损失: {loss:.4f}")

# 模拟评估
accuracy = system.evaluate(text_data, image_data, labels)
print(f"准确率: {accuracy:.4f}")
```

#### 总结反思（10分钟）
**核心要点回顾**：
- 多模态AI系统需要处理不同类型数据的异构性
- 跨模态对齐是多模态学习的核心挑战
- 注意力机制是实现模态间信息交互的关键技术
- 不同融合策略适用于不同的应用场景

## 📊 评估方式

### 过程性评价
- **概念理解**：对多模态AI基本概念的掌握程度
- **技术分析**：分析不同模态数据特征和处理方法的能力
- **系统设计**：设计多模态融合架构的能力
- **实现能力**：编程实现多模态系统的技能

### 结果性评价
- **系统实现**：完成多模态AI系统的设计和实现
- **融合策略**：比较和选择合适的模态融合方法
- **性能分析**：评估多模态系统的性能和效果
- **应用设计**：设计创新的多模态AI应用

## 🏠 课后延伸

### 基础任务
1. **模态分析**：分析不同模态数据的特征和预处理方法
2. **注意力实现**：实现跨模态注意力机制
3. **融合比较**：比较不同融合策略的优缺点

### 拓展任务
1. **系统优化**：优化多模态系统的性能和效率
2. **新应用设计**：设计新颖的多模态AI应用
3. **前沿技术**：研究最新的多模态AI技术和模型

### 预习任务
了解AI在不同学科领域的应用，思考跨学科融合的可能性。

---

*本课程旨在帮助学生理解多模态AI系统的设计原理和实现方法，培养跨模态思维和系统设计能力。*
