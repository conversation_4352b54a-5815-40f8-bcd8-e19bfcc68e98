# 第3课：AI与跨学科融合

## 🎯 课程基本信息

- **课程名称**：AI与跨学科融合
- **适用年级**：高中十一年级
- **课时安排**：90分钟（2课时）
- **课程类型**：算法深化课
- **核心主题**：AI技术在不同学科领域的应用与融合创新

## 📚 教学目标

### 认知目标
- 理解AI技术在各学科领域的应用原理和方法
- 掌握跨学科问题的AI解决方案设计思路
- 认识AI驱动的科学发现和创新模式
- 了解跨学科融合中的挑战和机遇

### 技能目标
- 能够分析不同学科问题的AI建模方法
- 掌握科学计算和数据分析的AI工具使用
- 学会设计跨学科的AI应用解决方案
- 能够评估AI技术在特定领域的适用性

### 思维目标
- 培养跨学科的系统性思维
- 发展问题抽象和建模的能力
- 建立AI技术迁移和适配的思维
- 培养创新性的跨领域融合思维

### 价值观目标
- 认识AI技术推动科学进步的价值
- 培养跨学科合作和开放包容的态度
- 增强用AI技术服务社会的责任感
- 建立科技创新推动人类发展的理念

## 🎯 教学流程

### 第一课时（45分钟）

#### 导入环节（10分钟）
**跨学科AI成果展示**：
- 展示AlphaFold在蛋白质结构预测中的突破
- 演示AI在天体物理学中发现系外行星的应用
- 介绍AI辅助药物发现和材料设计的案例

**核心问题**：
- "AI如何改变传统学科的研究方法？"
- "不同学科的问题如何转化为AI可以解决的形式？"
- "跨学科融合能带来哪些创新机遇？"

#### 新课讲授（25分钟）

##### 1. AI在自然科学中的应用（15分钟）
**物理学中的AI应用**：
```python
import numpy as np
import matplotlib.pyplot as plt
from scipy.integrate import odeint
import torch
import torch.nn as nn
from sklearn.preprocessing import StandardScaler

class PhysicsInformedNN:
    """物理信息神经网络（PINN）"""
    
    def __init__(self, layers=[1, 50, 50, 50, 1]):
        self.layers = layers
        self.network = self._build_network()
        self.optimizer = torch.optim.Adam(self.network.parameters(), lr=0.001)
    
    def _build_network(self):
        """构建神经网络"""
        layers = []
        for i in range(len(self.layers) - 1):
            layers.append(nn.Linear(self.layers[i], self.layers[i+1]))
            if i < len(self.layers) - 2:
                layers.append(nn.Tanh())
        return nn.Sequential(*layers)
    
    def forward(self, x):
        """前向传播"""
        return self.network(x)
    
    def physics_loss(self, x, u):
        """物理约束损失（以简谐振动为例）"""
        # 计算导数
        u_x = torch.autograd.grad(u, x, grad_outputs=torch.ones_like(u), 
                                 create_graph=True)[0]
        u_xx = torch.autograd.grad(u_x, x, grad_outputs=torch.ones_like(u_x), 
                                  create_graph=True)[0]
        
        # 简谐振动方程: d²u/dt² + ω²u = 0
        omega = 1.0  # 角频率
        physics_residual = u_xx + omega**2 * u
        
        return torch.mean(physics_residual**2)
    
    def boundary_loss(self, x_boundary, u_boundary, u_true_boundary):
        """边界条件损失"""
        return torch.mean((u_boundary - u_true_boundary)**2)
    
    def train_pinn(self, x_data, x_boundary, u_boundary_true, epochs=1000):
        """训练物理信息神经网络"""
        losses = []
        
        for epoch in range(epochs):
            self.optimizer.zero_grad()
            
            # 前向传播
            x_data.requires_grad_(True)
            u_pred = self.forward(x_data)
            u_boundary_pred = self.forward(x_boundary)
            
            # 计算损失
            physics_loss = self.physics_loss(x_data, u_pred)
            boundary_loss = self.boundary_loss(x_boundary, u_boundary_pred, u_boundary_true)
            total_loss = physics_loss + boundary_loss
            
            # 反向传播
            total_loss.backward()
            self.optimizer.step()
            
            losses.append(total_loss.item())
            
            if epoch % 200 == 0:
                print(f"Epoch {epoch}: Loss = {total_loss.item():.6f}")
        
        return losses
    
    def solve_harmonic_oscillator(self):
        """求解简谐振动问题"""
        # 生成训练数据
        t = torch.linspace(0, 2*np.pi, 100).reshape(-1, 1)
        
        # 边界条件：t=0时，u=1, du/dt=0
        t_boundary = torch.tensor([[0.0]])
        u_boundary_true = torch.tensor([[1.0]])
        
        # 训练网络
        losses = self.train_pinn(t, t_boundary, u_boundary_true, epochs=2000)
        
        # 预测解
        with torch.no_grad():
            t_test = torch.linspace(0, 4*np.pi, 200).reshape(-1, 1)
            u_pred = self.forward(t_test)
            
            # 解析解
            u_analytical = torch.cos(t_test)
        
        # 可视化结果
        plt.figure(figsize=(12, 8))
        
        plt.subplot(2, 2, 1)
        plt.plot(losses)
        plt.title('训练损失')
        plt.xlabel('训练轮次')
        plt.ylabel('损失值')
        plt.grid(True, alpha=0.3)
        
        plt.subplot(2, 2, 2)
        plt.plot(t_test.numpy(), u_pred.numpy(), 'r-', label='PINN预测', linewidth=2)
        plt.plot(t_test.numpy(), u_analytical.numpy(), 'b--', label='解析解', linewidth=2)
        plt.title('简谐振动求解')
        plt.xlabel('时间')
        plt.ylabel('位移')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        plt.subplot(2, 2, 3)
        error = torch.abs(u_pred - u_analytical)
        plt.plot(t_test.numpy(), error.numpy(), 'g-', linewidth=2)
        plt.title('预测误差')
        plt.xlabel('时间')
        plt.ylabel('绝对误差')
        plt.grid(True, alpha=0.3)
        
        plt.subplot(2, 2, 4)
        plt.scatter(t_test.numpy()[::10], u_pred.numpy()[::10], c='red', s=30, alpha=0.7, label='PINN')
        plt.scatter(t_test.numpy()[::10], u_analytical.numpy()[::10], c='blue', s=30, alpha=0.7, label='解析解')
        plt.title('解的比较')
        plt.xlabel('时间')
        plt.ylabel('位移')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.show()

# 演示物理信息神经网络
pinn = PhysicsInformedNN()
pinn.solve_harmonic_oscillator()
```

**化学中的AI应用**：
```python
class MolecularPropertyPrediction:
    """分子性质预测"""
    
    def __init__(self):
        # 简化的分子表示
        self.atom_features = {
            'C': [6, 4, 2.55],   # 原子序数，价电子数，电负性
            'N': [7, 5, 3.04],
            'O': [8, 6, 3.44],
            'H': [1, 1, 2.20],
            'S': [16, 6, 2.58]
        }
        
        # 分子性质预测网络
        self.property_predictor = nn.Sequential(
            nn.Linear(100, 256),  # 假设分子指纹长度为100
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(128, 1)  # 预测单一性质
        )
    
    def molecular_fingerprint(self, molecule_smiles):
        """生成分子指纹（简化版）"""
        # 这里使用随机特征模拟分子指纹
        # 实际应用中会使用RDKit等库生成真实的分子指纹
        np.random.seed(hash(molecule_smiles) % 2**32)
        return torch.tensor(np.random.randn(100), dtype=torch.float32)
    
    def predict_solubility(self, smiles_list):
        """预测分子溶解度"""
        predictions = []
        
        for smiles in smiles_list:
            fingerprint = self.molecular_fingerprint(smiles)
            with torch.no_grad():
                solubility = self.property_predictor(fingerprint.unsqueeze(0))
                predictions.append(solubility.item())
        
        return predictions
    
    def demonstrate_drug_discovery(self):
        """演示药物发现过程"""
        # 模拟候选分子
        candidate_molecules = [
            "CCO",  # 乙醇
            "CC(=O)O",  # 乙酸
            "c1ccccc1",  # 苯
            "CCN(CC)CC",  # 三乙胺
            "CC(C)O"  # 异丙醇
        ]
        
        # 预测性质
        solubilities = self.predict_solubility(candidate_molecules)
        
        # 可视化结果
        plt.figure(figsize=(10, 6))
        
        plt.subplot(1, 2, 1)
        plt.bar(range(len(candidate_molecules)), solubilities, 
                color=['red', 'blue', 'green', 'orange', 'purple'])
        plt.title('分子溶解度预测')
        plt.xlabel('分子编号')
        plt.ylabel('预测溶解度')
        plt.xticks(range(len(candidate_molecules)), 
                  [f"分子{i+1}" for i in range(len(candidate_molecules))])
        plt.grid(True, alpha=0.3)
        
        plt.subplot(1, 2, 2)
        # 模拟优化过程
        generations = range(1, 11)
        best_scores = [0.3 + 0.05*i + 0.02*np.random.randn() for i in generations]
        
        plt.plot(generations, best_scores, 'o-', linewidth=2, markersize=8)
        plt.title('分子优化过程')
        plt.xlabel('优化代数')
        plt.ylabel('最佳适应度')
        plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.show()
        
        return dict(zip(candidate_molecules, solubilities))

# 演示分子性质预测
mol_predictor = MolecularPropertyPrediction()
results = mol_predictor.demonstrate_drug_discovery()
print("分子溶解度预测结果:")
for smiles, solubility in results.items():
    print(f"{smiles}: {solubility:.4f}")
```

##### 2. AI在生物学中的应用（10分钟）
**蛋白质结构预测**：
```python
class ProteinStructurePrediction:
    """蛋白质结构预测（简化版）"""
    
    def __init__(self):
        # 氨基酸编码
        self.amino_acids = {
            'A': 0, 'R': 1, 'N': 2, 'D': 3, 'C': 4, 'Q': 5, 'E': 6, 'G': 7,
            'H': 8, 'I': 9, 'L': 10, 'K': 11, 'M': 12, 'F': 13, 'P': 14,
            'S': 15, 'T': 16, 'W': 17, 'Y': 18, 'V': 19
        }
        
        # 结构预测网络
        self.structure_predictor = nn.Sequential(
            nn.Embedding(20, 64),  # 氨基酸嵌入
            nn.LSTM(64, 128, batch_first=True, bidirectional=True),
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Linear(128, 3)  # 预测3D坐标
        )
    
    def encode_sequence(self, sequence):
        """编码蛋白质序列"""
        encoded = [self.amino_acids.get(aa, 0) for aa in sequence.upper()]
        return torch.tensor(encoded, dtype=torch.long)
    
    def predict_structure(self, sequence):
        """预测蛋白质结构"""
        encoded_seq = self.encode_sequence(sequence).unsqueeze(0)
        
        with torch.no_grad():
            # 获取嵌入
            embedded = self.structure_predictor[0](encoded_seq)
            
            # LSTM处理
            lstm_out, _ = self.structure_predictor[1](embedded)
            
            # 预测坐标
            coords = self.structure_predictor[2:](lstm_out)
            coords = coords.squeeze(0)  # 移除batch维度
        
        return coords.numpy()
    
    def visualize_structure(self, sequence, coords):
        """可视化蛋白质结构"""
        fig = plt.figure(figsize=(15, 5))
        
        # 3D结构图
        ax1 = fig.add_subplot(131, projection='3d')
        ax1.plot(coords[:, 0], coords[:, 1], coords[:, 2], 'o-', markersize=4)
        ax1.set_title('3D蛋白质结构')
        ax1.set_xlabel('X坐标')
        ax1.set_ylabel('Y坐标')
        ax1.set_zlabel('Z坐标')
        
        # 距离矩阵
        ax2 = fig.add_subplot(132)
        n_residues = len(sequence)
        distance_matrix = np.zeros((n_residues, n_residues))
        
        for i in range(n_residues):
            for j in range(n_residues):
                distance_matrix[i, j] = np.linalg.norm(coords[i] - coords[j])
        
        im = ax2.imshow(distance_matrix, cmap='viridis')
        ax2.set_title('残基间距离矩阵')
        ax2.set_xlabel('残基位置')
        ax2.set_ylabel('残基位置')
        plt.colorbar(im, ax=ax2)
        
        # 二级结构预测
        ax3 = fig.add_subplot(133)
        # 简化的二级结构预测（基于局部几何）
        angles = []
        for i in range(1, len(coords)-1):
            v1 = coords[i] - coords[i-1]
            v2 = coords[i+1] - coords[i]
            angle = np.arccos(np.clip(np.dot(v1, v2) / 
                                    (np.linalg.norm(v1) * np.linalg.norm(v2)), -1, 1))
            angles.append(angle)
        
        ax3.plot(angles, 'b-', linewidth=2)
        ax3.set_title('主链扭转角')
        ax3.set_xlabel('残基位置')
        ax3.set_ylabel('角度（弧度）')
        ax3.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.show()
    
    def demonstrate_folding(self):
        """演示蛋白质折叠预测"""
        # 示例蛋白质序列
        test_sequence = "MKFLVLLFNILCLFPVLAADNHGVGPQGASLFIRSDYNLQLLRQGVQALFQD"
        
        print(f"预测序列: {test_sequence}")
        print(f"序列长度: {len(test_sequence)} 个氨基酸")
        
        # 预测结构
        predicted_coords = self.predict_structure(test_sequence)
        
        # 可视化
        self.visualize_structure(test_sequence, predicted_coords)
        
        return predicted_coords

# 演示蛋白质结构预测
protein_predictor = ProteinStructurePrediction()
coords = protein_predictor.demonstrate_folding()
```

#### 实践体验（10分钟）
**跨学科问题建模**：
学生分组选择不同学科问题，讨论如何用AI方法解决

### 第二课时（45分钟）

#### 深入分析（20分钟）

##### 1. AI在社会科学中的应用（12分钟）
**经济学中的AI应用**：
```python
class EconomicModeling:
    """经济学AI建模"""

    def __init__(self):
        # 市场预测模型
        self.market_predictor = nn.Sequential(
            nn.Linear(10, 64),  # 10个经济指标
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(64, 32),
            nn.ReLU(),
            nn.Linear(32, 1)  # 预测价格变化
        )

        # 消费者行为模型
        self.behavior_model = nn.Sequential(
            nn.Linear(8, 32),   # 8个消费者特征
            nn.ReLU(),
            nn.Linear(32, 16),
            nn.ReLU(),
            nn.Linear(16, 3)    # 3种购买决策
        )

    def simulate_market_data(self, n_samples=1000):
        """模拟市场数据"""
        # 生成经济指标
        gdp_growth = np.random.normal(2.5, 1.0, n_samples)
        inflation = np.random.normal(2.0, 0.5, n_samples)
        unemployment = np.random.normal(5.0, 1.5, n_samples)
        interest_rate = np.random.normal(3.0, 1.0, n_samples)
        oil_price = np.random.normal(70, 15, n_samples)

        # 构造特征矩阵
        features = np.column_stack([
            gdp_growth, inflation, unemployment, interest_rate, oil_price,
            np.random.randn(n_samples, 5)  # 其他指标
        ])

        # 生成目标变量（股价变化）
        price_change = (0.3 * gdp_growth - 0.2 * inflation - 0.1 * unemployment
                       - 0.15 * interest_rate + 0.05 * oil_price +
                       np.random.normal(0, 2, n_samples))

        return features, price_change

    def train_market_model(self):
        """训练市场预测模型"""
        # 生成训练数据
        X, y = self.simulate_market_data(5000)
        X_tensor = torch.tensor(X, dtype=torch.float32)
        y_tensor = torch.tensor(y, dtype=torch.float32).unsqueeze(1)

        # 训练模型
        optimizer = torch.optim.Adam(self.market_predictor.parameters(), lr=0.001)
        losses = []

        for epoch in range(1000):
            optimizer.zero_grad()
            predictions = self.market_predictor(X_tensor)
            loss = nn.MSELoss()(predictions, y_tensor)
            loss.backward()
            optimizer.step()
            losses.append(loss.item())

            if epoch % 200 == 0:
                print(f"Epoch {epoch}: Loss = {loss.item():.4f}")

        return losses

    def analyze_consumer_behavior(self):
        """分析消费者行为"""
        # 模拟消费者数据
        n_consumers = 1000
        age = np.random.normal(40, 15, n_consumers)
        income = np.random.lognormal(10, 0.5, n_consumers)
        education = np.random.randint(1, 6, n_consumers)  # 1-5教育水平
        family_size = np.random.poisson(2.5, n_consumers)

        # 构造消费者特征
        consumer_features = np.column_stack([
            age, income, education, family_size,
            np.random.randn(n_consumers, 4)  # 其他特征
        ])

        # 预测购买行为
        with torch.no_grad():
            features_tensor = torch.tensor(consumer_features, dtype=torch.float32)
            behavior_probs = torch.softmax(self.behavior_model(features_tensor), dim=1)

        # 可视化结果
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))

        # 收入分布
        axes[0, 0].hist(income, bins=50, alpha=0.7, color='blue')
        axes[0, 0].set_title('消费者收入分布')
        axes[0, 0].set_xlabel('收入')
        axes[0, 0].set_ylabel('频次')
        axes[0, 0].grid(True, alpha=0.3)

        # 年龄vs购买概率
        buy_prob = behavior_probs[:, 1].numpy()  # 购买概率
        axes[0, 1].scatter(age, buy_prob, alpha=0.5)
        axes[0, 1].set_title('年龄与购买概率关系')
        axes[0, 1].set_xlabel('年龄')
        axes[0, 1].set_ylabel('购买概率')
        axes[0, 1].grid(True, alpha=0.3)

        # 收入vs购买概率
        axes[1, 0].scatter(income, buy_prob, alpha=0.5, c='red')
        axes[1, 0].set_title('收入与购买概率关系')
        axes[1, 0].set_xlabel('收入')
        axes[1, 0].set_ylabel('购买概率')
        axes[1, 0].grid(True, alpha=0.3)

        # 购买决策分布
        decisions = torch.argmax(behavior_probs, dim=1).numpy()
        decision_counts = np.bincount(decisions)
        decision_labels = ['不购买', '购买', '延迟购买']

        axes[1, 1].pie(decision_counts, labels=decision_labels, autopct='%1.1f%%')
        axes[1, 1].set_title('消费者决策分布')

        plt.tight_layout()
        plt.show()

        return consumer_features, behavior_probs.numpy()

# 演示经济学AI建模
econ_model = EconomicModeling()
market_losses = econ_model.train_market_model()
consumer_data, behavior_data = econ_model.analyze_consumer_behavior()
```

**心理学中的AI应用**：
```python
class PsychologyAI:
    """心理学AI应用"""

    def __init__(self):
        # 情感分析模型
        self.emotion_classifier = nn.Sequential(
            nn.Linear(100, 64),  # 文本特征维度
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(64, 32),
            nn.ReLU(),
            nn.Linear(32, 6)  # 6种基本情感
        )

        # 认知负荷评估模型
        self.cognitive_load_model = nn.Sequential(
            nn.Linear(20, 32),   # 生理和行为指标
            nn.ReLU(),
            nn.Linear(32, 16),
            nn.ReLU(),
            nn.Linear(16, 1)     # 认知负荷水平
        )

    def simulate_text_features(self, texts):
        """模拟文本特征提取"""
        # 简化的文本特征（实际中会使用BERT等）
        features = []
        for text in texts:
            # 基于文本长度和内容生成特征
            feature = np.random.randn(100)
            # 添加一些基于文本的简单特征
            feature[0] = len(text)  # 文本长度
            feature[1] = text.count('!')  # 感叹号数量
            feature[2] = text.count('?')  # 问号数量
            features.append(feature)

        return torch.tensor(features, dtype=torch.float32)

    def analyze_emotions(self, texts):
        """分析文本情感"""
        # 提取特征
        features = self.simulate_text_features(texts)

        # 预测情感
        with torch.no_grad():
            emotion_logits = self.emotion_classifier(features)
            emotion_probs = torch.softmax(emotion_logits, dim=1)

        emotion_labels = ['快乐', '悲伤', '愤怒', '恐惧', '惊讶', '厌恶']

        # 可视化情感分析结果
        plt.figure(figsize=(12, 8))

        # 情感分布热图
        plt.subplot(2, 2, 1)
        emotion_matrix = emotion_probs.numpy()
        plt.imshow(emotion_matrix.T, cmap='viridis', aspect='auto')
        plt.colorbar(label='情感强度')
        plt.title('文本情感分析热图')
        plt.xlabel('文本编号')
        plt.ylabel('情感类型')
        plt.yticks(range(len(emotion_labels)), emotion_labels)

        # 平均情感分布
        plt.subplot(2, 2, 2)
        avg_emotions = emotion_probs.mean(dim=0).numpy()
        plt.bar(emotion_labels, avg_emotions, color='skyblue')
        plt.title('平均情感分布')
        plt.xlabel('情感类型')
        plt.ylabel('平均强度')
        plt.xticks(rotation=45)
        plt.grid(True, alpha=0.3)

        # 情感变化趋势
        plt.subplot(2, 2, 3)
        dominant_emotions = torch.argmax(emotion_probs, dim=1).numpy()
        plt.plot(dominant_emotions, 'o-', linewidth=2, markersize=6)
        plt.title('主导情感变化趋势')
        plt.xlabel('文本序号')
        plt.ylabel('主导情感类型')
        plt.yticks(range(len(emotion_labels)), emotion_labels)
        plt.grid(True, alpha=0.3)

        # 情感强度分布
        plt.subplot(2, 2, 4)
        max_probs = torch.max(emotion_probs, dim=1)[0].numpy()
        plt.hist(max_probs, bins=20, alpha=0.7, color='orange')
        plt.title('情感强度分布')
        plt.xlabel('最大情感强度')
        plt.ylabel('频次')
        plt.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.show()

        return emotion_probs.numpy(), emotion_labels

    def assess_cognitive_load(self):
        """评估认知负荷"""
        # 模拟生理和行为数据
        n_subjects = 200

        # 生理指标
        heart_rate = np.random.normal(75, 10, n_subjects)
        pupil_diameter = np.random.normal(4, 0.5, n_subjects)
        skin_conductance = np.random.normal(10, 2, n_subjects)

        # 行为指标
        reaction_time = np.random.normal(500, 100, n_subjects)  # 毫秒
        error_rate = np.random.beta(2, 8, n_subjects)  # 错误率

        # 任务特征
        task_difficulty = np.random.randint(1, 6, n_subjects)
        time_pressure = np.random.uniform(0, 1, n_subjects)

        # 构造特征矩阵
        features = np.column_stack([
            heart_rate, pupil_diameter, skin_conductance, reaction_time, error_rate,
            task_difficulty, time_pressure,
            np.random.randn(n_subjects, 13)  # 其他特征
        ])

        # 预测认知负荷
        features_tensor = torch.tensor(features, dtype=torch.float32)
        with torch.no_grad():
            cognitive_load = self.cognitive_load_model(features_tensor)

        # 可视化结果
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))

        # 认知负荷分布
        axes[0, 0].hist(cognitive_load.numpy(), bins=30, alpha=0.7, color='green')
        axes[0, 0].set_title('认知负荷分布')
        axes[0, 0].set_xlabel('认知负荷水平')
        axes[0, 0].set_ylabel('频次')
        axes[0, 0].grid(True, alpha=0.3)

        # 任务难度vs认知负荷
        axes[0, 1].scatter(task_difficulty, cognitive_load.numpy(), alpha=0.6)
        axes[0, 1].set_title('任务难度与认知负荷关系')
        axes[0, 1].set_xlabel('任务难度')
        axes[0, 1].set_ylabel('认知负荷')
        axes[0, 1].grid(True, alpha=0.3)

        # 心率vs认知负荷
        axes[1, 0].scatter(heart_rate, cognitive_load.numpy(), alpha=0.6, c='red')
        axes[1, 0].set_title('心率与认知负荷关系')
        axes[1, 0].set_xlabel('心率 (bpm)')
        axes[1, 0].set_ylabel('认知负荷')
        axes[1, 0].grid(True, alpha=0.3)

        # 反应时间vs错误率
        axes[1, 1].scatter(reaction_time, error_rate, c=cognitive_load.numpy(),
                          cmap='viridis', alpha=0.6)
        axes[1, 1].set_title('反应时间vs错误率（颜色表示认知负荷）')
        axes[1, 1].set_xlabel('反应时间 (ms)')
        axes[1, 1].set_ylabel('错误率')
        plt.colorbar(axes[1, 1].collections[0], ax=axes[1, 1], label='认知负荷')
        axes[1, 1].grid(True, alpha=0.3)

        plt.tight_layout()
        plt.show()

        return features, cognitive_load.numpy()

# 演示心理学AI应用
psych_ai = PsychologyAI()

# 情感分析演示
sample_texts = [
    "今天真是太开心了！",
    "我感到很沮丧和难过",
    "这让我非常愤怒！",
    "我有点担心明天的考试",
    "哇，这真是太令人惊讶了",
    "这个味道让我感到恶心"
]

emotions, labels = psych_ai.analyze_emotions(sample_texts)
print("情感分析完成")

# 认知负荷评估演示
features, cognitive_loads = psych_ai.assess_cognitive_load()
print("认知负荷评估完成")
```

##### 2. AI在人文学科中的应用（8分钟）
**历史学中的AI应用**：
```python
class HistoricalAnalysis:
    """历史学AI分析"""

    def __init__(self):
        # 文本分类模型（用于历史文档分类）
        self.document_classifier = nn.Sequential(
            nn.Linear(200, 128),  # 文档特征维度
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(128, 64),
            nn.ReLU(),
            nn.Linear(64, 5)  # 5个历史时期
        )

        # 事件关联分析
        self.event_analyzer = nn.Sequential(
            nn.Linear(50, 32),    # 事件特征
            nn.ReLU(),
            nn.Linear(32, 16),
            nn.ReLU(),
            nn.Linear(16, 1)      # 关联强度
        )

    def simulate_historical_documents(self, n_docs=500):
        """模拟历史文档数据"""
        # 生成文档特征（词频、主题分布等）
        documents = []
        labels = []

        periods = ['古代', '中世纪', '近世', '近代', '现代']

        for i in range(n_docs):
            period_id = np.random.randint(0, 5)

            # 根据时期生成不同的特征分布
            if period_id == 0:  # 古代
                features = np.random.exponential(0.5, 200)
            elif period_id == 1:  # 中世纪
                features = np.random.gamma(2, 0.5, 200)
            elif period_id == 2:  # 近世
                features = np.random.normal(1, 0.5, 200)
            elif period_id == 3:  # 近代
                features = np.random.lognormal(0, 0.5, 200)
            else:  # 现代
                features = np.random.beta(2, 2, 200)

            documents.append(features)
            labels.append(period_id)

        return np.array(documents), np.array(labels), periods

    def analyze_historical_trends(self):
        """分析历史趋势"""
        # 生成模拟数据
        docs, labels, periods = self.simulate_historical_documents()

        # 训练分类器
        X_tensor = torch.tensor(docs, dtype=torch.float32)
        y_tensor = torch.tensor(labels, dtype=torch.long)

        optimizer = torch.optim.Adam(self.document_classifier.parameters(), lr=0.001)

        for epoch in range(500):
            optimizer.zero_grad()
            outputs = self.document_classifier(X_tensor)
            loss = nn.CrossEntropyLoss()(outputs, y_tensor)
            loss.backward()
            optimizer.step()

        # 预测和分析
        with torch.no_grad():
            predictions = torch.softmax(self.document_classifier(X_tensor), dim=1)

        # 可视化结果
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))

        # 时期分布
        axes[0, 0].bar(periods, np.bincount(labels), color='lightblue')
        axes[0, 0].set_title('历史文档时期分布')
        axes[0, 0].set_xlabel('历史时期')
        axes[0, 0].set_ylabel('文档数量')
        axes[0, 0].tick_params(axis='x', rotation=45)

        # 预测准确率
        pred_labels = torch.argmax(predictions, dim=1).numpy()
        accuracy = np.mean(pred_labels == labels)

        confusion_matrix = np.zeros((5, 5))
        for true, pred in zip(labels, pred_labels):
            confusion_matrix[true, pred] += 1

        axes[0, 1].imshow(confusion_matrix, cmap='Blues')
        axes[0, 1].set_title(f'混淆矩阵 (准确率: {accuracy:.3f})')
        axes[0, 1].set_xlabel('预测时期')
        axes[0, 1].set_ylabel('真实时期')

        # 添加数值标注
        for i in range(5):
            for j in range(5):
                axes[0, 1].text(j, i, f'{int(confusion_matrix[i, j])}',
                               ha='center', va='center')

        # 特征重要性分析
        feature_importance = np.abs(self.document_classifier[0].weight.data.numpy()).mean(axis=0)
        top_features = np.argsort(feature_importance)[-20:]

        axes[1, 0].barh(range(20), feature_importance[top_features])
        axes[1, 0].set_title('重要特征分析')
        axes[1, 0].set_xlabel('重要性得分')
        axes[1, 0].set_ylabel('特征编号')

        # 时期演化趋势
        period_evolution = predictions.numpy().mean(axis=0)
        axes[1, 1].plot(periods, period_evolution, 'o-', linewidth=2, markersize=8)
        axes[1, 1].set_title('历史时期特征演化')
        axes[1, 1].set_xlabel('历史时期')
        axes[1, 1].set_ylabel('平均特征强度')
        axes[1, 1].tick_params(axis='x', rotation=45)
        axes[1, 1].grid(True, alpha=0.3)

        plt.tight_layout()
        plt.show()

        return docs, labels, predictions.numpy()

# 演示历史学AI分析
hist_analyzer = HistoricalAnalysis()
docs, labels, predictions = hist_analyzer.analyze_historical_trends()
print("历史文档分析完成")
```

#### 创新应用设计（15分钟）

##### 跨学科融合项目设计
**智能教育系统**：
```python
class IntelligentEducationSystem:
    """智能教育系统"""

    def __init__(self):
        # 学习者建模
        self.learner_model = nn.Sequential(
            nn.Linear(15, 64),   # 学习者特征
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(64, 32),
            nn.ReLU(),
            nn.Linear(32, 10)    # 知识掌握程度
        )

        # 内容推荐系统
        self.content_recommender = nn.Sequential(
            nn.Linear(25, 64),   # 学习者+内容特征
            nn.ReLU(),
            nn.Linear(64, 32),
            nn.ReLU(),
            nn.Linear(32, 1)     # 推荐分数
        )

        # 学习效果预测
        self.effect_predictor = nn.Sequential(
            nn.Linear(20, 32),
            nn.ReLU(),
            nn.Linear(32, 16),
            nn.ReLU(),
            nn.Linear(16, 1)     # 学习效果分数
        )

    def simulate_learner_data(self, n_learners=1000):
        """模拟学习者数据"""
        # 学习者基本信息
        age = np.random.normal(16, 2, n_learners)
        prior_knowledge = np.random.beta(2, 3, n_learners)
        learning_style = np.random.randint(0, 4, n_learners)  # 4种学习风格
        motivation = np.random.beta(3, 2, n_learners)

        # 学习行为数据
        study_time = np.random.gamma(2, 2, n_learners)
        interaction_frequency = np.random.poisson(5, n_learners)
        completion_rate = np.random.beta(5, 2, n_learners)

        # 认知能力
        working_memory = np.random.normal(7, 2, n_learners)
        processing_speed = np.random.normal(100, 15, n_learners)

        # 构造特征矩阵
        learner_features = np.column_stack([
            age, prior_knowledge, learning_style, motivation,
            study_time, interaction_frequency, completion_rate,
            working_memory, processing_speed,
            np.random.randn(n_learners, 6)  # 其他特征
        ])

        return learner_features

    def adaptive_learning_path(self, learner_features):
        """自适应学习路径生成"""
        # 预测学习者的知识掌握程度
        with torch.no_grad():
            features_tensor = torch.tensor(learner_features, dtype=torch.float32)
            knowledge_levels = torch.sigmoid(self.learner_model(features_tensor))

        # 生成个性化学习路径
        learning_paths = []
        subjects = ['数学', '物理', '化学', '生物', '历史', '地理', '语文', '英语', '计算机', 'AI']

        for i, levels in enumerate(knowledge_levels):
            # 根据知识掌握程度排序学习内容
            subject_scores = levels.numpy()
            sorted_indices = np.argsort(subject_scores)

            # 构建学习路径（从薄弱环节开始）
            path = [subjects[idx] for idx in sorted_indices[:5]]
            learning_paths.append(path)

        return learning_paths, knowledge_levels.numpy()

    def visualize_education_system(self, learner_features, knowledge_levels):
        """可视化教育系统"""
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))

        # 学习者能力分布
        axes[0, 0].hist(learner_features[:, 1], bins=30, alpha=0.7, color='blue')
        axes[0, 0].set_title('先验知识分布')
        axes[0, 0].set_xlabel('先验知识水平')
        axes[0, 0].set_ylabel('学习者数量')
        axes[0, 0].grid(True, alpha=0.3)

        # 学习风格分布
        style_counts = np.bincount(learner_features[:, 2].astype(int))
        style_labels = ['视觉型', '听觉型', '动觉型', '读写型']
        axes[0, 1].pie(style_counts, labels=style_labels, autopct='%1.1f%%')
        axes[0, 1].set_title('学习风格分布')

        # 知识掌握热图
        axes[0, 2].imshow(knowledge_levels[:50].T, cmap='viridis', aspect='auto')
        axes[0, 2].set_title('知识掌握程度热图（前50名学习者）')
        axes[0, 2].set_xlabel('学习者编号')
        axes[0, 2].set_ylabel('知识点')

        # 学习时间vs效果
        study_time = learner_features[:, 4]
        avg_knowledge = knowledge_levels.mean(axis=1)
        axes[1, 0].scatter(study_time, avg_knowledge, alpha=0.6)
        axes[1, 0].set_title('学习时间与知识掌握关系')
        axes[1, 0].set_xlabel('学习时间')
        axes[1, 0].set_ylabel('平均知识掌握程度')
        axes[1, 0].grid(True, alpha=0.3)

        # 动机vs完成率
        motivation = learner_features[:, 3]
        completion_rate = learner_features[:, 6]
        axes[1, 1].scatter(motivation, completion_rate, alpha=0.6, c='red')
        axes[1, 1].set_title('学习动机与完成率关系')
        axes[1, 1].set_xlabel('学习动机')
        axes[1, 1].set_ylabel('完成率')
        axes[1, 1].grid(True, alpha=0.3)

        # 个性化推荐效果
        subjects = ['数学', '物理', '化学', '生物', '历史', '地理', '语文', '英语', '计算机', 'AI']
        avg_knowledge_by_subject = knowledge_levels.mean(axis=0)
        axes[1, 2].bar(subjects, avg_knowledge_by_subject, color='green', alpha=0.7)
        axes[1, 2].set_title('各学科平均掌握程度')
        axes[1, 2].set_xlabel('学科')
        axes[1, 2].set_ylabel('平均掌握程度')
        axes[1, 2].tick_params(axis='x', rotation=45)
        axes[1, 2].grid(True, alpha=0.3)

        plt.tight_layout()
        plt.show()

# 演示智能教育系统
edu_system = IntelligentEducationSystem()
learner_data = edu_system.simulate_learner_data()
learning_paths, knowledge_data = edu_system.adaptive_learning_path(learner_data)
edu_system.visualize_education_system(learner_data, knowledge_data)

print("智能教育系统演示完成")
print(f"生成了 {len(learning_paths)} 个个性化学习路径")
```

#### 总结反思（10分钟）
**核心要点回顾**：
- AI技术正在深刻改变各个学科的研究方法和应用模式
- 跨学科融合需要理解不同领域的问题特点和数据特征
- AI为解决复杂的跨领域问题提供了新的工具和思路
- 成功的跨学科AI应用需要领域专家和AI专家的深度合作

## 📊 评估方式

### 过程性评价
- **跨学科理解**：对不同学科AI应用的理解程度
- **问题建模**：将学科问题转化为AI问题的能力
- **技术适配**：选择合适AI技术解决特定问题的能力
- **创新思维**：设计新颖跨学科应用的创造力

### 结果性评价
- **应用分析**：分析现有跨学科AI应用的原理和效果
- **方案设计**：设计解决特定学科问题的AI方案
- **系统实现**：实现简化的跨学科AI应用原型
- **效果评估**：评估跨学科AI应用的性能和影响

## 🏠 课后延伸

### 基础任务
1. **领域调研**：深入调研某个学科中AI应用的现状和发展
2. **问题建模**：选择一个跨学科问题，设计AI解决方案
3. **技术实现**：实现一个简单的跨学科AI应用

### 拓展任务
1. **创新应用**：设计全新的跨学科AI应用场景
2. **效果评估**：评估AI技术在特定领域的应用效果
3. **未来展望**：预测AI在某个学科未来的发展趋势

### 预习任务
了解强化学习的基本概念，思考智能决策在现实中的应用场景。

---

*本课程旨在帮助学生理解AI技术的跨学科应用潜力，培养跨领域思维和创新应用设计能力。*
