# 第4课：强化学习与智能决策

## 🎯 课程基本信息

- **课程名称**：强化学习与智能决策
- **适用年级**：高中十一年级
- **课时安排**：90分钟（2课时）
- **课程类型**：系统设计课
- **核心主题**：强化学习算法原理与智能决策系统设计

## 📚 教学目标

### 认知目标
- 理解强化学习的基本概念和核心原理
- 掌握马尔可夫决策过程和价值函数的数学基础
- 认识Q学习、策略梯度等主要强化学习算法
- 了解强化学习在智能决策中的应用模式

### 技能目标
- 能够建模简单的强化学习问题
- 掌握Q学习算法的实现和调优方法
- 学会设计和评估智能决策系统
- 能够分析强化学习算法的收敛性和稳定性

### 思维目标
- 培养序贯决策和长期规划的思维
- 发展探索与利用平衡的策略思维
- 建立奖励设计和环境建模的系统思维
- 培养智能体自主学习的设计思维

### 价值观目标
- 认识智能决策对提高效率的价值
- 培养负责任的AI决策系统设计理念
- 增强对AI自主性和可控性的思考
- 建立人机协作的智能决策观念

## 🎯 教学流程

### 第一课时（45分钟）

#### 导入环节（10分钟）
**智能决策案例展示**：
- 展示AlphaGo在围棋中的决策过程
- 演示自动驾驶汽车的路径规划决策
- 介绍推荐系统的个性化决策机制

**核心问题**：
- "智能体如何在不确定环境中做出最优决策？"
- "如何平衡短期收益和长期目标？"
- "机器如何从试错中学习最优策略？"

#### 新课讲授（25分钟）

##### 1. 强化学习基础概念（15分钟）
**马尔可夫决策过程**：
```python
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from collections import defaultdict
import random

class GridWorld:
    """网格世界环境"""
    
    def __init__(self, width=5, height=5):
        self.width = width
        self.height = height
        self.grid = np.zeros((height, width))
        
        # 设置奖励
        self.rewards = {}
        self.rewards[(4, 4)] = 10  # 目标位置
        self.rewards[(2, 2)] = -5  # 陷阱
        self.rewards[(1, 3)] = -5  # 陷阱
        
        # 设置障碍物
        self.obstacles = [(1, 1), (3, 1), (3, 3)]
        
        # 动作空间：上、下、左、右
        self.actions = ['up', 'down', 'left', 'right']
        self.action_effects = {
            'up': (-1, 0),
            'down': (1, 0),
            'left': (0, -1),
            'right': (0, 1)
        }
        
        self.reset()
    
    def reset(self):
        """重置环境"""
        self.agent_pos = (0, 0)
        return self.agent_pos
    
    def step(self, action):
        """执行动作"""
        if action not in self.actions:
            raise ValueError(f"Invalid action: {action}")
        
        # 计算新位置
        dy, dx = self.action_effects[action]
        new_y = max(0, min(self.height - 1, self.agent_pos[0] + dy))
        new_x = max(0, min(self.width - 1, self.agent_pos[1] + dx))
        new_pos = (new_y, new_x)
        
        # 检查是否撞到障碍物
        if new_pos not in self.obstacles:
            self.agent_pos = new_pos
        
        # 计算奖励
        reward = self.rewards.get(self.agent_pos, -0.1)  # 默认小负奖励
        
        # 检查是否结束
        done = self.agent_pos == (4, 4)
        
        return self.agent_pos, reward, done
    
    def get_valid_actions(self, pos):
        """获取有效动作"""
        valid_actions = []
        for action in self.actions:
            dy, dx = self.action_effects[action]
            new_y = max(0, min(self.height - 1, pos[0] + dy))
            new_x = max(0, min(self.width - 1, pos[1] + dx))
            new_pos = (new_y, new_x)
            
            if new_pos not in self.obstacles:
                valid_actions.append(action)
        
        return valid_actions
    
    def visualize(self, values=None, policy=None):
        """可视化环境"""
        fig, axes = plt.subplots(1, 3, figsize=(15, 5))
        
        # 环境布局
        env_grid = np.zeros((self.height, self.width))
        for pos in self.obstacles:
            env_grid[pos] = -1  # 障碍物
        for pos, reward in self.rewards.items():
            if reward > 0:
                env_grid[pos] = 1  # 目标
            elif reward < 0:
                env_grid[pos] = -0.5  # 陷阱
        
        axes[0].imshow(env_grid, cmap='RdYlGn', alpha=0.8)
        axes[0].set_title('环境布局')
        axes[0].set_xlabel('X坐标')
        axes[0].set_ylabel('Y坐标')
        
        # 添加标注
        for i in range(self.height):
            for j in range(self.width):
                if (i, j) in self.obstacles:
                    axes[0].text(j, i, '■', ha='center', va='center', fontsize=20)
                elif (i, j) in self.rewards:
                    if self.rewards[(i, j)] > 0:
                        axes[0].text(j, i, '★', ha='center', va='center', fontsize=20)
                    else:
                        axes[0].text(j, i, '✗', ha='center', va='center', fontsize=20)
        
        # 价值函数可视化
        if values is not None:
            value_grid = np.zeros((self.height, self.width))
            for (i, j), v in values.items():
                value_grid[i, j] = v
            
            im = axes[1].imshow(value_grid, cmap='viridis')
            axes[1].set_title('状态价值函数')
            axes[1].set_xlabel('X坐标')
            axes[1].set_ylabel('Y坐标')
            plt.colorbar(im, ax=axes[1])
            
            # 添加数值标注
            for i in range(self.height):
                for j in range(self.width):
                    if (i, j) not in self.obstacles:
                        axes[1].text(j, i, f'{value_grid[i, j]:.1f}', 
                                   ha='center', va='center', color='white')
        
        # 策略可视化
        if policy is not None:
            policy_grid = np.zeros((self.height, self.width))
            arrows = {'up': '↑', 'down': '↓', 'left': '←', 'right': '→'}
            
            axes[2].imshow(policy_grid, cmap='gray', alpha=0.3)
            axes[2].set_title('最优策略')
            axes[2].set_xlabel('X坐标')
            axes[2].set_ylabel('Y坐标')
            
            for (i, j), action in policy.items():
                if (i, j) not in self.obstacles:
                    axes[2].text(j, i, arrows.get(action, '?'), 
                               ha='center', va='center', fontsize=20)
        
        plt.tight_layout()
        plt.show()

# 创建网格世界环境
env = GridWorld()
env.visualize()
```

**价值函数和贝尔曼方程**：
```python
class ValueIteration:
    """价值迭代算法"""
    
    def __init__(self, env, gamma=0.9, theta=1e-6):
        self.env = env
        self.gamma = gamma  # 折扣因子
        self.theta = theta  # 收敛阈值
        
        # 初始化价值函数
        self.V = defaultdict(float)
        for i in range(env.height):
            for j in range(env.width):
                if (i, j) not in env.obstacles:
                    self.V[(i, j)] = 0.0
    
    def value_iteration(self):
        """价值迭代"""
        iteration = 0
        value_history = []
        
        while True:
            delta = 0
            new_V = self.V.copy()
            
            # 对每个状态更新价值
            for state in self.V.keys():
                if state == (4, 4):  # 终止状态
                    continue
                
                # 计算所有动作的价值
                action_values = []
                valid_actions = self.env.get_valid_actions(state)
                
                for action in valid_actions:
                    # 模拟执行动作
                    old_pos = self.env.agent_pos
                    self.env.agent_pos = state
                    next_state, reward, done = self.env.step(action)
                    self.env.agent_pos = old_pos
                    
                    # 计算动作价值
                    if done:
                        action_value = reward
                    else:
                        action_value = reward + self.gamma * self.V[next_state]
                    action_values.append(action_value)
                
                # 更新价值函数
                if action_values:
                    new_V[state] = max(action_values)
                    delta = max(delta, abs(new_V[state] - self.V[state]))
            
            self.V = new_V
            value_history.append(dict(self.V))
            iteration += 1
            
            print(f"迭代 {iteration}: 最大价值变化 = {delta:.6f}")
            
            if delta < self.theta:
                break
        
        return value_history
    
    def extract_policy(self):
        """提取最优策略"""
        policy = {}
        
        for state in self.V.keys():
            if state == (4, 4):  # 终止状态
                continue
            
            best_action = None
            best_value = float('-inf')
            valid_actions = self.env.get_valid_actions(state)
            
            for action in valid_actions:
                # 模拟执行动作
                old_pos = self.env.agent_pos
                self.env.agent_pos = state
                next_state, reward, done = self.env.step(action)
                self.env.agent_pos = old_pos
                
                # 计算动作价值
                if done:
                    action_value = reward
                else:
                    action_value = reward + self.gamma * self.V[next_state]
                
                if action_value > best_value:
                    best_value = action_value
                    best_action = action
            
            policy[state] = best_action
        
        return policy
    
    def visualize_convergence(self, value_history):
        """可视化收敛过程"""
        # 选择几个关键状态跟踪价值变化
        key_states = [(0, 0), (2, 0), (4, 0), (0, 4)]
        
        plt.figure(figsize=(12, 8))
        
        for state in key_states:
            if state in self.V:
                values = [vh.get(state, 0) for vh in value_history]
                plt.plot(values, label=f'状态 {state}', linewidth=2)
        
        plt.title('价值函数收敛过程')
        plt.xlabel('迭代次数')
        plt.ylabel('状态价值')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.show()

# 运行价值迭代
vi = ValueIteration(env)
value_history = vi.value_iteration()
policy = vi.extract_policy()

# 可视化结果
env.visualize(values=vi.V, policy=policy)
vi.visualize_convergence(value_history)
```

##### 2. Q学习算法（10分钟）
**Q学习实现**：
```python
class QLearning:
    """Q学习算法"""
    
    def __init__(self, env, alpha=0.1, gamma=0.9, epsilon=0.1):
        self.env = env
        self.alpha = alpha      # 学习率
        self.gamma = gamma      # 折扣因子
        self.epsilon = epsilon  # 探索率
        
        # 初始化Q表
        self.Q = defaultdict(lambda: defaultdict(float))
        for i in range(env.height):
            for j in range(env.width):
                if (i, j) not in env.obstacles:
                    for action in env.actions:
                        self.Q[(i, j)][action] = 0.0
    
    def choose_action(self, state):
        """ε-贪婪策略选择动作"""
        valid_actions = self.env.get_valid_actions(state)
        
        if random.random() < self.epsilon:
            # 探索：随机选择动作
            return random.choice(valid_actions)
        else:
            # 利用：选择Q值最大的动作
            q_values = {action: self.Q[state][action] for action in valid_actions}
            return max(q_values, key=q_values.get)
    
    def update_q_value(self, state, action, reward, next_state, done):
        """更新Q值"""
        if done:
            target = reward
        else:
            # 计算下一状态的最大Q值
            valid_next_actions = self.env.get_valid_actions(next_state)
            if valid_next_actions:
                max_next_q = max(self.Q[next_state][a] for a in valid_next_actions)
            else:
                max_next_q = 0
            target = reward + self.gamma * max_next_q
        
        # Q学习更新规则
        self.Q[state][action] += self.alpha * (target - self.Q[state][action])
    
    def train(self, episodes=1000):
        """训练Q学习智能体"""
        episode_rewards = []
        episode_lengths = []
        
        for episode in range(episodes):
            state = self.env.reset()
            total_reward = 0
            steps = 0
            
            while steps < 100:  # 最大步数限制
                action = self.choose_action(state)
                next_state, reward, done = self.env.step(action)
                
                self.update_q_value(state, action, reward, next_state, done)
                
                state = next_state
                total_reward += reward
                steps += 1
                
                if done:
                    break
            
            episode_rewards.append(total_reward)
            episode_lengths.append(steps)
            
            # 衰减探索率
            if episode % 100 == 0:
                self.epsilon = max(0.01, self.epsilon * 0.95)
                avg_reward = np.mean(episode_rewards[-100:])
                print(f"Episode {episode}: 平均奖励 = {avg_reward:.2f}, ε = {self.epsilon:.3f}")
        
        return episode_rewards, episode_lengths
    
    def extract_policy(self):
        """从Q表提取策略"""
        policy = {}
        
        for state in self.Q.keys():
            if state == (4, 4):  # 终止状态
                continue
            
            valid_actions = self.env.get_valid_actions(state)
            if valid_actions:
                q_values = {action: self.Q[state][action] for action in valid_actions}
                policy[state] = max(q_values, key=q_values.get)
        
        return policy
    
    def visualize_q_table(self):
        """可视化Q表"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        actions = ['up', 'down', 'left', 'right']
        
        for idx, action in enumerate(actions):
            ax = axes[idx // 2, idx % 2]
            
            # 创建Q值矩阵
            q_matrix = np.zeros((self.env.height, self.env.width))
            for i in range(self.env.height):
                for j in range(self.env.width):
                    if (i, j) not in self.env.obstacles:
                        q_matrix[i, j] = self.Q[(i, j)][action]
            
            # 绘制热图
            im = ax.imshow(q_matrix, cmap='viridis')
            ax.set_title(f'Q值 - {action}')
            ax.set_xlabel('X坐标')
            ax.set_ylabel('Y坐标')
            
            # 添加数值标注
            for i in range(self.env.height):
                for j in range(self.env.width):
                    if (i, j) not in self.env.obstacles:
                        ax.text(j, i, f'{q_matrix[i, j]:.1f}', 
                               ha='center', va='center', color='white')
            
            plt.colorbar(im, ax=ax)
        
        plt.tight_layout()
        plt.show()

# 训练Q学习智能体
q_agent = QLearning(env)
rewards, lengths = q_agent.train(episodes=2000)
q_policy = q_agent.extract_policy()

# 可视化训练过程
plt.figure(figsize=(15, 5))

plt.subplot(1, 3, 1)
plt.plot(rewards)
plt.title('训练奖励变化')
plt.xlabel('回合')
plt.ylabel('总奖励')
plt.grid(True, alpha=0.3)

plt.subplot(1, 3, 2)
plt.plot(lengths)
plt.title('回合长度变化')
plt.xlabel('回合')
plt.ylabel('步数')
plt.grid(True, alpha=0.3)

plt.subplot(1, 3, 3)
# 计算移动平均
window = 100
moving_avg = np.convolve(rewards, np.ones(window)/window, mode='valid')
plt.plot(moving_avg)
plt.title(f'奖励移动平均（窗口={window}）')
plt.xlabel('回合')
plt.ylabel('平均奖励')
plt.grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

# 可视化Q表和策略
q_agent.visualize_q_table()
env.visualize(policy=q_policy)
```

#### 实践体验（10分钟）
**强化学习环境设计**：
学生分组设计简单的强化学习环境，定义状态、动作和奖励

### 第二课时（45分钟）

#### 深入分析（20分钟）

##### 1. 策略梯度方法（12分钟）
**REINFORCE算法**：
```python
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.distributions import Categorical

class PolicyNetwork(nn.Module):
    """策略网络"""

    def __init__(self, state_dim, action_dim, hidden_dim=64):
        super(PolicyNetwork, self).__init__()
        self.fc1 = nn.Linear(state_dim, hidden_dim)
        self.fc2 = nn.Linear(hidden_dim, hidden_dim)
        self.fc3 = nn.Linear(hidden_dim, action_dim)

    def forward(self, state):
        x = F.relu(self.fc1(state))
        x = F.relu(self.fc2(x))
        action_probs = F.softmax(self.fc3(x), dim=-1)
        return action_probs

class REINFORCE:
    """REINFORCE算法实现"""

    def __init__(self, state_dim, action_dim, lr=0.01, gamma=0.99):
        self.policy_net = PolicyNetwork(state_dim, action_dim)
        self.optimizer = optim.Adam(self.policy_net.parameters(), lr=lr)
        self.gamma = gamma

        # 存储轨迹
        self.log_probs = []
        self.rewards = []

    def select_action(self, state):
        """选择动作"""
        state_tensor = torch.FloatTensor(state).unsqueeze(0)
        action_probs = self.policy_net(state_tensor)

        # 创建分布并采样
        dist = Categorical(action_probs)
        action = dist.sample()

        # 保存log概率
        self.log_probs.append(dist.log_prob(action))

        return action.item()

    def update_policy(self):
        """更新策略"""
        # 计算折扣奖励
        discounted_rewards = []
        R = 0
        for reward in reversed(self.rewards):
            R = reward + self.gamma * R
            discounted_rewards.insert(0, R)

        # 标准化奖励
        discounted_rewards = torch.FloatTensor(discounted_rewards)
        discounted_rewards = (discounted_rewards - discounted_rewards.mean()) / (discounted_rewards.std() + 1e-8)

        # 计算策略损失
        policy_loss = []
        for log_prob, reward in zip(self.log_probs, discounted_rewards):
            policy_loss.append(-log_prob * reward)

        # 反向传播
        self.optimizer.zero_grad()
        policy_loss = torch.stack(policy_loss).sum()
        policy_loss.backward()
        self.optimizer.step()

        # 清空轨迹
        self.log_probs = []
        self.rewards = []

        return policy_loss.item()

class CartPoleEnvironment:
    """简化的CartPole环境"""

    def __init__(self):
        self.reset()
        self.max_steps = 200

    def reset(self):
        """重置环境"""
        # 状态：[位置, 速度, 角度, 角速度]
        self.state = np.random.uniform(-0.1, 0.1, 4)
        self.steps = 0
        return self.state.copy()

    def step(self, action):
        """执行动作"""
        # 简化的物理模拟
        force = 1.0 if action == 1 else -1.0

        # 更新状态（简化版）
        self.state[1] += 0.1 * force  # 速度
        self.state[0] += 0.1 * self.state[1]  # 位置
        self.state[3] += 0.1 * (force + 0.1 * self.state[2])  # 角速度
        self.state[2] += 0.1 * self.state[3]  # 角度

        # 添加噪声
        self.state += np.random.normal(0, 0.01, 4)

        self.steps += 1

        # 检查终止条件
        done = (abs(self.state[0]) > 2.0 or  # 位置超出范围
                abs(self.state[2]) > 0.5 or  # 角度过大
                self.steps >= self.max_steps)

        # 奖励函数
        if done and self.steps < self.max_steps:
            reward = -10  # 失败惩罚
        else:
            reward = 1    # 存活奖励

        return self.state.copy(), reward, done

def train_reinforce():
    """训练REINFORCE智能体"""
    env = CartPoleEnvironment()
    agent = REINFORCE(state_dim=4, action_dim=2, lr=0.01)

    episode_rewards = []
    episode_lengths = []
    policy_losses = []

    for episode in range(1000):
        state = env.reset()
        total_reward = 0

        # 收集一个回合的经验
        while True:
            action = agent.select_action(state)
            next_state, reward, done = env.step(action)

            agent.rewards.append(reward)
            total_reward += reward
            state = next_state

            if done:
                break

        # 更新策略
        loss = agent.update_policy()

        episode_rewards.append(total_reward)
        episode_lengths.append(len(agent.rewards))
        policy_losses.append(loss)

        if episode % 100 == 0:
            avg_reward = np.mean(episode_rewards[-100:])
            print(f"Episode {episode}: 平均奖励 = {avg_reward:.2f}")

    return episode_rewards, episode_lengths, policy_losses

# 训练REINFORCE智能体
reinforce_rewards, reinforce_lengths, reinforce_losses = train_reinforce()

# 可视化训练结果
fig, axes = plt.subplots(2, 2, figsize=(15, 10))

# 奖励变化
axes[0, 0].plot(reinforce_rewards, alpha=0.6)
window = 50
moving_avg = np.convolve(reinforce_rewards, np.ones(window)/window, mode='valid')
axes[0, 0].plot(range(window-1, len(reinforce_rewards)), moving_avg, 'r-', linewidth=2)
axes[0, 0].set_title('REINFORCE训练奖励')
axes[0, 0].set_xlabel('回合')
axes[0, 0].set_ylabel('总奖励')
axes[0, 0].grid(True, alpha=0.3)

# 回合长度
axes[0, 1].plot(reinforce_lengths, alpha=0.6)
moving_avg_len = np.convolve(reinforce_lengths, np.ones(window)/window, mode='valid')
axes[0, 1].plot(range(window-1, len(reinforce_lengths)), moving_avg_len, 'r-', linewidth=2)
axes[0, 1].set_title('回合长度变化')
axes[0, 1].set_xlabel('回合')
axes[0, 1].set_ylabel('步数')
axes[0, 1].grid(True, alpha=0.3)

# 策略损失
axes[1, 0].plot(reinforce_losses, alpha=0.6)
axes[1, 0].set_title('策略损失变化')
axes[1, 0].set_xlabel('回合')
axes[1, 0].set_ylabel('损失值')
axes[1, 0].grid(True, alpha=0.3)

# 性能分布
axes[1, 1].hist(reinforce_rewards[-200:], bins=20, alpha=0.7, color='green')
axes[1, 1].set_title('最近200回合奖励分布')
axes[1, 1].set_xlabel('总奖励')
axes[1, 1].set_ylabel('频次')
axes[1, 1].grid(True, alpha=0.3)

plt.tight_layout()
plt.show()
```

##### 2. 深度Q网络（DQN）（8分钟）
**DQN实现**：
```python
import torch.nn as nn
import torch.optim as optim
import random
from collections import deque

class DQN(nn.Module):
    """深度Q网络"""

    def __init__(self, state_dim, action_dim, hidden_dim=128):
        super(DQN, self).__init__()
        self.fc1 = nn.Linear(state_dim, hidden_dim)
        self.fc2 = nn.Linear(hidden_dim, hidden_dim)
        self.fc3 = nn.Linear(hidden_dim, action_dim)

    def forward(self, state):
        x = F.relu(self.fc1(state))
        x = F.relu(self.fc2(x))
        q_values = self.fc3(x)
        return q_values

class DQNAgent:
    """DQN智能体"""

    def __init__(self, state_dim, action_dim, lr=0.001, gamma=0.99,
                 epsilon=1.0, epsilon_decay=0.995, epsilon_min=0.01):
        self.state_dim = state_dim
        self.action_dim = action_dim
        self.gamma = gamma
        self.epsilon = epsilon
        self.epsilon_decay = epsilon_decay
        self.epsilon_min = epsilon_min

        # 神经网络
        self.q_network = DQN(state_dim, action_dim)
        self.target_network = DQN(state_dim, action_dim)
        self.optimizer = optim.Adam(self.q_network.parameters(), lr=lr)

        # 经验回放
        self.memory = deque(maxlen=10000)
        self.batch_size = 32

        # 更新目标网络
        self.update_target_network()

    def update_target_network(self):
        """更新目标网络"""
        self.target_network.load_state_dict(self.q_network.state_dict())

    def remember(self, state, action, reward, next_state, done):
        """存储经验"""
        self.memory.append((state, action, reward, next_state, done))

    def act(self, state):
        """选择动作"""
        if random.random() <= self.epsilon:
            return random.randrange(self.action_dim)

        state_tensor = torch.FloatTensor(state).unsqueeze(0)
        q_values = self.q_network(state_tensor)
        return q_values.argmax().item()

    def replay(self):
        """经验回放训练"""
        if len(self.memory) < self.batch_size:
            return 0

        # 随机采样批次
        batch = random.sample(self.memory, self.batch_size)
        states = torch.FloatTensor([e[0] for e in batch])
        actions = torch.LongTensor([e[1] for e in batch])
        rewards = torch.FloatTensor([e[2] for e in batch])
        next_states = torch.FloatTensor([e[3] for e in batch])
        dones = torch.BoolTensor([e[4] for e in batch])

        # 当前Q值
        current_q_values = self.q_network(states).gather(1, actions.unsqueeze(1))

        # 目标Q值
        next_q_values = self.target_network(next_states).max(1)[0].detach()
        target_q_values = rewards + (self.gamma * next_q_values * ~dones)

        # 计算损失
        loss = F.mse_loss(current_q_values.squeeze(), target_q_values)

        # 反向传播
        self.optimizer.zero_grad()
        loss.backward()
        self.optimizer.step()

        # 衰减探索率
        if self.epsilon > self.epsilon_min:
            self.epsilon *= self.epsilon_decay

        return loss.item()

def train_dqn():
    """训练DQN智能体"""
    env = CartPoleEnvironment()
    agent = DQNAgent(state_dim=4, action_dim=2)

    episode_rewards = []
    episode_lengths = []
    losses = []

    for episode in range(1000):
        state = env.reset()
        total_reward = 0
        episode_loss = 0
        steps = 0

        while True:
            action = agent.act(state)
            next_state, reward, done = env.step(action)

            agent.remember(state, action, reward, next_state, done)
            state = next_state
            total_reward += reward
            steps += 1

            # 训练网络
            loss = agent.replay()
            if loss > 0:
                episode_loss += loss

            if done:
                break

        episode_rewards.append(total_reward)
        episode_lengths.append(steps)
        losses.append(episode_loss / max(1, steps))

        # 定期更新目标网络
        if episode % 100 == 0:
            agent.update_target_network()
            avg_reward = np.mean(episode_rewards[-100:])
            print(f"Episode {episode}: 平均奖励 = {avg_reward:.2f}, ε = {agent.epsilon:.3f}")

    return episode_rewards, episode_lengths, losses

# 训练DQN智能体
dqn_rewards, dqn_lengths, dqn_losses = train_dqn()

# 比较Q学习和DQN性能
plt.figure(figsize=(15, 10))

# 奖励比较
plt.subplot(2, 2, 1)
window = 50
q_moving_avg = np.convolve(rewards, np.ones(window)/window, mode='valid')
dqn_moving_avg = np.convolve(dqn_rewards, np.ones(window)/window, mode='valid')

plt.plot(range(window-1, len(rewards)), q_moving_avg, label='Q-Learning', linewidth=2)
plt.plot(range(window-1, len(dqn_rewards)), dqn_moving_avg, label='DQN', linewidth=2)
plt.title('算法性能比较')
plt.xlabel('回合')
plt.ylabel('移动平均奖励')
plt.legend()
plt.grid(True, alpha=0.3)

# DQN损失
plt.subplot(2, 2, 2)
plt.plot(dqn_losses, alpha=0.6)
plt.title('DQN训练损失')
plt.xlabel('回合')
plt.ylabel('平均损失')
plt.grid(True, alpha=0.3)

# 收敛速度比较
plt.subplot(2, 2, 3)
plt.plot(np.cumsum(q_moving_avg > 150), label='Q-Learning', linewidth=2)
plt.plot(np.cumsum(dqn_moving_avg > 150), label='DQN', linewidth=2)
plt.title('收敛速度比较（奖励>150的回合数）')
plt.xlabel('回合')
plt.ylabel('累计成功回合')
plt.legend()
plt.grid(True, alpha=0.3)

# 最终性能分布
plt.subplot(2, 2, 4)
plt.hist(rewards[-200:], bins=20, alpha=0.5, label='Q-Learning', density=True)
plt.hist(dqn_rewards[-200:], bins=20, alpha=0.5, label='DQN', density=True)
plt.title('最终性能分布')
plt.xlabel('总奖励')
plt.ylabel('密度')
plt.legend()
plt.grid(True, alpha=0.3)

plt.tight_layout()
plt.show()
```

#### 智能决策系统设计（15分钟）

##### 多智能体强化学习
**协作与竞争环境**：
```python
class MultiAgentEnvironment:
    """多智能体环境"""

    def __init__(self, grid_size=8, n_agents=3, n_targets=5):
        self.grid_size = grid_size
        self.n_agents = n_agents
        self.n_targets = n_targets
        self.reset()

    def reset(self):
        """重置环境"""
        # 随机放置智能体
        self.agent_positions = []
        for _ in range(self.n_agents):
            pos = (np.random.randint(0, self.grid_size),
                   np.random.randint(0, self.grid_size))
            self.agent_positions.append(pos)

        # 随机放置目标
        self.target_positions = []
        for _ in range(self.n_targets):
            pos = (np.random.randint(0, self.grid_size),
                   np.random.randint(0, self.grid_size))
            self.target_positions.append(pos)

        self.collected_targets = set()
        return self.get_observations()

    def get_observations(self):
        """获取所有智能体的观察"""
        observations = []
        for i, agent_pos in enumerate(self.agent_positions):
            obs = np.zeros((self.grid_size, self.grid_size, 3))  # 3个通道

            # 通道0：自己的位置
            obs[agent_pos[0], agent_pos[1], 0] = 1

            # 通道1：其他智能体位置
            for j, other_pos in enumerate(self.agent_positions):
                if i != j:
                    obs[other_pos[0], other_pos[1], 1] = 1

            # 通道2：目标位置
            for target_pos in self.target_positions:
                if target_pos not in self.collected_targets:
                    obs[target_pos[0], target_pos[1], 2] = 1

            observations.append(obs.flatten())

        return observations

    def step(self, actions):
        """执行所有智能体的动作"""
        rewards = [0] * self.n_agents

        # 移动智能体
        for i, action in enumerate(actions):
            old_pos = self.agent_positions[i]
            new_pos = self.move_agent(old_pos, action)
            self.agent_positions[i] = new_pos

            # 检查是否收集到目标
            if new_pos in self.target_positions and new_pos not in self.collected_targets:
                self.collected_targets.add(new_pos)
                rewards[i] += 10  # 收集奖励

                # 给其他智能体小额奖励（协作）
                for j in range(self.n_agents):
                    if j != i:
                        rewards[j] += 1

            # 移动惩罚
            rewards[i] -= 0.1

        # 检查碰撞惩罚
        for i in range(self.n_agents):
            for j in range(i+1, self.n_agents):
                if self.agent_positions[i] == self.agent_positions[j]:
                    rewards[i] -= 5
                    rewards[j] -= 5

        done = len(self.collected_targets) == self.n_targets
        return self.get_observations(), rewards, done

    def move_agent(self, pos, action):
        """移动智能体"""
        moves = [(0, 0), (-1, 0), (1, 0), (0, -1), (0, 1)]  # 停止，上，下，左，右
        if action < len(moves):
            dy, dx = moves[action]
            new_y = max(0, min(self.grid_size - 1, pos[0] + dy))
            new_x = max(0, min(self.grid_size - 1, pos[1] + dx))
            return (new_y, new_x)
        return pos

    def visualize(self):
        """可视化环境"""
        grid = np.zeros((self.grid_size, self.grid_size, 3))

        # 绘制目标（绿色）
        for target_pos in self.target_positions:
            if target_pos not in self.collected_targets:
                grid[target_pos[0], target_pos[1], 1] = 1

        # 绘制智能体（红色）
        for agent_pos in self.agent_positions:
            grid[agent_pos[0], agent_pos[1], 0] = 1

        plt.figure(figsize=(8, 8))
        plt.imshow(grid)
        plt.title(f'多智能体环境 (收集: {len(self.collected_targets)}/{self.n_targets})')
        plt.axis('off')
        plt.show()

class MultiAgentQLearning:
    """多智能体Q学习"""

    def __init__(self, n_agents, state_dim, action_dim=5):
        self.n_agents = n_agents
        self.agents = []

        for i in range(n_agents):
            agent = {
                'Q': defaultdict(lambda: defaultdict(float)),
                'alpha': 0.1,
                'gamma': 0.9,
                'epsilon': 0.1
            }
            self.agents.append(agent)

    def choose_actions(self, observations):
        """选择所有智能体的动作"""
        actions = []
        for i, obs in enumerate(observations):
            obs_key = tuple(obs)
            agent = self.agents[i]

            if random.random() < agent['epsilon']:
                action = random.randint(0, 4)
            else:
                q_values = [agent['Q'][obs_key][a] for a in range(5)]
                action = np.argmax(q_values)

            actions.append(action)

        return actions

    def update(self, observations, actions, rewards, next_observations, done):
        """更新所有智能体的Q值"""
        for i in range(self.n_agents):
            obs_key = tuple(observations[i])
            next_obs_key = tuple(next_observations[i])
            agent = self.agents[i]

            if done:
                target = rewards[i]
            else:
                next_q_values = [agent['Q'][next_obs_key][a] for a in range(5)]
                target = rewards[i] + agent['gamma'] * max(next_q_values)

            agent['Q'][obs_key][actions[i]] += agent['alpha'] * (
                target - agent['Q'][obs_key][actions[i]]
            )

def train_multi_agent():
    """训练多智能体系统"""
    env = MultiAgentEnvironment()
    agents = MultiAgentQLearning(n_agents=3, state_dim=env.grid_size*env.grid_size*3)

    episode_rewards = []
    collection_rates = []

    for episode in range(500):
        observations = env.reset()
        total_rewards = [0] * env.n_agents

        for step in range(100):
            actions = agents.choose_actions(observations)
            next_observations, rewards, done = env.step(actions)

            agents.update(observations, actions, rewards, next_observations, done)

            observations = next_observations
            for i in range(env.n_agents):
                total_rewards[i] += rewards[i]

            if done:
                break

        episode_rewards.append(total_rewards)
        collection_rates.append(len(env.collected_targets) / env.n_targets)

        # 衰减探索率
        for agent in agents.agents:
            agent['epsilon'] = max(0.01, agent['epsilon'] * 0.995)

        if episode % 100 == 0:
            avg_collection = np.mean(collection_rates[-100:])
            print(f"Episode {episode}: 平均收集率 = {avg_collection:.3f}")

    return episode_rewards, collection_rates

# 训练多智能体系统
multi_rewards, collection_rates = train_multi_agent()

# 可视化多智能体训练结果
plt.figure(figsize=(15, 5))

plt.subplot(1, 3, 1)
agent_rewards = np.array(multi_rewards)
for i in range(3):
    plt.plot(agent_rewards[:, i], label=f'智能体 {i+1}', alpha=0.7)
plt.title('各智能体奖励变化')
plt.xlabel('回合')
plt.ylabel('总奖励')
plt.legend()
plt.grid(True, alpha=0.3)

plt.subplot(1, 3, 2)
plt.plot(collection_rates, alpha=0.7)
window = 50
if len(collection_rates) >= window:
    moving_avg = np.convolve(collection_rates, np.ones(window)/window, mode='valid')
    plt.plot(range(window-1, len(collection_rates)), moving_avg, 'r-', linewidth=2)
plt.title('目标收集率')
plt.xlabel('回合')
plt.ylabel('收集率')
plt.grid(True, alpha=0.3)

plt.subplot(1, 3, 3)
team_rewards = agent_rewards.sum(axis=1)
plt.plot(team_rewards, alpha=0.7)
if len(team_rewards) >= window:
    team_moving_avg = np.convolve(team_rewards, np.ones(window)/window, mode='valid')
    plt.plot(range(window-1, len(team_rewards)), team_moving_avg, 'r-', linewidth=2)
plt.title('团队总奖励')
plt.xlabel('回合')
plt.ylabel('团队奖励')
plt.grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

# 展示最终环境状态
env = MultiAgentEnvironment()
env.reset()
env.visualize()
```

#### 总结反思（10分钟）
**核心要点回顾**：
- 强化学习通过试错学习实现智能决策
- 价值函数和策略是强化学习的两个核心概念
- Q学习和策略梯度是两类主要的强化学习方法
- 多智能体强化学习可以解决复杂的协作和竞争问题

## 📊 评估方式

### 过程性评价
- **概念理解**：对强化学习基本概念的掌握程度
- **算法分析**：分析不同强化学习算法的特点和适用性
- **环境建模**：设计强化学习环境的能力
- **系统思维**：理解智能决策系统的整体架构

### 结果性评价
- **算法实现**：实现基本的强化学习算法
- **环境设计**：设计有意义的强化学习环境
- **性能分析**：分析和优化强化学习系统的性能
- **应用设计**：设计智能决策系统的应用方案

## 🏠 课后延伸

### 基础任务
1. **算法比较**：比较Q学习和策略梯度方法的优缺点
2. **环境设计**：设计一个新的强化学习环境
3. **参数调优**：分析学习率、折扣因子等参数的影响

### 拓展任务
1. **高级算法**：实现Actor-Critic或PPO算法
2. **多智能体**：设计复杂的多智能体协作场景
3. **实际应用**：将强化学习应用到实际问题中

### 预习任务
了解AI系统架构设计的基本原则，思考如何构建大规模AI系统。

---

*本课程旨在帮助学生理解强化学习的核心原理和智能决策系统的设计方法，培养序贯决策思维和系统设计能力。*
