# 第5课：AI系统架构设计

## 🎯 课程基本信息

- **课程名称**：AI系统架构设计
- **适用年级**：高中十一年级
- **课时安排**：90分钟（2课时）
- **课程类型**：系统设计课
- **核心主题**：大规模AI系统的架构设计原理与实践

## 📚 教学目标

### 认知目标
- 理解AI系统架构设计的基本原则和方法
- 掌握分布式AI系统的设计模式和技术要点
- 认识AI系统的可扩展性、可靠性和性能优化
- 了解AI系统的部署、监控和运维管理

### 技能目标
- 能够设计简单的AI系统架构方案
- 掌握AI模型的部署和服务化方法
- 学会分析和优化AI系统的性能瓶颈
- 能够评估AI系统的质量和可靠性

### 思维目标
- 培养系统性的架构设计思维
- 发展模块化和分层设计的思维模式
- 建立性能优化和资源管理的意识
- 培养工程化和产品化的思维

### 价值观目标
- 认识系统架构对AI应用成功的重要性
- 培养工程严谨性和质量意识
- 增强团队协作和系统思维
- 建立可持续发展的技术理念

## 🎯 教学流程

### 第一课时（45分钟）

#### 导入环节（10分钟）
**大规模AI系统案例**：
- 展示ChatGPT、Google搜索等大规模AI系统
- 分析这些系统的架构特点和技术挑战
- 讨论AI系统与传统软件系统的区别

**核心问题**：
- "如何设计能够处理海量数据和请求的AI系统？"
- "AI系统的架构设计需要考虑哪些特殊因素？"
- "如何保证AI系统的可靠性和可扩展性？"

#### 新课讲授（25分钟）

##### 1. AI系统架构基础（15分钟）
**分层架构设计**：
```python
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from abc import ABC, abstractmethod
import time
import threading
from queue import Queue
import json

class AISystemArchitecture:
    """AI系统架构设计框架"""
    
    def __init__(self):
        self.layers = {
            'presentation': 'API网关和用户界面',
            'application': '业务逻辑和AI服务',
            'model': 'AI模型推理引擎',
            'data': '数据存储和管理',
            'infrastructure': '基础设施和资源管理'
        }
        
        self.components = {
            'load_balancer': '负载均衡器',
            'api_gateway': 'API网关',
            'model_server': '模型服务器',
            'cache': '缓存系统',
            'database': '数据库',
            'message_queue': '消息队列',
            'monitoring': '监控系统'
        }
    
    def visualize_architecture(self):
        """可视化系统架构"""
        fig, ax = plt.subplots(1, 1, figsize=(14, 10))
        
        # 定义层级位置
        layer_positions = {
            'presentation': (0.1, 0.8, 0.8, 0.15),
            'application': (0.1, 0.6, 0.8, 0.15),
            'model': (0.1, 0.4, 0.8, 0.15),
            'data': (0.1, 0.2, 0.8, 0.15),
            'infrastructure': (0.1, 0.0, 0.8, 0.15)
        }
        
        # 绘制层级
        colors = ['lightblue', 'lightgreen', 'lightyellow', 'lightcoral', 'lightgray']
        for i, (layer, (x, y, w, h)) in enumerate(layer_positions.items()):
            rect = patches.Rectangle((x, y), w, h, linewidth=2, 
                                   edgecolor='black', facecolor=colors[i], alpha=0.7)
            ax.add_patch(rect)
            ax.text(x + w/2, y + h/2, f'{layer.upper()}\n{self.layers[layer]}', 
                   ha='center', va='center', fontsize=10, weight='bold')
        
        # 绘制组件
        component_positions = [
            (0.15, 0.85, 'API网关'),
            (0.4, 0.85, '负载均衡'),
            (0.65, 0.85, '用户界面'),
            (0.15, 0.65, '业务逻辑'),
            (0.4, 0.65, 'AI服务'),
            (0.65, 0.65, '缓存'),
            (0.15, 0.45, '推理引擎'),
            (0.4, 0.45, '模型管理'),
            (0.65, 0.45, '批处理'),
            (0.15, 0.25, '数据库'),
            (0.4, 0.25, '文件存储'),
            (0.65, 0.25, '消息队列'),
            (0.15, 0.05, '容器编排'),
            (0.4, 0.05, '资源监控'),
            (0.65, 0.05, '日志系统')
        ]
        
        for x, y, name in component_positions:
            circle = patches.Circle((x, y), 0.06, linewidth=1, 
                                  edgecolor='darkblue', facecolor='white')
            ax.add_patch(circle)
            ax.text(x, y, name, ha='center', va='center', fontsize=8)
        
        # 绘制连接线
        connections = [
            ((0.5, 0.8), (0.5, 0.75)),  # presentation -> application
            ((0.5, 0.6), (0.5, 0.55)),  # application -> model
            ((0.5, 0.4), (0.5, 0.35)),  # model -> data
            ((0.5, 0.2), (0.5, 0.15))   # data -> infrastructure
        ]
        
        for (x1, y1), (x2, y2) in connections:
            ax.arrow(x1, y1, x2-x1, y2-y1, head_width=0.02, head_length=0.01, 
                    fc='red', ec='red', alpha=0.7)
        
        ax.set_xlim(0, 1)
        ax.set_ylim(0, 1)
        ax.set_title('AI系统分层架构设计', fontsize=16, weight='bold')
        ax.axis('off')
        plt.tight_layout()
        plt.show()

# 可视化AI系统架构
arch = AISystemArchitecture()
arch.visualize_architecture()
```

**微服务架构模式**：
```python
class MicroserviceComponent(ABC):
    """微服务组件基类"""
    
    def __init__(self, name, port):
        self.name = name
        self.port = port
        self.status = 'stopped'
        self.metrics = {
            'requests': 0,
            'errors': 0,
            'latency': []
        }
    
    @abstractmethod
    def process_request(self, request):
        """处理请求的抽象方法"""
        pass
    
    def start(self):
        """启动服务"""
        self.status = 'running'
        print(f"{self.name} 服务已启动，端口: {self.port}")
    
    def stop(self):
        """停止服务"""
        self.status = 'stopped'
        print(f"{self.name} 服务已停止")
    
    def health_check(self):
        """健康检查"""
        return self.status == 'running'
    
    def get_metrics(self):
        """获取服务指标"""
        avg_latency = np.mean(self.metrics['latency']) if self.metrics['latency'] else 0
        return {
            'name': self.name,
            'requests': self.metrics['requests'],
            'errors': self.metrics['errors'],
            'avg_latency': avg_latency,
            'error_rate': self.metrics['errors'] / max(1, self.metrics['requests'])
        }

class APIGateway(MicroserviceComponent):
    """API网关服务"""
    
    def __init__(self):
        super().__init__('API-Gateway', 8080)
        self.routes = {}
        self.rate_limits = {}
    
    def register_service(self, path, service):
        """注册服务路由"""
        self.routes[path] = service
        print(f"注册路由: {path} -> {service.name}")
    
    def process_request(self, request):
        """处理API请求"""
        start_time = time.time()
        self.metrics['requests'] += 1
        
        try:
            # 路由请求
            path = request.get('path', '/')
            if path in self.routes:
                service = self.routes[path]
                response = service.process_request(request)
                
                # 记录延迟
                latency = time.time() - start_time
                self.metrics['latency'].append(latency)
                
                return {
                    'status': 'success',
                    'data': response,
                    'latency': latency
                }
            else:
                raise ValueError(f"路由 {path} 未找到")
                
        except Exception as e:
            self.metrics['errors'] += 1
            return {
                'status': 'error',
                'message': str(e),
                'latency': time.time() - start_time
            }

class ModelInferenceService(MicroserviceComponent):
    """模型推理服务"""
    
    def __init__(self, model_name):
        super().__init__(f'Model-{model_name}', 8081)
        self.model_name = model_name
        self.model_loaded = False
        self.batch_size = 32
        self.request_queue = Queue()
    
    def load_model(self):
        """加载模型"""
        print(f"正在加载模型: {self.model_name}")
        time.sleep(1)  # 模拟加载时间
        self.model_loaded = True
        print(f"模型 {self.model_name} 加载完成")
    
    def process_request(self, request):
        """处理推理请求"""
        start_time = time.time()
        self.metrics['requests'] += 1
        
        try:
            if not self.model_loaded:
                self.load_model()
            
            # 模拟推理过程
            input_data = request.get('data', [])
            
            # 批处理优化
            if len(input_data) > self.batch_size:
                results = []
                for i in range(0, len(input_data), self.batch_size):
                    batch = input_data[i:i+self.batch_size]
                    batch_result = self._inference(batch)
                    results.extend(batch_result)
            else:
                results = self._inference(input_data)
            
            latency = time.time() - start_time
            self.metrics['latency'].append(latency)
            
            return {
                'model': self.model_name,
                'predictions': results,
                'inference_time': latency
            }
            
        except Exception as e:
            self.metrics['errors'] += 1
            raise e
    
    def _inference(self, data):
        """执行推理"""
        # 模拟推理计算
        time.sleep(0.1 * len(data) / 10)  # 模拟计算时间
        return [np.random.random() for _ in data]

class DataService(MicroserviceComponent):
    """数据服务"""
    
    def __init__(self):
        super().__init__('Data-Service', 8082)
        self.cache = {}
        self.database = {}
    
    def process_request(self, request):
        """处理数据请求"""
        start_time = time.time()
        self.metrics['requests'] += 1
        
        try:
            operation = request.get('operation', 'read')
            key = request.get('key', '')
            
            if operation == 'read':
                # 先查缓存
                if key in self.cache:
                    result = self.cache[key]
                    cache_hit = True
                else:
                    # 查数据库
                    result = self.database.get(key, None)
                    if result:
                        self.cache[key] = result  # 更新缓存
                    cache_hit = False
                
                latency = time.time() - start_time
                self.metrics['latency'].append(latency)
                
                return {
                    'data': result,
                    'cache_hit': cache_hit,
                    'latency': latency
                }
                
            elif operation == 'write':
                value = request.get('value', '')
                self.database[key] = value
                self.cache[key] = value  # 更新缓存
                
                return {'status': 'written', 'key': key}
                
        except Exception as e:
            self.metrics['errors'] += 1
            raise e

class AISystemOrchestrator:
    """AI系统编排器"""
    
    def __init__(self):
        self.services = {}
        self.gateway = APIGateway()
        self.monitoring_data = []
    
    def register_service(self, service, path=None):
        """注册服务"""
        self.services[service.name] = service
        if path:
            self.gateway.register_service(path, service)
    
    def start_all_services(self):
        """启动所有服务"""
        self.gateway.start()
        for service in self.services.values():
            service.start()
    
    def stop_all_services(self):
        """停止所有服务"""
        for service in self.services.values():
            service.stop()
        self.gateway.stop()
    
    def health_check_all(self):
        """检查所有服务健康状态"""
        health_status = {}
        health_status['gateway'] = self.gateway.health_check()
        
        for name, service in self.services.items():
            health_status[name] = service.health_check()
        
        return health_status
    
    def collect_metrics(self):
        """收集系统指标"""
        metrics = {}
        metrics['gateway'] = self.gateway.get_metrics()
        
        for name, service in self.services.items():
            metrics[name] = service.get_metrics()
        
        self.monitoring_data.append({
            'timestamp': time.time(),
            'metrics': metrics
        })
        
        return metrics
    
    def simulate_traffic(self, num_requests=100):
        """模拟系统流量"""
        print(f"开始模拟 {num_requests} 个请求...")
        
        for i in range(num_requests):
            # 模拟不同类型的请求
            if i % 3 == 0:
                # 推理请求
                request = {
                    'path': '/inference',
                    'data': np.random.randn(10).tolist()
                }
            elif i % 3 == 1:
                # 数据读取请求
                request = {
                    'path': '/data',
                    'operation': 'read',
                    'key': f'data_{i % 20}'
                }
            else:
                # 数据写入请求
                request = {
                    'path': '/data',
                    'operation': 'write',
                    'key': f'data_{i}',
                    'value': f'value_{i}'
                }
            
            response = self.gateway.process_request(request)
            
            if i % 20 == 0:
                print(f"处理了 {i+1} 个请求")
        
        print("流量模拟完成")
    
    def visualize_metrics(self):
        """可视化系统指标"""
        if not self.monitoring_data:
            print("没有监控数据")
            return
        
        # 收集最新指标
        latest_metrics = self.collect_metrics()
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # 请求数统计
        services = list(latest_metrics.keys())
        requests = [latest_metrics[s]['requests'] for s in services]
        
        axes[0, 0].bar(services, requests, color='skyblue')
        axes[0, 0].set_title('各服务请求数')
        axes[0, 0].set_ylabel('请求数')
        axes[0, 0].tick_params(axis='x', rotation=45)
        
        # 错误率统计
        error_rates = [latest_metrics[s]['error_rate'] * 100 for s in services]
        
        axes[0, 1].bar(services, error_rates, color='lightcoral')
        axes[0, 1].set_title('各服务错误率')
        axes[0, 1].set_ylabel('错误率 (%)')
        axes[0, 1].tick_params(axis='x', rotation=45)
        
        # 平均延迟
        latencies = [latest_metrics[s]['avg_latency'] * 1000 for s in services]  # 转换为毫秒
        
        axes[1, 0].bar(services, latencies, color='lightgreen')
        axes[1, 0].set_title('各服务平均延迟')
        axes[1, 0].set_ylabel('延迟 (ms)')
        axes[1, 0].tick_params(axis='x', rotation=45)
        
        # 系统健康状态
        health_status = self.health_check_all()
        healthy_services = sum(health_status.values())
        total_services = len(health_status)
        
        axes[1, 1].pie([healthy_services, total_services - healthy_services], 
                      labels=['健康', '异常'], 
                      colors=['lightgreen', 'lightcoral'],
                      autopct='%1.1f%%')
        axes[1, 1].set_title('系统健康状态')
        
        plt.tight_layout()
        plt.show()

# 演示微服务架构
def demonstrate_microservices():
    """演示微服务架构"""
    # 创建系统编排器
    orchestrator = AISystemOrchestrator()
    
    # 创建服务
    model_service = ModelInferenceService('bert-base')
    data_service = DataService()
    
    # 注册服务
    orchestrator.register_service(model_service, '/inference')
    orchestrator.register_service(data_service, '/data')
    
    # 启动所有服务
    orchestrator.start_all_services()
    
    # 模拟流量
    orchestrator.simulate_traffic(200)
    
    # 可视化指标
    orchestrator.visualize_metrics()
    
    # 停止服务
    orchestrator.stop_all_services()
    
    return orchestrator

# 运行微服务演示
orchestrator = demonstrate_microservices()
```

##### 2. 性能优化策略（10分钟）
**缓存和批处理优化**：
```python
class PerformanceOptimizer:
    """性能优化器"""
    
    def __init__(self):
        self.cache_strategies = ['LRU', 'LFU', 'TTL']
        self.batch_strategies = ['固定批次', '动态批次', '时间窗口']
    
    def demonstrate_caching(self):
        """演示缓存策略"""
        # 模拟不同缓存策略的性能
        cache_sizes = [10, 50, 100, 200]
        hit_rates = {
            'LRU': [0.3, 0.6, 0.75, 0.85],
            'LFU': [0.25, 0.55, 0.7, 0.8],
            'TTL': [0.2, 0.5, 0.65, 0.75]
        }
        
        plt.figure(figsize=(12, 8))
        
        # 缓存命中率比较
        plt.subplot(2, 2, 1)
        for strategy, rates in hit_rates.items():
            plt.plot(cache_sizes, rates, 'o-', label=strategy, linewidth=2, markersize=6)
        
        plt.title('缓存策略性能比较')
        plt.xlabel('缓存大小')
        plt.ylabel('命中率')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # 响应时间对比
        plt.subplot(2, 2, 2)
        no_cache_time = [100] * len(cache_sizes)
        with_cache_times = {
            'LRU': [100 * (1 - rate) + 5 * rate for rate in hit_rates['LRU']],
            'LFU': [100 * (1 - rate) + 5 * rate for rate in hit_rates['LFU']],
            'TTL': [100 * (1 - rate) + 5 * rate for rate in hit_rates['TTL']]
        }
        
        plt.plot(cache_sizes, no_cache_time, 'r--', label='无缓存', linewidth=2)
        for strategy, times in with_cache_times.items():
            plt.plot(cache_sizes, times, 'o-', label=f'缓存-{strategy}', linewidth=2)
        
        plt.title('响应时间对比')
        plt.xlabel('缓存大小')
        plt.ylabel('平均响应时间 (ms)')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # 批处理效果
        plt.subplot(2, 2, 3)
        batch_sizes = [1, 4, 8, 16, 32, 64]
        throughput = [10, 35, 60, 100, 150, 180]  # 每秒处理请求数
        latency = [10, 15, 25, 40, 70, 120]  # 平均延迟(ms)
        
        ax1 = plt.gca()
        ax2 = ax1.twinx()
        
        line1 = ax1.plot(batch_sizes, throughput, 'b-o', label='吞吐量', linewidth=2)
        line2 = ax2.plot(batch_sizes, latency, 'r-s', label='延迟', linewidth=2)
        
        ax1.set_xlabel('批次大小')
        ax1.set_ylabel('吞吐量 (请求/秒)', color='b')
        ax2.set_ylabel('延迟 (ms)', color='r')
        ax1.set_title('批处理性能权衡')
        
        # 合并图例
        lines = line1 + line2
        labels = [l.get_label() for l in lines]
        ax1.legend(lines, labels, loc='center right')
        ax1.grid(True, alpha=0.3)
        
        # 资源利用率
        plt.subplot(2, 2, 4)
        optimization_levels = ['基础', '缓存', '批处理', '缓存+批处理']
        cpu_usage = [80, 60, 45, 35]
        memory_usage = [70, 85, 60, 75]
        
        x = np.arange(len(optimization_levels))
        width = 0.35
        
        plt.bar(x - width/2, cpu_usage, width, label='CPU使用率', color='lightblue')
        plt.bar(x + width/2, memory_usage, width, label='内存使用率', color='lightcoral')
        
        plt.title('优化策略对资源使用的影响')
        plt.xlabel('优化策略')
        plt.ylabel('使用率 (%)')
        plt.xticks(x, optimization_levels, rotation=45)
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.show()

# 演示性能优化
optimizer = PerformanceOptimizer()
optimizer.demonstrate_caching()
```

#### 实践体验（10分钟）
**架构设计练习**：
学生分组设计一个简单AI应用的系统架构，考虑各个组件的职责和交互

### 第二课时（45分钟）

#### 深入分析（20分钟）

##### 1. 分布式AI系统设计（12分钟）
**分布式训练架构**：
```python
import threading
import time
from concurrent.futures import ThreadPoolExecutor
import matplotlib.pyplot as plt
import numpy as np

class DistributedTrainingSystem:
    """分布式训练系统"""

    def __init__(self, num_workers=4, num_parameter_servers=2):
        self.num_workers = num_workers
        self.num_parameter_servers = num_parameter_servers
        self.workers = []
        self.parameter_servers = []
        self.global_model = {'weights': np.random.randn(100)}
        self.training_metrics = {
            'loss': [],
            'accuracy': [],
            'communication_overhead': [],
            'training_time': []
        }
        self.lock = threading.Lock()

    def create_workers(self):
        """创建工作节点"""
        for i in range(self.num_workers):
            worker = {
                'id': i,
                'local_model': {'weights': self.global_model['weights'].copy()},
                'data_shard': self._generate_data_shard(i),
                'gradients': None,
                'status': 'idle'
            }
            self.workers.append(worker)

        print(f"创建了 {self.num_workers} 个工作节点")

    def create_parameter_servers(self):
        """创建参数服务器"""
        for i in range(self.num_parameter_servers):
            ps = {
                'id': i,
                'model_partition': self._partition_model(i),
                'gradient_buffer': [],
                'update_count': 0
            }
            self.parameter_servers.append(ps)

        print(f"创建了 {self.num_parameter_servers} 个参数服务器")

    def _generate_data_shard(self, worker_id):
        """为工作节点生成数据分片"""
        # 模拟数据分片
        np.random.seed(worker_id)
        return {
            'X': np.random.randn(1000, 50),
            'y': np.random.randint(0, 2, 1000)
        }

    def _partition_model(self, ps_id):
        """模型分区"""
        # 简单的模型分区策略
        total_params = len(self.global_model['weights'])
        partition_size = total_params // self.num_parameter_servers
        start_idx = ps_id * partition_size
        end_idx = start_idx + partition_size if ps_id < self.num_parameter_servers - 1 else total_params

        return {'start': start_idx, 'end': end_idx}

    def worker_training_step(self, worker):
        """工作节点训练步骤"""
        worker['status'] = 'training'

        # 模拟本地训练
        time.sleep(0.1)  # 模拟计算时间

        # 计算梯度（简化）
        gradients = np.random.randn(*worker['local_model']['weights'].shape) * 0.01
        worker['gradients'] = gradients

        # 模拟损失计算
        loss = np.random.exponential(0.5)
        accuracy = 0.5 + 0.4 * np.random.random()

        worker['status'] = 'gradient_ready'

        return loss, accuracy

    def aggregate_gradients(self):
        """聚合梯度"""
        # 收集所有工作节点的梯度
        all_gradients = []
        for worker in self.workers:
            if worker['gradients'] is not None:
                all_gradients.append(worker['gradients'])

        if all_gradients:
            # 平均梯度
            avg_gradients = np.mean(all_gradients, axis=0)

            # 更新全局模型
            learning_rate = 0.01
            self.global_model['weights'] -= learning_rate * avg_gradients

            # 清空梯度
            for worker in self.workers:
                worker['gradients'] = None
                worker['status'] = 'idle'

            return True

        return False

    def synchronous_training(self, num_epochs=10):
        """同步训练"""
        print("开始同步分布式训练...")

        for epoch in range(num_epochs):
            epoch_start_time = time.time()

            # 并行训练所有工作节点
            with ThreadPoolExecutor(max_workers=self.num_workers) as executor:
                futures = []
                for worker in self.workers:
                    # 同步全局模型到本地
                    worker['local_model']['weights'] = self.global_model['weights'].copy()
                    future = executor.submit(self.worker_training_step, worker)
                    futures.append(future)

                # 等待所有工作节点完成
                results = [future.result() for future in futures]

            # 聚合梯度
            self.aggregate_gradients()

            # 记录指标
            avg_loss = np.mean([r[0] for r in results])
            avg_accuracy = np.mean([r[1] for r in results])
            training_time = time.time() - epoch_start_time

            self.training_metrics['loss'].append(avg_loss)
            self.training_metrics['accuracy'].append(avg_accuracy)
            self.training_metrics['training_time'].append(training_time)

            print(f"Epoch {epoch+1}: Loss = {avg_loss:.4f}, Accuracy = {avg_accuracy:.4f}, Time = {training_time:.2f}s")

    def asynchronous_training(self, num_iterations=50):
        """异步训练"""
        print("开始异步分布式训练...")

        def async_worker_loop(worker):
            """异步工作节点循环"""
            for _ in range(num_iterations // self.num_workers):
                # 获取最新全局模型
                with self.lock:
                    worker['local_model']['weights'] = self.global_model['weights'].copy()

                # 本地训练
                loss, accuracy = self.worker_training_step(worker)

                # 异步更新全局模型
                with self.lock:
                    learning_rate = 0.01
                    self.global_model['weights'] -= learning_rate * worker['gradients']

                time.sleep(0.05)  # 模拟网络延迟

        # 启动所有工作节点
        threads = []
        for worker in self.workers:
            thread = threading.Thread(target=async_worker_loop, args=(worker,))
            threads.append(thread)
            thread.start()

        # 等待所有线程完成
        for thread in threads:
            thread.join()

        print("异步训练完成")

    def visualize_training_metrics(self):
        """可视化训练指标"""
        if not self.training_metrics['loss']:
            print("没有训练指标数据")
            return

        fig, axes = plt.subplots(2, 2, figsize=(15, 10))

        epochs = range(1, len(self.training_metrics['loss']) + 1)

        # 损失曲线
        axes[0, 0].plot(epochs, self.training_metrics['loss'], 'b-o', linewidth=2)
        axes[0, 0].set_title('训练损失变化')
        axes[0, 0].set_xlabel('Epoch')
        axes[0, 0].set_ylabel('Loss')
        axes[0, 0].grid(True, alpha=0.3)

        # 准确率曲线
        axes[0, 1].plot(epochs, self.training_metrics['accuracy'], 'g-o', linewidth=2)
        axes[0, 1].set_title('训练准确率变化')
        axes[0, 1].set_xlabel('Epoch')
        axes[0, 1].set_ylabel('Accuracy')
        axes[0, 1].grid(True, alpha=0.3)

        # 训练时间
        axes[1, 0].bar(epochs, self.training_metrics['training_time'], color='orange', alpha=0.7)
        axes[1, 0].set_title('每轮训练时间')
        axes[1, 0].set_xlabel('Epoch')
        axes[1, 0].set_ylabel('Time (s)')
        axes[1, 0].grid(True, alpha=0.3)

        # 系统效率分析
        total_time = sum(self.training_metrics['training_time'])
        avg_time_per_epoch = total_time / len(epochs)
        theoretical_single_time = avg_time_per_epoch * self.num_workers
        speedup = theoretical_single_time / avg_time_per_epoch
        efficiency = speedup / self.num_workers

        metrics = ['加速比', '效率', '通信开销']
        values = [speedup, efficiency * 100, 20]  # 假设通信开销为20%

        axes[1, 1].bar(metrics, values, color=['lightblue', 'lightgreen', 'lightcoral'])
        axes[1, 1].set_title('分布式训练性能指标')
        axes[1, 1].set_ylabel('值')

        # 添加数值标注
        for i, v in enumerate(values):
            axes[1, 1].text(i, v + 0.1, f'{v:.2f}', ha='center', va='bottom')

        plt.tight_layout()
        plt.show()

    def compare_training_strategies(self):
        """比较不同训练策略"""
        strategies = ['单机训练', '同步分布式', '异步分布式']
        training_times = [100, 30, 25]  # 相对训练时间
        convergence_quality = [100, 95, 85]  # 收敛质量
        resource_utilization = [25, 90, 95]  # 资源利用率

        x = np.arange(len(strategies))
        width = 0.25

        fig, ax = plt.subplots(figsize=(12, 8))

        bars1 = ax.bar(x - width, training_times, width, label='训练时间', color='lightblue')
        bars2 = ax.bar(x, convergence_quality, width, label='收敛质量', color='lightgreen')
        bars3 = ax.bar(x + width, resource_utilization, width, label='资源利用率', color='lightcoral')

        ax.set_xlabel('训练策略')
        ax.set_ylabel('相对性能 (%)')
        ax.set_title('分布式训练策略比较')
        ax.set_xticks(x)
        ax.set_xticklabels(strategies)
        ax.legend()
        ax.grid(True, alpha=0.3)

        # 添加数值标注
        for bars in [bars1, bars2, bars3]:
            for bar in bars:
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height + 1,
                       f'{height}%', ha='center', va='bottom')

        plt.tight_layout()
        plt.show()

# 演示分布式训练系统
def demonstrate_distributed_training():
    """演示分布式训练"""
    # 创建分布式训练系统
    dist_system = DistributedTrainingSystem(num_workers=4, num_parameter_servers=2)

    # 初始化系统
    dist_system.create_workers()
    dist_system.create_parameter_servers()

    # 同步训练
    dist_system.synchronous_training(num_epochs=15)

    # 可视化结果
    dist_system.visualize_training_metrics()
    dist_system.compare_training_strategies()

    return dist_system

# 运行分布式训练演示
dist_system = demonstrate_distributed_training()
```

##### 2. 容器化和编排（8分钟）
**Kubernetes部署模拟**：
```python
class KubernetesSimulator:
    """Kubernetes部署模拟器"""

    def __init__(self):
        self.nodes = []
        self.pods = []
        self.services = []
        self.deployments = []
        self.resource_usage = {
            'cpu': [],
            'memory': [],
            'network': []
        }

    def create_cluster(self, num_nodes=3):
        """创建集群"""
        for i in range(num_nodes):
            node = {
                'name': f'node-{i}',
                'cpu_capacity': 4.0,  # 4核
                'memory_capacity': 8.0,  # 8GB
                'cpu_used': 0.0,
                'memory_used': 0.0,
                'pods': [],
                'status': 'Ready'
            }
            self.nodes.append(node)

        print(f"创建了 {num_nodes} 个节点的集群")

    def create_deployment(self, name, image, replicas=3, cpu_request=0.5, memory_request=1.0):
        """创建部署"""
        deployment = {
            'name': name,
            'image': image,
            'replicas': replicas,
            'cpu_request': cpu_request,
            'memory_request': memory_request,
            'pods': []
        }

        # 调度Pod到节点
        for i in range(replicas):
            pod = self.schedule_pod(f'{name}-{i}', cpu_request, memory_request)
            if pod:
                deployment['pods'].append(pod)

        self.deployments.append(deployment)
        print(f"创建部署 {name}，副本数: {replicas}")

        return deployment

    def schedule_pod(self, pod_name, cpu_request, memory_request):
        """调度Pod到节点"""
        # 简单的调度算法：选择资源最充足的节点
        best_node = None
        best_score = -1

        for node in self.nodes:
            if node['status'] != 'Ready':
                continue

            cpu_available = node['cpu_capacity'] - node['cpu_used']
            memory_available = node['memory_capacity'] - node['memory_used']

            if cpu_available >= cpu_request and memory_available >= memory_request:
                # 计算节点得分（剩余资源比例）
                score = (cpu_available / node['cpu_capacity'] +
                        memory_available / node['memory_capacity']) / 2

                if score > best_score:
                    best_score = score
                    best_node = node

        if best_node:
            pod = {
                'name': pod_name,
                'node': best_node['name'],
                'cpu_request': cpu_request,
                'memory_request': memory_request,
                'status': 'Running'
            }

            # 更新节点资源使用
            best_node['cpu_used'] += cpu_request
            best_node['memory_used'] += memory_request
            best_node['pods'].append(pod)
            self.pods.append(pod)

            return pod
        else:
            print(f"无法调度Pod {pod_name}：资源不足")
            return None

    def create_service(self, name, deployment_name, port=80):
        """创建服务"""
        # 找到对应的部署
        deployment = None
        for dep in self.deployments:
            if dep['name'] == deployment_name:
                deployment = dep
                break

        if deployment:
            service = {
                'name': name,
                'deployment': deployment_name,
                'port': port,
                'endpoints': [pod['name'] for pod in deployment['pods']],
                'load_balancer_ip': f'10.0.0.{len(self.services) + 100}'
            }
            self.services.append(service)
            print(f"创建服务 {name}，负载均衡IP: {service['load_balancer_ip']}")
            return service
        else:
            print(f"未找到部署 {deployment_name}")
            return None

    def simulate_auto_scaling(self, deployment_name, target_cpu_percent=70):
        """模拟自动扩缩容"""
        deployment = None
        for dep in self.deployments:
            if dep['name'] == deployment_name:
                deployment = dep
                break

        if not deployment:
            print(f"未找到部署 {deployment_name}")
            return

        # 模拟CPU使用率
        current_cpu_usage = np.random.uniform(50, 90)
        print(f"当前CPU使用率: {current_cpu_usage:.1f}%")

        if current_cpu_usage > target_cpu_percent:
            # 扩容
            new_replicas = min(deployment['replicas'] + 1, 10)
            if new_replicas > deployment['replicas']:
                pod = self.schedule_pod(
                    f"{deployment['name']}-{new_replicas-1}",
                    deployment['cpu_request'],
                    deployment['memory_request']
                )
                if pod:
                    deployment['pods'].append(pod)
                    deployment['replicas'] = new_replicas
                    print(f"扩容到 {new_replicas} 个副本")

        elif current_cpu_usage < target_cpu_percent * 0.5:
            # 缩容
            if deployment['replicas'] > 1:
                # 删除一个Pod
                pod_to_remove = deployment['pods'].pop()

                # 释放节点资源
                for node in self.nodes:
                    if node['name'] == pod_to_remove['node']:
                        node['cpu_used'] -= pod_to_remove['cpu_request']
                        node['memory_used'] -= pod_to_remove['memory_request']
                        node['pods'].remove(pod_to_remove)
                        break

                self.pods.remove(pod_to_remove)
                deployment['replicas'] -= 1
                print(f"缩容到 {deployment['replicas']} 个副本")

    def collect_cluster_metrics(self):
        """收集集群指标"""
        total_cpu_capacity = sum(node['cpu_capacity'] for node in self.nodes)
        total_memory_capacity = sum(node['memory_capacity'] for node in self.nodes)
        total_cpu_used = sum(node['cpu_used'] for node in self.nodes)
        total_memory_used = sum(node['memory_used'] for node in self.nodes)

        cpu_utilization = (total_cpu_used / total_cpu_capacity) * 100
        memory_utilization = (total_memory_used / total_memory_capacity) * 100

        self.resource_usage['cpu'].append(cpu_utilization)
        self.resource_usage['memory'].append(memory_utilization)
        self.resource_usage['network'].append(np.random.uniform(20, 80))  # 模拟网络使用率

        return {
            'cpu_utilization': cpu_utilization,
            'memory_utilization': memory_utilization,
            'total_pods': len(self.pods),
            'healthy_nodes': sum(1 for node in self.nodes if node['status'] == 'Ready')
        }

    def visualize_cluster_status(self):
        """可视化集群状态"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))

        # 节点资源使用情况
        node_names = [node['name'] for node in self.nodes]
        cpu_usage = [(node['cpu_used'] / node['cpu_capacity']) * 100 for node in self.nodes]
        memory_usage = [(node['memory_used'] / node['memory_capacity']) * 100 for node in self.nodes]

        x = np.arange(len(node_names))
        width = 0.35

        axes[0, 0].bar(x - width/2, cpu_usage, width, label='CPU', color='lightblue')
        axes[0, 0].bar(x + width/2, memory_usage, width, label='Memory', color='lightcoral')
        axes[0, 0].set_title('节点资源使用率')
        axes[0, 0].set_xlabel('节点')
        axes[0, 0].set_ylabel('使用率 (%)')
        axes[0, 0].set_xticks(x)
        axes[0, 0].set_xticklabels(node_names)
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)

        # Pod分布
        pod_counts = [len(node['pods']) for node in self.nodes]
        axes[0, 1].pie(pod_counts, labels=node_names, autopct='%1.1f%%')
        axes[0, 1].set_title('Pod分布')

        # 部署状态
        deployment_names = [dep['name'] for dep in self.deployments]
        replica_counts = [dep['replicas'] for dep in self.deployments]

        axes[1, 0].bar(deployment_names, replica_counts, color='lightgreen')
        axes[1, 0].set_title('部署副本数')
        axes[1, 0].set_xlabel('部署')
        axes[1, 0].set_ylabel('副本数')
        axes[1, 0].tick_params(axis='x', rotation=45)
        axes[1, 0].grid(True, alpha=0.3)

        # 资源使用趋势
        if self.resource_usage['cpu']:
            time_points = range(len(self.resource_usage['cpu']))
            axes[1, 1].plot(time_points, self.resource_usage['cpu'], 'b-o', label='CPU')
            axes[1, 1].plot(time_points, self.resource_usage['memory'], 'r-s', label='Memory')
            axes[1, 1].plot(time_points, self.resource_usage['network'], 'g-^', label='Network')
            axes[1, 1].set_title('集群资源使用趋势')
            axes[1, 1].set_xlabel('时间点')
            axes[1, 1].set_ylabel('使用率 (%)')
            axes[1, 1].legend()
            axes[1, 1].grid(True, alpha=0.3)

        plt.tight_layout()
        plt.show()

# 演示Kubernetes部署
def demonstrate_kubernetes():
    """演示Kubernetes部署"""
    k8s = KubernetesSimulator()

    # 创建集群
    k8s.create_cluster(num_nodes=3)

    # 创建AI应用部署
    k8s.create_deployment('ai-inference', 'ai-model:v1.0', replicas=3, cpu_request=1.0, memory_request=2.0)
    k8s.create_deployment('data-service', 'data-api:v1.0', replicas=2, cpu_request=0.5, memory_request=1.0)
    k8s.create_deployment('web-frontend', 'web-ui:v1.0', replicas=2, cpu_request=0.3, memory_request=0.5)

    # 创建服务
    k8s.create_service('ai-inference-svc', 'ai-inference', port=8080)
    k8s.create_service('data-service-svc', 'data-service', port=8081)
    k8s.create_service('web-frontend-svc', 'web-frontend', port=80)

    # 收集初始指标
    k8s.collect_cluster_metrics()

    # 模拟自动扩缩容
    print("\n模拟负载增加，触发自动扩缩容...")
    k8s.simulate_auto_scaling('ai-inference')
    k8s.collect_cluster_metrics()

    # 可视化集群状态
    k8s.visualize_cluster_status()

    return k8s

# 运行Kubernetes演示
k8s_cluster = demonstrate_kubernetes()
```

#### 系统监控与运维（15分钟)

##### 监控和日志系统
**AI系统监控仪表板**：
```python
class AISystemMonitor:
    """AI系统监控器"""

    def __init__(self):
        self.metrics_history = {
            'model_accuracy': [],
            'inference_latency': [],
            'throughput': [],
            'error_rate': [],
            'resource_usage': [],
            'data_drift': []
        }
        self.alerts = []
        self.thresholds = {
            'accuracy_min': 0.85,
            'latency_max': 100,  # ms
            'error_rate_max': 0.05,
            'cpu_usage_max': 80,  # %
            'memory_usage_max': 85  # %
        }

    def collect_metrics(self, timestamp=None):
        """收集系统指标"""
        if timestamp is None:
            timestamp = time.time()

        # 模拟指标收集
        metrics = {
            'timestamp': timestamp,
            'model_accuracy': 0.9 + 0.05 * np.random.randn(),
            'inference_latency': 50 + 20 * np.random.randn(),
            'throughput': 100 + 30 * np.random.randn(),
            'error_rate': 0.02 + 0.01 * np.random.randn(),
            'cpu_usage': 60 + 15 * np.random.randn(),
            'memory_usage': 70 + 10 * np.random.randn(),
            'data_drift_score': 0.1 + 0.05 * np.random.randn()
        }

        # 确保指标在合理范围内
        metrics['model_accuracy'] = np.clip(metrics['model_accuracy'], 0.7, 1.0)
        metrics['inference_latency'] = np.clip(metrics['inference_latency'], 10, 200)
        metrics['throughput'] = np.clip(metrics['throughput'], 50, 200)
        metrics['error_rate'] = np.clip(metrics['error_rate'], 0, 0.1)
        metrics['cpu_usage'] = np.clip(metrics['cpu_usage'], 20, 100)
        metrics['memory_usage'] = np.clip(metrics['memory_usage'], 30, 100)
        metrics['data_drift_score'] = np.clip(metrics['data_drift_score'], 0, 1)

        # 存储历史数据
        for key, value in metrics.items():
            if key != 'timestamp' and key in self.metrics_history:
                self.metrics_history[key].append(value)

        # 检查告警
        self.check_alerts(metrics)

        return metrics

    def check_alerts(self, metrics):
        """检查告警条件"""
        alerts = []

        if metrics['model_accuracy'] < self.thresholds['accuracy_min']:
            alerts.append({
                'type': 'CRITICAL',
                'message': f"模型准确率过低: {metrics['model_accuracy']:.3f}",
                'timestamp': metrics['timestamp']
            })

        if metrics['inference_latency'] > self.thresholds['latency_max']:
            alerts.append({
                'type': 'WARNING',
                'message': f"推理延迟过高: {metrics['inference_latency']:.1f}ms",
                'timestamp': metrics['timestamp']
            })

        if metrics['error_rate'] > self.thresholds['error_rate_max']:
            alerts.append({
                'type': 'WARNING',
                'message': f"错误率过高: {metrics['error_rate']:.3f}",
                'timestamp': metrics['timestamp']
            })

        if metrics['cpu_usage'] > self.thresholds['cpu_usage_max']:
            alerts.append({
                'type': 'INFO',
                'message': f"CPU使用率过高: {metrics['cpu_usage']:.1f}%",
                'timestamp': metrics['timestamp']
            })

        self.alerts.extend(alerts)

        for alert in alerts:
            print(f"[{alert['type']}] {alert['message']}")

    def simulate_monitoring_period(self, duration_hours=24, interval_minutes=5):
        """模拟监控周期"""
        print(f"开始监控，持续时间: {duration_hours}小时，采集间隔: {interval_minutes}分钟")

        num_samples = int(duration_hours * 60 / interval_minutes)

        for i in range(num_samples):
            timestamp = time.time() + i * interval_minutes * 60
            metrics = self.collect_metrics(timestamp)

            if i % 12 == 0:  # 每小时打印一次
                print(f"时间点 {i//12}h: 准确率={metrics['model_accuracy']:.3f}, "
                      f"延迟={metrics['inference_latency']:.1f}ms, "
                      f"吞吐量={metrics['throughput']:.1f}req/s")

        print(f"监控完成，共收集 {num_samples} 个数据点，产生 {len(self.alerts)} 个告警")

    def create_dashboard(self):
        """创建监控仪表板"""
        if not self.metrics_history['model_accuracy']:
            print("没有监控数据")
            return

        fig, axes = plt.subplots(3, 2, figsize=(16, 12))

        time_points = range(len(self.metrics_history['model_accuracy']))

        # 模型准确率
        axes[0, 0].plot(time_points, self.metrics_history['model_accuracy'], 'b-', linewidth=2)
        axes[0, 0].axhline(y=self.thresholds['accuracy_min'], color='r', linestyle='--', label='最低阈值')
        axes[0, 0].set_title('模型准确率')
        axes[0, 0].set_ylabel('准确率')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)

        # 推理延迟
        axes[0, 1].plot(time_points, self.metrics_history['inference_latency'], 'g-', linewidth=2)
        axes[0, 1].axhline(y=self.thresholds['latency_max'], color='r', linestyle='--', label='最大阈值')
        axes[0, 1].set_title('推理延迟')
        axes[0, 1].set_ylabel('延迟 (ms)')
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3)

        # 吞吐量
        axes[1, 0].plot(time_points, self.metrics_history['throughput'], 'orange', linewidth=2)
        axes[1, 0].set_title('系统吞吐量')
        axes[1, 0].set_ylabel('请求/秒')
        axes[1, 0].grid(True, alpha=0.3)

        # 错误率
        axes[1, 1].plot(time_points, self.metrics_history['error_rate'], 'r-', linewidth=2)
        axes[1, 1].axhline(y=self.thresholds['error_rate_max'], color='r', linestyle='--', label='最大阈值')
        axes[1, 1].set_title('错误率')
        axes[1, 1].set_ylabel('错误率')
        axes[1, 1].legend()
        axes[1, 1].grid(True, alpha=0.3)

        # 资源使用率
        cpu_usage = [self.metrics_history['resource_usage'][i] if i < len(self.metrics_history['resource_usage'])
                    else np.random.uniform(50, 80) for i in range(len(time_points))]
        memory_usage = [cpu_usage[i] + np.random.uniform(-10, 15) for i in range(len(time_points))]

        axes[2, 0].plot(time_points, cpu_usage, 'purple', label='CPU', linewidth=2)
        axes[2, 0].plot(time_points, memory_usage, 'brown', label='Memory', linewidth=2)
        axes[2, 0].axhline(y=self.thresholds['cpu_usage_max'], color='r', linestyle='--', alpha=0.7)
        axes[2, 0].set_title('资源使用率')
        axes[2, 0].set_ylabel('使用率 (%)')
        axes[2, 0].set_xlabel('时间点')
        axes[2, 0].legend()
        axes[2, 0].grid(True, alpha=0.3)

        # 数据漂移
        axes[2, 1].plot(time_points, self.metrics_history['data_drift'], 'cyan', linewidth=2)
        axes[2, 1].axhline(y=0.3, color='orange', linestyle='--', label='注意阈值')
        axes[2, 1].axhline(y=0.5, color='r', linestyle='--', label='严重阈值')
        axes[2, 1].set_title('数据漂移检测')
        axes[2, 1].set_ylabel('漂移分数')
        axes[2, 1].set_xlabel('时间点')
        axes[2, 1].legend()
        axes[2, 1].grid(True, alpha=0.3)

        plt.suptitle('AI系统监控仪表板', fontsize=16, fontweight='bold')
        plt.tight_layout()
        plt.show()

    def generate_alert_report(self):
        """生成告警报告"""
        if not self.alerts:
            print("没有告警记录")
            return

        # 统计告警类型
        alert_types = {}
        for alert in self.alerts:
            alert_type = alert['type']
            alert_types[alert_type] = alert_types.get(alert_type, 0) + 1

        # 可视化告警统计
        fig, axes = plt.subplots(1, 2, figsize=(12, 5))

        # 告警类型分布
        types = list(alert_types.keys())
        counts = list(alert_types.values())
        colors = {'CRITICAL': 'red', 'WARNING': 'orange', 'INFO': 'blue'}
        bar_colors = [colors.get(t, 'gray') for t in types]

        axes[0].bar(types, counts, color=bar_colors)
        axes[0].set_title('告警类型分布')
        axes[0].set_ylabel('告警数量')

        # 告警时间分布
        alert_hours = []
        for alert in self.alerts:
            # 简化：假设告警均匀分布在24小时内
            hour = len(alert_hours) % 24
            alert_hours.append(hour)

        axes[1].hist(alert_hours, bins=24, alpha=0.7, color='lightcoral')
        axes[1].set_title('告警时间分布')
        axes[1].set_xlabel('小时')
        axes[1].set_ylabel('告警数量')
        axes[1].grid(True, alpha=0.3)

        plt.tight_layout()
        plt.show()

        print(f"\n告警报告:")
        print(f"总告警数: {len(self.alerts)}")
        for alert_type, count in alert_types.items():
            print(f"{alert_type}: {count} 个")

# 演示AI系统监控
def demonstrate_monitoring():
    """演示AI系统监控"""
    monitor = AISystemMonitor()

    # 模拟24小时监控
    monitor.simulate_monitoring_period(duration_hours=24, interval_minutes=30)

    # 创建监控仪表板
    monitor.create_dashboard()

    # 生成告警报告
    monitor.generate_alert_report()

    return monitor

# 运行监控演示
monitor = demonstrate_monitoring()
```

#### 总结反思（10分钟）
**核心要点回顾**：
- AI系统架构设计需要考虑可扩展性、可靠性和性能
- 微服务架构和分布式设计是大规模AI系统的关键
- 容器化和编排技术简化了AI系统的部署和管理
- 监控和运维是保证AI系统稳定运行的重要保障

## 📊 评估方式

### 过程性评价
- **架构理解**：对AI系统架构设计原理的掌握程度
- **设计能力**：设计合理AI系统架构的能力
- **技术应用**：运用分布式、容器化等技术的能力
- **系统思维**：从整体角度思考AI系统设计的能力

### 结果性评价
- **架构设计**：完成完整的AI系统架构设计方案
- **性能分析**：分析和优化AI系统性能的能力
- **部署方案**：设计AI系统部署和运维方案
- **监控系统**：设计AI系统监控和告警机制

## 🏠 课后延伸

### 基础任务
1. **架构分析**：分析一个现有AI产品的系统架构
2. **设计练习**：为特定场景设计AI系统架构
3. **性能优化**：分析和优化AI系统的性能瓶颈

### 拓展任务
1. **分布式系统**：深入研究分布式AI系统的设计模式
2. **云原生架构**：设计基于云原生技术的AI系统
3. **边缘计算**：探索AI系统在边缘计算环境中的部署

### 预习任务
了解AI产业生态的基本结构，思考AI技术如何产业化应用。

---

*本课程旨在帮助学生理解AI系统架构设计的核心原理和实践方法，培养系统性思维和工程化能力。*
