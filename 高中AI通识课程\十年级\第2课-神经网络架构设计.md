# 第2课：神经网络架构设计

## 🎯 课程基本信息

- **课程名称**：神经网络架构设计
- **适用年级**：高中十年级
- **课时安排**：90分钟（2课时）
- **课程类型**：技术实践课
- **核心主题**：神经网络架构原理与设计方法

## 📚 教学目标

### 认知目标
- 深入理解神经网络的基本组成和工作原理
- 掌握主流神经网络架构的设计思想和特点
- 认识网络架构设计对模型性能的重要影响
- 了解神经架构搜索（NAS）的基本概念和方法

### 技能目标
- 能够分析和比较不同神经网络架构的优缺点
- 学会使用深度学习框架构建神经网络模型
- 掌握网络架构可视化和分析的方法
- 能够设计针对特定任务的神经网络架构

### 思维目标
- 培养系统性设计思维和工程思维
- 发展抽象思维和模块化思维
- 建立性能优化和效率平衡的意识
- 培养创新设计和问题解决能力

### 价值观目标
- 树立严谨的工程设计态度
- 培养追求卓越的技术精神
- 增强对技术美学的欣赏能力
- 建立开源共享的合作理念

## 🎮 教学重点与难点

### 教学重点
1. 神经网络的基本组成和前向传播过程
2. 卷积神经网络（CNN）的设计原理和应用
3. 循环神经网络（RNN）的结构特点和变体
4. 注意力机制和Transformer架构的设计思想

### 教学难点
1. 反向传播算法的数学原理和计算过程
2. 不同架构在特定任务上的适用性分析
3. 网络深度与性能的关系理解
4. 架构设计中的权衡和优化策略

## 📋 教学准备

### 设备准备
- **主要设备**：计算机教室、GPU服务器
- **开发环境**：Python、PyTorch、TensorFlow
- **可视化工具**：TensorBoard、Netron、Graphviz
- **在线平台**：Google Colab、Jupyter Notebook

### 教学材料
- **理论资源**：
  - 经典神经网络论文合集
  - 网络架构设计指南
  - 深度学习教材章节
  - 架构演进历史资料

- **实践材料**：
  - 预构建的网络模型代码
  - 数据集和训练脚本
  - 架构可视化工具
  - 性能评估基准

- **案例资源**：
  - AlexNet到ResNet的演进历程
  - BERT和GPT的架构对比
  - 移动端优化网络设计
  - 多模态网络架构案例

## 🎯 教学流程

### 第一课时（45分钟）

#### 导入环节（10分钟）

##### 1. 架构之美展示（5分钟）
**活动设计**：
- 展示不同神经网络架构的可视化图像
- 对比简单全连接网络与复杂Transformer的结构差异
- 展示网络架构演进的时间线

**引导思考**：
"为什么不同的任务需要不同的网络架构？架构设计背后的哲学是什么？"

##### 2. 问题驱动引入（5分钟）
**核心问题**：
- "如何设计一个既高效又准确的神经网络？"
- "网络的深度和宽度如何影响性能？"
- "为什么CNN适合图像，RNN适合序列？"

#### 新课讲授（25分钟）

##### 1. 神经网络基础架构（12分钟）
**基本组成单元**：

**神经元模型**：
```python
# 单个神经元的计算
def neuron(inputs, weights, bias):
    """
    inputs: 输入向量 [x1, x2, ..., xn]
    weights: 权重向量 [w1, w2, ..., wn]
    bias: 偏置项 b
    """
    linear_output = sum(w * x for w, x in zip(weights, inputs)) + bias
    activation_output = activation_function(linear_output)
    return activation_output
```

**层的概念**：
```
全连接层（Dense Layer）：
- 每个神经元与前一层所有神经元连接
- 参数量 = (输入维度 + 1) × 输出维度
- 适用于特征提取和分类

卷积层（Convolutional Layer）：
- 局部连接和权重共享
- 参数量 = 卷积核大小 × 输入通道 × 输出通道
- 适用于空间特征提取

循环层（Recurrent Layer）：
- 具有记忆功能的时序建模
- 参数量 = 隐藏状态维度的平方级别
- 适用于序列数据处理
```

**激活函数选择**：
```python
# 常用激活函数
import torch.nn as nn

# ReLU：解决梯度消失，计算简单
relu = nn.ReLU()

# GELU：平滑的ReLU变体，Transformer中常用
gelu = nn.GELU()

# Swish：自门控激活函数
swish = nn.SiLU()  # PyTorch中的Swish实现

# Tanh：输出范围[-1,1]，中心化
tanh = nn.Tanh()
```

##### 2. 经典架构设计思想（13分钟）
**卷积神经网络（CNN）演进**：

**LeNet-5（1998）**：
```
架构特点：
- 简单的卷积-池化-全连接结构
- 参数量约6万个
- 为手写数字识别设计

设计思想：
- 局部感受野
- 权重共享
- 平移不变性
```

**AlexNet（2012）**：
```
架构创新：
- 更深的网络（8层）
- ReLU激活函数
- Dropout正则化
- 数据增强技术

性能突破：
- ImageNet错误率从26%降到15%
- 开启深度学习时代
```

**ResNet（2015）**：
```
核心创新：
- 残差连接（Skip Connection）
- 解决梯度消失问题
- 支持极深网络训练

残差块设计：
F(x) = H(x) - x
其中H(x)是期望的映射，F(x)是残差映射
```

**循环神经网络（RNN）发展**：

**标准RNN**：
```python
# RNN基本计算
h_t = tanh(W_hh * h_{t-1} + W_xh * x_t + b_h)
y_t = W_hy * h_t + b_y

问题：
- 梯度消失/爆炸
- 长期依赖建模困难
```

**LSTM（1997）**：
```
门控机制：
- 遗忘门：决定丢弃哪些信息
- 输入门：决定存储哪些新信息
- 输出门：决定输出哪些信息

优势：
- 有效解决长期依赖问题
- 梯度流动更稳定
```

#### 实践体验（10分钟）

##### 网络架构构建实验
**活动设计**：
使用PyTorch构建不同类型的神经网络，观察架构差异

**实验代码**：
```python
import torch
import torch.nn as nn

# 1. 简单全连接网络
class SimpleNet(nn.Module):
    def __init__(self, input_size, hidden_size, output_size):
        super(SimpleNet, self).__init__()
        self.fc1 = nn.Linear(input_size, hidden_size)
        self.fc2 = nn.Linear(hidden_size, output_size)
        self.relu = nn.ReLU()
    
    def forward(self, x):
        x = self.relu(self.fc1(x))
        x = self.fc2(x)
        return x

# 2. 卷积神经网络
class ConvNet(nn.Module):
    def __init__(self, num_classes):
        super(ConvNet, self).__init__()
        self.conv1 = nn.Conv2d(3, 32, 3, padding=1)
        self.conv2 = nn.Conv2d(32, 64, 3, padding=1)
        self.pool = nn.MaxPool2d(2, 2)
        self.fc = nn.Linear(64 * 8 * 8, num_classes)
        self.relu = nn.ReLU()
    
    def forward(self, x):
        x = self.pool(self.relu(self.conv1(x)))
        x = self.pool(self.relu(self.conv2(x)))
        x = x.view(-1, 64 * 8 * 8)
        x = self.fc(x)
        return x

# 3. 残差网络块
class ResidualBlock(nn.Module):
    def __init__(self, in_channels, out_channels):
        super(ResidualBlock, self).__init__()
        self.conv1 = nn.Conv2d(in_channels, out_channels, 3, padding=1)
        self.conv2 = nn.Conv2d(out_channels, out_channels, 3, padding=1)
        self.relu = nn.ReLU()
        
    def forward(self, x):
        residual = x
        out = self.relu(self.conv1(x))
        out = self.conv2(out)
        out += residual  # 残差连接
        out = self.relu(out)
        return out
```

### 第二课时（45分钟）

#### 深入分析（20分钟）

##### 1. 现代架构设计原则（12分钟）
**设计原则总结**：

**模块化设计**：
```
基本思想：
- 将复杂网络分解为可重复的模块
- 每个模块负责特定的功能
- 便于调试、优化和扩展

典型例子：
- ResNet的残差块
- Transformer的编码器/解码器层
- Inception的多分支模块
```

**注意力机制设计**：
```python
# 自注意力机制实现
class SelfAttention(nn.Module):
    def __init__(self, embed_dim, num_heads):
        super(SelfAttention, self).__init__()
        self.embed_dim = embed_dim
        self.num_heads = num_heads
        self.head_dim = embed_dim // num_heads
        
        self.qkv = nn.Linear(embed_dim, embed_dim * 3)
        self.proj = nn.Linear(embed_dim, embed_dim)
        
    def forward(self, x):
        B, N, C = x.shape
        qkv = self.qkv(x).reshape(B, N, 3, self.num_heads, self.head_dim)
        q, k, v = qkv.permute(2, 0, 3, 1, 4)
        
        # 计算注意力权重
        attn = (q @ k.transpose(-2, -1)) / (self.head_dim ** 0.5)
        attn = attn.softmax(dim=-1)
        
        # 应用注意力权重
        x = (attn @ v).transpose(1, 2).reshape(B, N, C)
        x = self.proj(x)
        return x
```

**效率优化策略**：
```
参数效率：
- 深度可分离卷积
- 组卷积（Group Convolution）
- 知识蒸馏

计算效率：
- 早期退出（Early Exit）
- 动态网络（Dynamic Networks）
- 剪枝和量化
```

##### 2. 神经架构搜索（NAS）（8分钟）
**NAS基本概念**：
```
定义：
自动化搜索最优神经网络架构的技术

搜索空间：
- 宏观搜索：整体网络结构
- 微观搜索：单元内部连接
- 混合搜索：多层次组合

搜索策略：
- 强化学习
- 进化算法
- 可微分搜索
- 贝叶斯优化
```

**代表性NAS方法**：
```
ENAS (Efficient NAS)：
- 权重共享加速搜索
- 强化学习指导架构选择

DARTS (Differentiable NAS)：
- 连续松弛搜索空间
- 梯度下降优化架构

EfficientNet：
- 复合缩放方法
- 平衡深度、宽度和分辨率
```

#### 架构设计实践（15分钟）

##### 1. 设计挑战：图像分类网络（8分钟）
**任务要求**：
为CIFAR-10数据集设计一个高效的图像分类网络

**设计约束**：
- 参数量不超过100万
- 准确率目标90%以上
- 推理速度要求实时

**设计思路**：
```python
class EfficientCIFARNet(nn.Module):
    def __init__(self, num_classes=10):
        super(EfficientCIFARNet, self).__init__()
        
        # 初始特征提取
        self.stem = nn.Sequential(
            nn.Conv2d(3, 32, 3, padding=1),
            nn.BatchNorm2d(32),
            nn.ReLU(inplace=True)
        )
        
        # 高效残差块
        self.layer1 = self._make_layer(32, 64, 2)
        self.layer2 = self._make_layer(64, 128, 2)
        self.layer3 = self._make_layer(128, 256, 2)
        
        # 全局平均池化
        self.avgpool = nn.AdaptiveAvgPool2d((1, 1))
        self.classifier = nn.Linear(256, num_classes)
        
    def _make_layer(self, in_channels, out_channels, num_blocks):
        layers = []
        # 下采样块
        layers.append(nn.Conv2d(in_channels, out_channels, 3, 
                               stride=2, padding=1))
        layers.append(nn.BatchNorm2d(out_channels))
        layers.append(nn.ReLU(inplace=True))
        
        # 残差块
        for _ in range(num_blocks - 1):
            layers.append(ResidualBlock(out_channels, out_channels))
            
        return nn.Sequential(*layers)
```

##### 2. 架构分析和优化（7分钟）
**性能分析工具**：
```python
# 计算模型参数量和FLOPs
def count_parameters(model):
    return sum(p.numel() for p in model.parameters() if p.requires_grad)

def count_flops(model, input_size):
    # 使用thop库计算FLOPs
    from thop import profile
    input_tensor = torch.randn(1, *input_size)
    flops, params = profile(model, inputs=(input_tensor,))
    return flops, params

# 分析网络架构
model = EfficientCIFARNet()
params = count_parameters(model)
flops, _ = count_flops(model, (3, 32, 32))

print(f"参数量: {params:,}")
print(f"FLOPs: {flops:,}")
```

#### 总结反思（10分钟）

##### 知识总结
**核心要点回顾**：
- 神经网络架构设计需要考虑任务特点和资源约束
- 不同架构适用于不同类型的数据和任务
- 现代架构设计注重模块化和效率优化
- 自动化架构搜索是未来发展趋势

##### 设计思考
**反思问题**：
1. 如何在准确率和效率之间找到最佳平衡点？
2. 未来的神经网络架构会朝什么方向发展？
3. 如何设计适合特定应用场景的网络架构？
4. 人工设计和自动搜索各有什么优势？

## 📊 评估方式

### 过程性评价
- **理解深度**：对架构设计原理的理解和应用能力
- **实践能力**：网络构建和调试的技术水平
- **创新思维**：架构设计中的创新想法和改进方案
- **分析能力**：对不同架构优缺点的分析能力

### 结果性评价
- **架构设计**：完成特定任务的网络架构设计
- **性能分析**：模型性能评估和优化建议
- **技术报告**：架构设计思路和实验结果报告
- **代码实现**：可运行的网络模型代码

### 评价标准
- **优秀**：设计合理、实现正确、有创新亮点
- **良好**：基本掌握设计方法，能够完成任务
- **合格**：了解基本概念，在指导下完成设计
- **需努力**：概念理解不清，需要更多练习

## 🏠 课后延伸

### 基础任务
1. **架构复现**：复现一个经典网络架构（如ResNet-18）
2. **性能对比**：比较不同架构在同一任务上的性能
3. **设计报告**：撰写网络架构设计思路和实验报告

### 拓展任务
1. **创新设计**：设计一个针对特定任务的新架构
2. **效率优化**：优化现有架构以提高推理速度
3. **NAS实践**：尝试使用自动化方法搜索网络架构

### 预习任务
了解AI在科学研究中的应用案例，思考如何将AI技术应用于解决科学问题。

## 🔗 教学反思

### 成功要素
- 结合理论讲解和实践操作，增强学习效果
- 通过架构演进历程帮助学生理解设计思想
- 引导学生思考设计背后的原理和权衡
- 培养学生的工程思维和创新能力

### 改进方向
- 增加更多实际应用案例
- 提供更多动手实践机会
- 关注学生的个性化学习需求
- 加强与前沿研究的联系

---

*本课程旨在帮助十年级学生掌握神经网络架构设计的基本原理和方法，培养系统性设计思维和技术创新能力。*
