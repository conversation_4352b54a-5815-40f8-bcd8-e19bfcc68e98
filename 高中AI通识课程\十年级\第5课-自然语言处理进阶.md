# 第5课：自然语言处理进阶

## 🎯 课程基本信息

- **课程名称**：自然语言处理进阶
- **适用年级**：高中十年级
- **课时安排**：90分钟（2课时）
- **课程类型**：技术深化课
- **核心主题**：自然语言处理技术原理与前沿应用

## 📚 教学目标

### 认知目标
- 深入理解自然语言处理的核心技术和发展历程
- 掌握大语言模型的架构原理和训练方法
- 认识多语言处理和跨语言理解技术
- 了解NLP在各领域的应用和发展趋势

### 技能目标
- 能够使用预训练语言模型完成各种NLP任务
- 掌握文本预处理和特征提取技术
- 学会设计和优化语言模型的提示词
- 能够评估和改进NLP系统的性能

### 思维目标
- 培养语言理解和生成的计算思维
- 发展从词汇到语义的抽象思维
- 建立多语言和跨文化的理解能力
- 培养语言AI的伦理思考能力

## 🎯 教学流程

### 第一课时（45分钟）

#### 导入环节（10分钟）
**语言AI突破展示**：
- 演示ChatGPT的对话能力
- 展示机器翻译的质量提升
- 演示代码生成和文本创作

#### 新课讲授（25分钟）

##### 1. NLP技术演进（15分钟）
**发展历程**：
```
第一代：基于规则的方法（1950s-1980s）
- 专家系统和语法规则
- 有限的适用性和扩展性

第二代：统计方法（1990s-2010s）
- N-gram语言模型
- 隐马尔可夫模型
- 支持向量机

第三代：神经网络方法（2010s-2017）
- 词向量（Word2Vec, GloVe）
- 循环神经网络（RNN, LSTM）
- 序列到序列模型

第四代：预训练语言模型（2018-至今）
- BERT, GPT系列
- Transformer架构
- 大规模预训练
```

**核心技术原理**：
```python
# 语言模型的基本概念
class LanguageModel:
    """语言模型的基本框架"""
    
    def __init__(self, vocab_size, embed_dim, hidden_dim):
        self.embedding = nn.Embedding(vocab_size, embed_dim)
        self.transformer = nn.TransformerEncoder(...)
        self.output_layer = nn.Linear(hidden_dim, vocab_size)
    
    def forward(self, input_ids):
        # 词嵌入
        embeddings = self.embedding(input_ids)
        
        # 上下文编码
        hidden_states = self.transformer(embeddings)
        
        # 预测下一个词
        logits = self.output_layer(hidden_states)
        
        return logits
    
    def generate_text(self, prompt, max_length=100):
        """文本生成"""
        generated = prompt
        
        for _ in range(max_length):
            # 预测下一个词
            next_token = self.predict_next_token(generated)
            generated.append(next_token)
            
            if next_token == self.eos_token:
                break
        
        return generated
```

##### 2. 大语言模型深度解析（10分钟）
**BERT vs GPT架构对比**：
```python
# BERT：双向编码器
class BERTModel(nn.Module):
    """BERT模型架构"""
    
    def __init__(self, config):
        super().__init__()
        self.embeddings = BERTEmbeddings(config)
        self.encoder = BERTEncoder(config)
        self.pooler = BERTPooler(config)
    
    def forward(self, input_ids, attention_mask=None):
        # 嵌入层
        embedding_output = self.embeddings(input_ids)
        
        # 编码器（双向注意力）
        encoder_outputs = self.encoder(
            embedding_output, 
            attention_mask=attention_mask
        )
        
        # 池化层
        pooled_output = self.pooler(encoder_outputs)
        
        return encoder_outputs, pooled_output

# GPT：自回归解码器
class GPTModel(nn.Module):
    """GPT模型架构"""
    
    def __init__(self, config):
        super().__init__()
        self.embeddings = GPTEmbeddings(config)
        self.decoder = GPTDecoder(config)
        self.lm_head = nn.Linear(config.hidden_size, config.vocab_size)
    
    def forward(self, input_ids):
        # 嵌入层
        hidden_states = self.embeddings(input_ids)
        
        # 解码器（因果注意力）
        hidden_states = self.decoder(hidden_states)
        
        # 语言模型头
        logits = self.lm_head(hidden_states)
        
        return logits
```

#### 实践体验（10分钟）
**文本生成实验**：
使用预训练模型进行文本生成和理解任务

### 第二课时（45分钟）

#### 深入分析（20分钟）

##### 1. 核心NLP任务（12分钟）
**文本分类**：
```python
# 情感分析示例
class SentimentAnalyzer:
    def __init__(self, model_name="bert-base-uncased"):
        self.tokenizer = AutoTokenizer.from_pretrained(model_name)
        self.model = AutoModelForSequenceClassification.from_pretrained(model_name)
    
    def analyze_sentiment(self, text):
        # 文本编码
        inputs = self.tokenizer(text, return_tensors="pt", 
                               truncation=True, padding=True)
        
        # 模型预测
        with torch.no_grad():
            outputs = self.model(**inputs)
            predictions = torch.nn.functional.softmax(outputs.logits, dim=-1)
        
        # 解析结果
        labels = ["负面", "正面"]
        confidence = predictions.max().item()
        label = labels[predictions.argmax().item()]
        
        return {"label": label, "confidence": confidence}
```

**命名实体识别**：
```python
# NER任务实现
class NamedEntityRecognizer:
    def __init__(self):
        self.tokenizer = AutoTokenizer.from_pretrained("bert-base-ner")
        self.model = AutoModelForTokenClassification.from_pretrained("bert-base-ner")
    
    def extract_entities(self, text):
        # 分词和编码
        tokens = self.tokenizer.tokenize(text)
        inputs = self.tokenizer(text, return_tensors="pt")
        
        # 预测
        with torch.no_grad():
            outputs = self.model(**inputs)
            predictions = torch.argmax(outputs.logits, dim=2)
        
        # 解析实体
        entities = []
        current_entity = None
        
        for i, (token, pred) in enumerate(zip(tokens, predictions[0])):
            label = self.model.config.id2label[pred.item()]
            
            if label.startswith("B-"):  # 实体开始
                if current_entity:
                    entities.append(current_entity)
                current_entity = {"text": token, "label": label[2:], "start": i}
            elif label.startswith("I-") and current_entity:  # 实体继续
                current_entity["text"] += token
            else:  # 非实体
                if current_entity:
                    entities.append(current_entity)
                    current_entity = None
        
        return entities
```

##### 2. 多语言和跨语言技术（8分钟）
**多语言模型**：
```python
# 多语言BERT应用
class MultilingualProcessor:
    def __init__(self):
        self.model_name = "bert-base-multilingual-cased"
        self.tokenizer = AutoTokenizer.from_pretrained(self.model_name)
        self.model = AutoModel.from_pretrained(self.model_name)
    
    def get_sentence_embedding(self, text, language=None):
        """获取句子嵌入"""
        inputs = self.tokenizer(text, return_tensors="pt", 
                               truncation=True, padding=True)
        
        with torch.no_grad():
            outputs = self.model(**inputs)
            # 使用[CLS]标记的嵌入作为句子表示
            sentence_embedding = outputs.last_hidden_state[:, 0, :]
        
        return sentence_embedding
    
    def cross_lingual_similarity(self, text1, text2):
        """跨语言相似度计算"""
        emb1 = self.get_sentence_embedding(text1)
        emb2 = self.get_sentence_embedding(text2)
        
        # 余弦相似度
        similarity = torch.cosine_similarity(emb1, emb2)
        return similarity.item()

# 使用示例
processor = MultilingualProcessor()
similarity = processor.cross_lingual_similarity(
    "Hello, how are you?",  # 英文
    "你好，你好吗？"         # 中文
)
print(f"跨语言相似度: {similarity:.3f}")
```

#### 前沿技术探索（15分钟）

##### 1. 提示工程和上下文学习（8分钟）
**提示设计策略**：
```python
class PromptEngineer:
    """提示工程工具类"""
    
    def __init__(self, model):
        self.model = model
    
    def zero_shot_classification(self, text, labels):
        """零样本分类"""
        prompt = f"""
        请将以下文本分类到给定的类别中：
        
        文本: {text}
        类别: {', '.join(labels)}
        
        分类结果:
        """
        return self.model.generate(prompt)
    
    def few_shot_learning(self, examples, query):
        """少样本学习"""
        prompt = "以下是一些示例：\n\n"
        
        for example in examples:
            prompt += f"输入: {example['input']}\n"
            prompt += f"输出: {example['output']}\n\n"
        
        prompt += f"输入: {query}\n输出:"
        
        return self.model.generate(prompt)
    
    def chain_of_thought(self, problem):
        """思维链推理"""
        prompt = f"""
        请逐步解决以下问题：
        
        问题: {problem}
        
        让我们一步步思考：
        """
        return self.model.generate(prompt)
```

##### 2. 代码生成和程序理解（7分钟）
**代码生成模型**：
```python
class CodeGenerator:
    """代码生成助手"""
    
    def __init__(self):
        self.model = "code-davinci-002"  # 或其他代码模型
    
    def generate_function(self, description, language="python"):
        """根据描述生成函数"""
        prompt = f"""
        # 语言: {language}
        # 功能描述: {description}
        # 请生成完整的函数实现:
        
        def 
        """
        return self.model.generate(prompt)
    
    def explain_code(self, code):
        """解释代码功能"""
        prompt = f"""
        请解释以下代码的功能：
        
        ```python
        {code}
        ```
        
        解释:
        """
        return self.model.generate(prompt)
    
    def debug_code(self, code, error_message):
        """代码调试"""
        prompt = f"""
        以下代码出现了错误：
        
        代码:
        ```python
        {code}
        ```
        
        错误信息: {error_message}
        
        请分析错误原因并提供修复建议:
        """
        return self.model.generate(prompt)
```

#### 总结反思（10分钟）
**核心要点回顾**：
- NLP从规则方法发展到大语言模型的技术演进
- 预训练-微调范式的重要性和有效性
- 多语言和跨语言理解的技术突破
- 提示工程和上下文学习的新范式

## 📊 评估方式

### 过程性评价
- **技术理解**：对NLP技术原理的掌握程度
- **实践能力**：使用语言模型完成任务的能力
- **创新思维**：提示设计和应用创新的能力
- **跨语言意识**：多语言处理的理解和应用

### 结果性评价
- **项目实现**：完成一个NLP应用项目
- **技术报告**：撰写NLP技术分析报告
- **提示设计**：设计有效的提示词模板
- **多语言实验**：跨语言任务的实现和分析

## 🏠 课后延伸

### 基础任务
1. **模型应用**：使用预训练模型完成文本分类任务
2. **提示优化**：设计和优化特定任务的提示词
3. **多语言实验**：尝试跨语言文本处理任务

### 拓展任务
1. **创新应用**：设计一个NLP创新应用
2. **模型微调**：在特定数据集上微调语言模型
3. **性能评估**：评估和比较不同NLP模型的性能

### 预习任务
了解AI伦理和法律框架的基本概念，思考AI技术发展的社会责任。

---

*本课程旨在帮助十年级学生深入理解自然语言处理技术，培养语言AI的应用能力和创新思维。*
